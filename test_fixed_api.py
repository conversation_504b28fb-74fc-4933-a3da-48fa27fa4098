from azure_service import AzureDocumentService
import os

def test_fixed_api():
    """Test the fixed Azure Document Intelligence API"""
    
    # Initialize service
    service = AzureDocumentService()
    
    # Test with a sample file
    test_file = "./Financial Statements - Sample Files (2)/Account Capture - American Funds - Client, Value - 2024-9-12.png"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    print(f"Testing with file: {test_file}")
    
    # Test document extraction
    print("🔍 Testing document extraction...")
    result = service.extract_document_layout(test_file)
    
    if result:
        print("✅ Document extraction successful!")
        
        # Test data extraction
        print("📊 Testing data extraction...")
        extracted_data = service.extract_tables_and_text(result)
        
        print(f"Found {len(extracted_data.get('tables', []))} tables")
        print(f"Found {len(extracted_data.get('paragraphs', []))} paragraphs")
        
        # Test GPT-4o analysis
        print("🤖 Testing GPT-4o analysis...")
        analysis_result = service.analyze_with_gpt4o(extracted_data, "test_document.png")
        
        if analysis_result:
            print("✅ GPT-4o analysis successful!")
            print("Result:", analysis_result)
        else:
            print("❌ GPT-4o analysis failed")
            
    else:
        print("❌ Document extraction failed")

if __name__ == "__main__":
    test_fixed_api()
