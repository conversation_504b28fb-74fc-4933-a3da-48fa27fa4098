import asyncio
from enhanced_agents import EnhancedFinancialExtractionCrew
from models import EnhancedTableFilterCriteria
import os
import json

async def test_enhanced_features():
    """Test the enhanced document extraction system"""
    
    print("🚀 Testing Enhanced Financial Document Extraction System")
    print("=" * 60)
    
    # Initialize the enhanced crew
    crew = EnhancedFinancialExtractionCrew()
    
    # Test with a sample file
    test_file = "./Financial Statements - Sample Files (2)/Account Capture - American Funds - Client, Value - 2024-9-12.png"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    print(f"📄 Testing with file: {os.path.basename(test_file)}")
    
    # Check file size
    file_size = os.path.getsize(test_file)
    file_size_mb = file_size / (1024 * 1024)
    print(f"📊 File size: {file_size_mb:.2f} MB")
    
    # Test the enhanced processing
    print("\n🔍 Starting enhanced document processing...")
    result = await crew.process_document_enhanced(test_file, "test_document.png")
    
    print("\n📊 PROCESSING RESULTS:")
    print("=" * 40)
    
    if result.get('success'):
        print("✅ Processing successful!")
        
        # Show processing stats
        if 'processing_stats' in result:
            stats = result['processing_stats']
            print(f"\n📈 Processing Statistics:")
            print(f"  • Total tables found: {stats.get('total_tables', 0)}")
            print(f"  • Relevant tables: {stats.get('relevant_tables', 0)}")
            print(f"  • Cross-page groups: {stats.get('cross_page_groups', 0)}")
            print(f"  • Processing method: {stats.get('processing_method', 'Unknown')}")
        
        # Show extraction results
        extraction_data = result['result']
        print(f"\n💎 Extracted Data:")
        print(f"  • Document: {extraction_data.get('document_name', 'N/A')}")
        print(f"  • Client: {extraction_data.get('client_name', 'N/A')}")
        print(f"  • Advisor: {extraction_data.get('advisor_name', 'N/A')}")
        print(f"  • Portfolio ID: {extraction_data.get('portfolio_id', 'N/A')}")
        print(f"  • Total Value: {extraction_data.get('total_account_value', 'N/A')}")
        print(f"  • Holdings count: {len(extraction_data.get('tabular_data', []))}")
        
        # Show filtered tables info
        if 'filtered_tables' in result:
            print(f"\n🎯 Filtered Tables Analysis:")
            for i, table in enumerate(result['filtered_tables'][:3]):  # Show first 3
                print(f"  Table {table['table_index']}:")
                print(f"    - Relevance score: {table['relevance_score']:.2f}")
                print(f"    - Keywords: {', '.join(table['financial_context'][:5])}")  # First 5 keywords
                print(f"    - Dimensions: {table['estimated_rows']}×{table['estimated_cols']}")
                print(f"    - Cross-page: {table['is_cross_page']}")
        
        # Show cross-page detection results
        if 'cross_page_tables' in result and result['cross_page_tables']:
            print(f"\n🔗 Cross-page Tables Detected:")
            for cp_table in result['cross_page_tables']:
                print(f"  • Pages {cp_table['page_span']}: {cp_table['total_rows']} rows, {cp_table['original_tables']} source tables")
                print(f"    Confidence: {cp_table['confidence_score']:.2f}")
        else:
            print(f"\n🔗 No cross-page tables detected")
        
        # Show sample holdings
        if extraction_data.get('tabular_data'):
            print(f"\n📋 Sample Holdings:")
            for i, holding in enumerate(extraction_data['tabular_data'][:3]):  # Show first 3
                print(f"  {i+1}. {holding.get('ticker_symbol', 'N/A')} - {holding.get('shares_quantity', 'N/A')} shares")
                if holding.get('current_value'):
                    print(f"     Value: ${holding['current_value']:,.2f}")
        
        print(f"\n💾 Full result saved to 'test_result.json'")
        with open('test_result.json', 'w') as f:
            json.dump(result, f, indent=2)
    
    else:
        print("❌ Processing failed!")
        print(f"Error: {result.get('error', 'Unknown error')}")
        
        if 'partial_result' in result:
            print("\n📋 Partial results available:")
            print(json.dumps(result['partial_result'], indent=2)[:500] + "...")

def test_pydantic_filtering():
    """Test the Pydantic table filtering logic"""
    print("\n🧪 Testing Pydantic Table Filtering Logic")
    print("=" * 50)
    
    from models import EnhancedFilteredTableData, CrossPageTableInfo
    
    # Test data
    test_table_data = {
        "table_index": 0,
        "table_html": "Symbol Shares Market Value AAPL 100 $15000 MSFT 50 $12000",
        "keyword_count": 4,
        "estimated_rows": 3,
        "estimated_cols": 3,
        "is_cross_page": False
    }
    
    # Create filtered table
    filtered_table = EnhancedFilteredTableData(**test_table_data)
    
    print(f"✅ Table created successfully")
    print(f"  • Relevance score: {filtered_table.relevance_score:.2f}")
    print(f"  • Contains required keywords: {filtered_table.contains_required_keywords}")
    print(f"  • Financial context: {filtered_table.financial_context}")
    
    # Test filtering criteria
    criteria = EnhancedTableFilterCriteria(
        min_financial_keywords=2,
        enable_cross_page_detection=True
    )
    
    should_pass = filtered_table.should_pass_to_llm(criteria)
    print(f"  • Should pass to LLM: {should_pass}")
    
    # Test cross-page table
    cross_page_info = CrossPageTableInfo(
        table_type="cross_page",
        page_span="1-3",
        total_rows=25,
        total_columns=5,
        original_tables=3
    )
    
    print(f"\n🔗 Cross-page table test:")
    print(f"  • Confidence score: {cross_page_info.confidence_score:.2f}")
    print(f"  • High confidence: {cross_page_info.is_high_confidence()}")

if __name__ == "__main__":
    print("🧪 Running Enhanced System Tests")
    print("=" * 60)
    
    # Test Pydantic filtering first
    test_pydantic_filtering()
    
    # Test full system
    asyncio.run(test_enhanced_features())
