CONSTANTS G,LB,W,H
MOTIONVARIABLES' THETA'',PHI'',OMEGA',ALPHA'
NEWTONIAN N
BODIES A,B
SIMPROT(N,A,2,THETA)
SIMPROT(A,B,3,PHI)
POINT O
LA = (LB-H/2)/2
P_O_AO> = LA*A3>
P_O_BO> = LB*A3>
OMEGA = THETA'
ALPHA = PHI'
W_A_N> = OMEGA*N2>
W_B_A> = ALPHA*A3>
V_O_N> = 0>
V2PTS(N, A, O, AO)
V2PTS(N, A, O, BO)
MASS A=MA, B=MB
IAXX = 1/12*MA*(2*LA)^2
IAYY = IAXX
IAZZ = 0
IBXX = 1/12*MB*H^2
IBYY = 1/12*MB*(W^2+H^2)
IBZZ = 1/12*MB*W^2
INERTIA A, IAXX, IAYY, IAZZ
INERTIA B, IBXX, IBYY, IBZZ
GRAVITY(G*N3>)
ZERO = FR() + FRSTAR()
KANE()
INPUT LB=0.2,H=0.1,W=0.2,MA=0.01,MB=0.1,G=9.81
INPUT THETA = 90 DEG, PHI = 0.5 DEG, OMEGA=0, ALPHA=0
INPUT TFINAL=10, INTEGSTP=0.02
CODE DYNAMICS() some_filename.c
