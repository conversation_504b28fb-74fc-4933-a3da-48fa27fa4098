Metadata-Version: 2.4
Name: opentelemetry-exporter-otlp-proto-common
Version: 1.33.1
Summary: OpenTelemetry Protobuf encoding
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-otlp-proto-common
Project-URL: Repository, https://github.com/open-telemetry/opentelemetry-python
Author-email: OpenTelemetry Authors <<EMAIL>>
License: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: OpenTelemetry
Classifier: Framework :: OpenTelemetry :: Exporters
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.8
Requires-Dist: opentelemetry-proto==1.33.1
Description-Content-Type: text/x-rst

OpenTelemetry Protobuf Encoding
===============================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-otlp-proto-common.svg
   :target: https://pypi.org/project/opentelemetry-exporter-otlp-proto-common/

This library is provided as a convenience to encode to Protobuf. Currently used by:

* opentelemetry-exporter-otlp-proto-grpc
* opentelemetry-exporter-otlp-proto-http


Installation
------------

::

     pip install opentelemetry-exporter-otlp-proto-common


References
----------

* `OpenTelemetry <https://opentelemetry.io/>`_
* `OpenTelemetry Protocol Specification <https://github.com/open-telemetry/oteps/blob/main/text/0035-opentelemetry-protocol.md>`_
