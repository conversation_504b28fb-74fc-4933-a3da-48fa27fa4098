"""
    pygments.lexers.macaulay2
    ~~~~~~~~~~~~~~~~~~~~~~~~~

    Lexer for Macaulay2.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

from pygments.lexer import RegexLexer, words
from pygments.token import Comment, Keyword, Name, String, Text

__all__ = ['Macaulay2Lexer']

# Auto-generated for Macaulay2-1.24.11. Do not modify this file manually.

M2KEYWORDS = (
    "SPACE",
    "TEST",
    "and",
    "break",
    "catch",
    "continue",
    "do",
    "elapsedTime",
    "elapsedTiming",
    "else",
    "for",
    "from",
    "global",
    "if",
    "in",
    "list",
    "local",
    "new",
    "not",
    "of",
    "or",
    "return",
    "shield",
    "step",
    "symbol",
    "then",
    "threadLocal",
    "threadVariable",
    "throw",
    "time",
    "timing",
    "to",
    "try",
    "when",
    "while",
    "xor"
    )

M2DATATYPES = (
    "ANCHOR",
    "Adjacent",
    "AffineVariety",
    "Analyzer",
    "AngleBarList",
    "Array",
    "AssociativeExpression",
    "AtomicInt",
    "BLOCKQUOTE",
    "BODY",
    "BOLD",
    "BR",
    "BUTTON",
    "Bag",
    "BasicList",
    "BettiTally",
    "BinaryOperation",
    "Boolean",
    "CC",
    "CDATA",
    "CODE",
    "COMMENT",
    "CacheFunction",
    "CacheTable",
    "ChainComplex",
    "ChainComplexMap",
    "CoherentSheaf",
    "Command",
    "CompiledFunction",
    "CompiledFunctionBody",
    "CompiledFunctionClosure",
    "ComplexField",
    "Constant",
    "DD",
    "DIV",
    "DL",
    "DT",
    "Database",
    "Descent",
    "Describe",
    "Dictionary",
    "DirectSum",
    "Divide",
    "DocumentTag",
    "EM",
    "Eliminate",
    "EngineRing",
    "Equation",
    "ExampleItem",
    "Expression",
    "File",
    "FilePosition",
    "FractionField",
    "Function",
    "FunctionApplication",
    "FunctionBody",
    "FunctionClosure",
    "GaloisField",
    "GeneralOrderedMonoid",
    "GlobalDictionary",
    "GradedModule",
    "GradedModuleMap",
    "GroebnerBasis",
    "GroebnerBasisOptions",
    "HEAD",
    "HEADER1",
    "HEADER2",
    "HEADER3",
    "HEADER4",
    "HEADER5",
    "HEADER6",
    "HR",
    "HREF",
    "HTML",
    "HashTable",
    "HeaderType",
    "Holder",
    "Hybrid",
    "Hypertext",
    "HypertextContainer",
    "HypertextParagraph",
    "HypertextVoid",
    "IMG",
    "INDENT",
    "INPUT",
    "ITALIC",
    "Ideal",
    "ImmutableType",
    "IndeterminateNumber",
    "IndexedVariable",
    "IndexedVariableTable",
    "InexactField",
    "InexactFieldFamily",
    "InexactNumber",
    "InfiniteNumber",
    "IntermediateMarkUpType",
    "Iterator",
    "KBD",
    "Keyword",
    "LABEL",
    "LATER",
    "LI",
    "LINK",
    "LITERAL",
    "List",
    "LocalDictionary",
    "LowerBound",
    "MENU",
    "META",
    "Manipulator",
    "MapExpression",
    "MarkUpType",
    "Matrix",
    "MatrixExpression",
    "MethodFunction",
    "MethodFunctionBinary",
    "MethodFunctionSingle",
    "MethodFunctionWithOptions",
    "Minus",
    "Module",
    "Monoid",
    "MonoidElement",
    "MonomialIdeal",
    "MultigradedBettiTally",
    "MutableHashTable",
    "MutableList",
    "MutableMatrix",
    "Net",
    "NetFile",
    "Nothing",
    "Number",
    "NumberedVerticalList",
    "OL",
    "OneExpression",
    "Option",
    "OptionTable",
    "OrderedMonoid",
    "PARA",
    "PRE",
    "Package",
    "Parenthesize",
    "Parser",
    "Partition",
    "PolynomialRing",
    "Power",
    "Product",
    "ProductOrder",
    "Program",
    "ProgramRun",
    "ProjectiveHilbertPolynomial",
    "ProjectiveVariety",
    "Pseudocode",
    "PseudocodeClosure",
    "QQ",
    "QuotientRing",
    "RR",
    "RRi",
    "RealField",
    "Resolution",
    "Ring",
    "RingElement",
    "RingFamily",
    "RingMap",
    "RowExpression",
    "SAMP",
    "SCRIPT",
    "SMALL",
    "SPAN",
    "STRONG",
    "STYLE",
    "SUB",
    "SUBSECTION",
    "SUP",
    "ScriptedFunctor",
    "SelfInitializingType",
    "Sequence",
    "Set",
    "SheafExpression",
    "SheafMap",
    "SheafOfRings",
    "SparseMonomialVectorExpression",
    "SparseVectorExpression",
    "String",
    "Subscript",
    "Sum",
    "SumOfTwists",
    "Superscript",
    "Symbol",
    "SymbolBody",
    "TABLE",
    "TD",
    "TEX",
    "TH",
    "TITLE",
    "TO",
    "TO2",
    "TOH",
    "TR",
    "TT",
    "Table",
    "Tally",
    "Task",
    "TensorProduct",
    "TestInput",
    "Thing",
    "Time",
    "Type",
    "UL",
    "URL",
    "VAR",
    "Variety",
    "Vector",
    "VectorExpression",
    "VerticalList",
    "VirtualTally",
    "VisibleList",
    "WrapperType",
    "ZZ",
    "ZeroExpression"
    )

M2FUNCTIONS = (
    "BesselJ",
    "BesselY",
    "Beta",
    "Digamma",
    "EXAMPLE",
    "End",
    "Fano",
    "GCstats",
    "GF",
    "Gamma",
    "Grassmannian",
    "Hom",
    "LLL",
    "LUdecomposition",
    "M2CODE",
    "NNParser",
    "Proj",
    "QQParser",
    "QRDecomposition",
    "SVD",
    "SYNOPSIS",
    "Schubert",
    "Spec",
    "ZZParser",
    "about",
    "abs",
    "accumulate",
    "acos",
    "acosh",
    "acot",
    "acoth",
    "addCancelTask",
    "addDependencyTask",
    "addEndFunction",
    "addHook",
    "addStartTask",
    "adjoint",
    "agm",
    "alarm",
    "all",
    "ambient",
    "analyticSpread",
    "ancestor",
    "ancestors",
    "andP",
    "ann",
    "annihilator",
    "antipode",
    "any",
    "append",
    "applicationDirectory",
    "apply",
    "applyKeys",
    "applyPairs",
    "applyTable",
    "applyValues",
    "apropos",
    "arXiv",
    "ascii",
    "asin",
    "asinh",
    "ass",
    "assert",
    "associatedGradedRing",
    "associatedPrimes",
    "atEndOfFile",
    "atan",
    "atan2",
    "atanh",
    "autoload",
    "baseFilename",
    "baseName",
    "baseRing",
    "basis",
    "beginDocumentation",
    "benchmark",
    "betti",
    "between",
    "binomial",
    "borel",
    "cacheValue",
    "cancelTask",
    "canonicalBundle",
    "capture",
    "ceiling",
    "centerString",
    "chainComplex",
    "changeBase",
    "changeDirectory",
    "char",
    "charAnalyzer",
    "characters",
    "check",
    "checkDegrees",
    "chi",
    "class",
    "clean",
    "clearEcho",
    "code",
    "codim",
    "coefficient",
    "coefficientRing",
    "coefficients",
    "cohomology",
    "coimage",
    "coker",
    "cokernel",
    "collectGarbage",
    "columnAdd",
    "columnMult",
    "columnPermute",
    "columnRankProfile",
    "columnSwap",
    "columnate",
    "combine",
    "commandInterpreter",
    "commonRing",
    "commonest",
    "comodule",
    "compareExchange",
    "complement",
    "complete",
    "components",
    "compose",
    "compositions",
    "compress",
    "concatenate",
    "conductor",
    "cone",
    "conjugate",
    "connectionCount",
    "constParser",
    "content",
    "contract",
    "conwayPolynomial",
    "copy",
    "copyDirectory",
    "copyFile",
    "cos",
    "cosh",
    "cot",
    "cotangentSheaf",
    "coth",
    "cover",
    "coverMap",
    "cpuTime",
    "createTask",
    "csc",
    "csch",
    "currentColumnNumber",
    "currentDirectory",
    "currentPosition",
    "currentRowNumber",
    "currentTime",
    "deadParser",
    "debug",
    "debugError",
    "decompose",
    "deepSplice",
    "default",
    "degree",
    "degreeGroup",
    "degreeLength",
    "degrees",
    "degreesMonoid",
    "degreesRing",
    "delete",
    "demark",
    "denominator",
    "depth",
    "describe",
    "det",
    "determinant",
    "diagonalMatrix",
    "diameter",
    "dictionary",
    "diff",
    "difference",
    "dim",
    "directSum",
    "disassemble",
    "discriminant",
    "dismiss",
    "distinguished",
    "divideByVariable",
    "doc",
    "document",
    "drop",
    "dual",
    "eagonNorthcott",
    "echoOff",
    "echoOn",
    "eigenvalues",
    "eigenvectors",
    "eint",
    "elements",
    "eliminate",
    "endPackage",
    "entries",
    "erase",
    "erf",
    "erfc",
    "error",
    "euler",
    "eulers",
    "even",
    "examples",
    "exchange",
    "exec",
    "exp",
    "expectedReesIdeal",
    "expm1",
    "exponents",
    "export",
    "exportFrom",
    "exportMutable",
    "expression",
    "extend",
    "exteriorPower",
    "factor",
    "fileExecutable",
    "fileExists",
    "fileLength",
    "fileMode",
    "fileReadable",
    "fileTime",
    "fileWritable",
    "fillMatrix",
    "findFiles",
    "findHeft",
    "findProgram",
    "findSynonyms",
    "first",
    "firstkey",
    "fittingIdeal",
    "flagLookup",
    "flatten",
    "flattenRing",
    "flip",
    "floor",
    "fold",
    "forceGB",
    "fork",
    "format",
    "formation",
    "frac",
    "fraction",
    "frames",
    "fromDividedPowers",
    "fromDual",
    "functionBody",
    "futureParser",
    "gb",
    "gbRemove",
    "gbSnapshot",
    "gcd",
    "gcdCoefficients",
    "gcdLLL",
    "genera",
    "generateAssertions",
    "generator",
    "generators",
    "genericMatrix",
    "genericSkewMatrix",
    "genericSymmetricMatrix",
    "gens",
    "genus",
    "get",
    "getChangeMatrix",
    "getGlobalSymbol",
    "getIOThreadMode",
    "getNetFile",
    "getNonUnit",
    "getPrimeWithRootOfUnity",
    "getSymbol",
    "getWWW",
    "getc",
    "getenv",
    "globalAssign",
    "globalAssignFunction",
    "globalAssignment",
    "globalReleaseFunction",
    "gradedModule",
    "gradedModuleMap",
    "gramm",
    "graphIdeal",
    "graphRing",
    "groebnerBasis",
    "groupID",
    "hash",
    "hashTable",
    "headlines",
    "heft",
    "height",
    "hermite",
    "hilbertFunction",
    "hilbertPolynomial",
    "hilbertSeries",
    "hold",
    "homogenize",
    "homology",
    "homomorphism",
    "hooks",
    "horizontalJoin",
    "html",
    "httpHeaders",
    "hypertext",
    "icFracP",
    "icFractions",
    "icMap",
    "icPIdeal",
    "ideal",
    "idealizer",
    "identity",
    "image",
    "imaginaryPart",
    "importFrom",
    "independentSets",
    "index",
    "indices",
    "inducedMap",
    "inducesWellDefinedMap",
    "info",
    "input",
    "insert",
    "installAssignmentMethod",
    "installHilbertFunction",
    "installMethod",
    "installMinprimes",
    "installPackage",
    "installedPackages",
    "instance",
    "instances",
    "integralClosure",
    "integrate",
    "intersect",
    "intersectInP",
    "intersection",
    "interval",
    "inverse",
    "inverseErf",
    "inversePermutation",
    "inverseRegularizedBeta",
    "inverseRegularizedGamma",
    "inverseSystem",
    "irreducibleCharacteristicSeries",
    "irreducibleDecomposition",
    "isANumber",
    "isAffineRing",
    "isBorel",
    "isCanceled",
    "isCommutative",
    "isConstant",
    "isDirectSum",
    "isDirectory",
    "isEmpty",
    "isField",
    "isFinite",
    "isFinitePrimeField",
    "isFreeModule",
    "isGlobalSymbol",
    "isHomogeneous",
    "isIdeal",
    "isInfinite",
    "isInjective",
    "isInputFile",
    "isIsomorphic",
    "isIsomorphism",
    "isLLL",
    "isLiftable",
    "isLinearType",
    "isListener",
    "isMember",
    "isModule",
    "isMonomialIdeal",
    "isMutable",
    "isNormal",
    "isOpen",
    "isOutputFile",
    "isPolynomialRing",
    "isPrimary",
    "isPrime",
    "isPrimitive",
    "isProjective",
    "isPseudoprime",
    "isQuotientModule",
    "isQuotientOf",
    "isQuotientRing",
    "isReady",
    "isReal",
    "isReduction",
    "isRegularFile",
    "isRing",
    "isSkewCommutative",
    "isSmooth",
    "isSorted",
    "isSquareFree",
    "isStandardGradedPolynomialRing",
    "isSubmodule",
    "isSubquotient",
    "isSubset",
    "isSupportedInZeroLocus",
    "isSurjective",
    "isTable",
    "isUnit",
    "isVeryAmple",
    "isWellDefined",
    "isWeylAlgebra",
    "isc",
    "iterator",
    "jacobian",
    "jacobianDual",
    "join",
    "ker",
    "kernel",
    "kernelLLL",
    "kernelOfLocalization",
    "keys",
    "kill",
    "koszul",
    "last",
    "lcm",
    "leadCoefficient",
    "leadComponent",
    "leadMonomial",
    "leadTerm",
    "left",
    "length",
    "letterParser",
    "lift",
    "liftable",
    "limitFiles",
    "limitProcesses",
    "lines",
    "linkFile",
    "listForm",
    "listSymbols",
    "lngamma",
    "load",
    "loadPackage",
    "localDictionaries",
    "localize",
    "locate",
    "log",
    "log1p",
    "lookup",
    "lookupCount",
    "makeDirectory",
    "makeDocumentTag",
    "makePackageIndex",
    "makeS2",
    "map",
    "markedGB",
    "match",
    "mathML",
    "matrix",
    "max",
    "maxPosition",
    "member",
    "memoize",
    "memoizeClear",
    "memoizeValues",
    "merge",
    "mergePairs",
    "method",
    "methodOptions",
    "methods",
    "midpoint",
    "min",
    "minPosition",
    "minPres",
    "mingens",
    "mingle",
    "minimalBetti",
    "minimalPresentation",
    "minimalPrimes",
    "minimalReduction",
    "minimize",
    "minimizeFilename",
    "minors",
    "minprimes",
    "minus",
    "mkdir",
    "mod",
    "module",
    "modulo",
    "monoid",
    "monomialCurveIdeal",
    "monomialIdeal",
    "monomialSubideal",
    "monomials",
    "moveFile",
    "multidegree",
    "multidoc",
    "multigraded",
    "multiplicity",
    "mutable",
    "mutableIdentity",
    "mutableMatrix",
    "nanosleep",
    "needs",
    "needsPackage",
    "net",
    "netList",
    "newClass",
    "newCoordinateSystem",
    "newNetFile",
    "newPackage",
    "newRing",
    "next",
    "nextPrime",
    "nextkey",
    "nonspaceAnalyzer",
    "norm",
    "normalCone",
    "notImplemented",
    "nullParser",
    "nullSpace",
    "nullhomotopy",
    "numColumns",
    "numRows",
    "number",
    "numcols",
    "numerator",
    "numeric",
    "numericInterval",
    "numgens",
    "numrows",
    "odd",
    "oeis",
    "ofClass",
    "on",
    "openDatabase",
    "openDatabaseOut",
    "openFiles",
    "openIn",
    "openInOut",
    "openListener",
    "openOut",
    "openOutAppend",
    "optP",
    "optionalSignParser",
    "options",
    "orP",
    "override",
    "pack",
    "package",
    "packageTemplate",
    "pad",
    "pager",
    "pairs",
    "parallelApply",
    "parent",
    "part",
    "partition",
    "partitions",
    "parts",
    "pdim",
    "peek",
    "permanents",
    "permutations",
    "pfaffians",
    "pivots",
    "plus",
    "poincare",
    "poincareN",
    "polarize",
    "poly",
    "position",
    "positions",
    "power",
    "powermod",
    "precision",
    "preimage",
    "prepend",
    "presentation",
    "pretty",
    "primaryComponent",
    "primaryDecomposition",
    "print",
    "printString",
    "printerr",
    "processID",
    "product",
    "profile",
    "projectiveHilbertPolynomial",
    "promote",
    "protect",
    "prune",
    "pseudoRemainder",
    "pseudocode",
    "pullback",
    "pushForward",
    "pushout",
    "quotient",
    "quotientRemainder",
    "radical",
    "radicalContainment",
    "random",
    "randomKRationalPoint",
    "randomMutableMatrix",
    "rank",
    "rays",
    "read",
    "readDirectory",
    "readPackage",
    "readlink",
    "realPart",
    "realpath",
    "recursionDepth",
    "reduceHilbert",
    "reducedRowEchelonForm",
    "reductionNumber",
    "reesAlgebra",
    "reesAlgebraIdeal",
    "reesIdeal",
    "regSeqInIdeal",
    "regex",
    "regexQuote",
    "registerFinalizer",
    "regularity",
    "regularizedBeta",
    "regularizedGamma",
    "relations",
    "relativizeFilename",
    "remainder",
    "remove",
    "removeDirectory",
    "removeFile",
    "removeLowestDimension",
    "reorganize",
    "replace",
    "res",
    "reshape",
    "resolution",
    "resultant",
    "reverse",
    "right",
    "ring",
    "ringFromFractions",
    "roots",
    "rotate",
    "round",
    "rowAdd",
    "rowMult",
    "rowPermute",
    "rowRankProfile",
    "rowSwap",
    "rsort",
    "run",
    "runHooks",
    "runLengthEncode",
    "runProgram",
    "same",
    "saturate",
    "scan",
    "scanKeys",
    "scanLines",
    "scanPairs",
    "scanValues",
    "schedule",
    "schreyerOrder",
    "searchPath",
    "sec",
    "sech",
    "seeParsing",
    "select",
    "selectInSubring",
    "selectKeys",
    "selectPairs",
    "selectValues",
    "selectVariables",
    "separate",
    "separateRegexp",
    "sequence",
    "serialNumber",
    "set",
    "setEcho",
    "setGroupID",
    "setIOExclusive",
    "setIOSynchronized",
    "setIOUnSynchronized",
    "setRandomSeed",
    "setup",
    "setupEmacs",
    "setupLift",
    "setupPromote",
    "sheaf",
    "sheafHom",
    "sheafMap",
    "show",
    "showHtml",
    "showTex",
    "simpleDocFrob",
    "sin",
    "singularLocus",
    "sinh",
    "size",
    "size2",
    "sleep",
    "smithNormalForm",
    "solve",
    "someTerms",
    "sort",
    "sortColumns",
    "source",
    "span",
    "specialFiber",
    "specialFiberIdeal",
    "splice",
    "splitWWW",
    "sqrt",
    "stack",
    "stacksProject",
    "standardForm",
    "standardPairs",
    "stashValue",
    "status",
    "store",
    "style",
    "sub",
    "sublists",
    "submatrix",
    "submatrixByDegrees",
    "subquotient",
    "subsets",
    "substitute",
    "substring",
    "subtable",
    "sum",
    "super",
    "support",
    "switch",
    "sylvesterMatrix",
    "symbolBody",
    "symlinkDirectory",
    "symlinkFile",
    "symmetricAlgebra",
    "symmetricAlgebraIdeal",
    "symmetricKernel",
    "symmetricPower",
    "synonym",
    "syz",
    "syzygyScheme",
    "table",
    "take",
    "tally",
    "tan",
    "tangentCone",
    "tangentSheaf",
    "tanh",
    "target",
    "taskResult",
    "temporaryFileName",
    "tensor",
    "tensorAssociativity",
    "terminalParser",
    "terms",
    "testHunekeQuestion",
    "tests",
    "tex",
    "texMath",
    "times",
    "toAbsolutePath",
    "toCC",
    "toDividedPowers",
    "toDual",
    "toExternalString",
    "toField",
    "toList",
    "toLower",
    "toRR",
    "toRRi",
    "toSequence",
    "toString",
    "toUpper",
    "top",
    "topCoefficients",
    "topComponents",
    "trace",
    "transpose",
    "trim",
    "truncate",
    "truncateOutput",
    "tutorial",
    "ultimate",
    "unbag",
    "uncurry",
    "undocumented",
    "uniform",
    "uninstallAllPackages",
    "uninstallPackage",
    "union",
    "unique",
    "uniquePermutations",
    "unsequence",
    "unstack",
    "urlEncode",
    "use",
    "userSymbols",
    "utf8",
    "utf8check",
    "utf8substring",
    "validate",
    "value",
    "values",
    "variety",
    "vars",
    "vector",
    "versalEmbedding",
    "wait",
    "wedgeProduct",
    "weightRange",
    "whichGm",
    "width",
    "wikipedia",
    "wrap",
    "youngest",
    "zero",
    "zeta"
    )

M2CONSTANTS = (
    "A1BrouwerDegrees",
    "AbstractSimplicialComplexes",
    "AbstractToricVarieties",
    "Acknowledgement",
    "AdditionalPaths",
    "AdjointIdeal",
    "AdjunctionForSurfaces",
    "AfterEval",
    "AfterNoPrint",
    "AfterPrint",
    "AInfinity",
    "AlgebraicSplines",
    "Algorithm",
    "Alignment",
    "AllCodimensions",
    "allowableThreads",
    "AnalyzeSheafOnP1",
    "applicationDirectorySuffix",
    "argument",
    "Ascending",
    "AssociativeAlgebras",
    "Authors",
    "AuxiliaryFiles",
    "backtrace",
    "Bareiss",
    "Base",
    "BaseFunction",
    "baseRings",
    "BaseRow",
    "BasisElementLimit",
    "Bayer",
    "BeforePrint",
    "BeginningMacaulay2",
    "Benchmark",
    "BernsteinSato",
    "Bertini",
    "BettiCharacters",
    "BGG",
    "BIBasis",
    "Binary",
    "Binomial",
    "BinomialEdgeIdeals",
    "Binomials",
    "BKZ",
    "blockMatrixForm",
    "Body",
    "BoijSoederberg",
    "Book3264Examples",
    "BooleanGB",
    "Boxes",
    "Browse",
    "Bruns",
    "cache",
    "CacheExampleOutput",
    "CallLimit",
    "CannedExample",
    "CatalanConstant",
    "Caveat",
    "CellularResolutions",
    "Center",
    "Certification",
    "ChainComplexExtras",
    "ChainComplexOperations",
    "ChangeMatrix",
    "CharacteristicClasses",
    "CheckDocumentation",
    "Chordal",
    "cite",
    "Classic",
    "clearAll",
    "clearOutput",
    "close",
    "closeIn",
    "closeOut",
    "ClosestFit",
    "Code",
    "CodimensionLimit",
    "CodingTheory",
    "CoefficientRing",
    "Cofactor",
    "CohenEngine",
    "CohenTopLevel",
    "CohomCalg",
    "CoincidentRootLoci",
    "commandLine",
    "compactMatrixForm",
    "Complement",
    "CompleteIntersection",
    "CompleteIntersectionResolutions",
    "Complexes",
    "ConductorElement",
    "Configuration",
    "ConformalBlocks",
    "Consequences",
    "Constants",
    "Contributors",
    "ConvexInterface",
    "ConwayPolynomials",
    "copyright",
    "Core",
    "CorrespondenceScrolls",
    "CotangentSchubert",
    "Cremona",
    "currentFileDirectory",
    "currentFileName",
    "currentLayout",
    "currentPackage",
    "Cyclotomic",
    "Date",
    "dd",
    "DebuggingMode",
    "debuggingMode",
    "debugLevel",
    "DecomposableSparseSystems",
    "Decompose",
    "Default",
    "defaultPrecision",
    "Degree",
    "DegreeGroup",
    "DegreeLift",
    "DegreeLimit",
    "DegreeMap",
    "DegreeOrder",
    "DegreeRank",
    "Degrees",
    "Dense",
    "Density",
    "Depth",
    "Descending",
    "Description",
    "DeterminantalRepresentations",
    "DGAlgebras",
    "dictionaryPath",
    "DiffAlg",
    "Dispatch",
    "DivideConquer",
    "DividedPowers",
    "Divisor",
    "Dmodules",
    "docExample",
    "docTemplate",
    "Down",
    "Dynamic",
    "EagonResolution",
    "EdgeIdeals",
    "edit",
    "EigenSolver",
    "EisenbudHunekeVasconcelos",
    "Elimination",
    "EliminationMatrices",
    "EllipticCurves",
    "EllipticIntegrals",
    "Email",
    "end",
    "endl",
    "Engine",
    "engineDebugLevel",
    "EngineTests",
    "EnumerationCurves",
    "environment",
    "EquivariantGB",
    "errorDepth",
    "EulerConstant",
    "Example",
    "ExampleFiles",
    "ExampleSystems",
    "Exclude",
    "exit",
    "Ext",
    "ExteriorIdeals",
    "ExteriorModules",
    "false",
    "FastMinors",
    "FastNonminimal",
    "FGLM",
    "fileDictionaries",
    "fileExitHooks",
    "FileName",
    "FindOne",
    "FiniteFittingIdeals",
    "First",
    "FirstPackage",
    "FlatMonoid",
    "Flexible",
    "flush",
    "FollowLinks",
    "ForeignFunctions",
    "FormalGroupLaws",
    "Format",
    "FourierMotzkin",
    "FourTiTwo",
    "fpLLL",
    "FrobeniusThresholds",
    "FunctionFieldDesingularization",
    "GBDegrees",
    "gbTrace",
    "GenerateAssertions",
    "Generic",
    "GenericInitialIdeal",
    "GeometricDecomposability",
    "gfanInterface",
    "Givens",
    "GKMVarieties",
    "GLex",
    "Global",
    "GlobalAssignHook",
    "globalAssignmentHooks",
    "GlobalHookStore",
    "GlobalReleaseHook",
    "GlobalSectionLimit",
    "Gorenstein",
    "GradedLieAlgebras",
    "GraphicalModels",
    "GraphicalModelsMLE",
    "Graphics",
    "Graphs",
    "GRevLex",
    "GroebnerStrata",
    "GroebnerWalk",
    "GroupLex",
    "GroupRevLex",
    "GTZ",
    "Hadamard",
    "handleInterrupts",
    "HardDegreeLimit",
    "Heading",
    "Headline",
    "Heft",
    "Height",
    "help",
    "Hermite",
    "Hermitian",
    "HH",
    "hh",
    "HigherCIOperators",
    "HighestWeights",
    "Hilbert",
    "HodgeIntegrals",
    "HolonomicSystems",
    "homeDirectory",
    "HomePage",
    "Homogeneous",
    "Homogeneous2",
    "HomotopyLieAlgebra",
    "HorizontalSpace",
    "HyperplaneArrangements",
    "id",
    "IgnoreExampleErrors",
    "ii",
    "incomparable",
    "Increment",
    "indeterminate",
    "Index",
    "indexComponents",
    "infinity",
    "InfoDirSection",
    "infoHelp",
    "Inhomogeneous",
    "Inputs",
    "InstallPrefix",
    "IntegralClosure",
    "interpreterDepth",
    "Intersection",
    "InvariantRing",
    "InverseMethod",
    "Inverses",
    "InverseSystems",
    "Invertible",
    "InvolutiveBases",
    "Isomorphism",
    "Item",
    "Iterate",
    "Jacobian",
    "Jets",
    "Join",
    "JSON",
    "Jupyter",
    "K3Carpets",
    "K3Surfaces",
    "Keep",
    "KeepFiles",
    "KeepZeroes",
    "Key",
    "Keywords",
    "Kronecker",
    "KustinMiller",
    "lastMatch",
    "LatticePolytopes",
    "Layout",
    "Left",
    "LengthLimit",
    "Lex",
    "LexIdeals",
    "Licenses",
    "LieTypes",
    "Limit",
    "Linear",
    "LinearAlgebra",
    "LinearTruncations",
    "lineNumber",
    "listLocalSymbols",
    "listUserSymbols",
    "LLLBases",
    "loadDepth",
    "LoadDocumentation",
    "loadedFiles",
    "loadedPackages",
    "Local",
    "LocalRings",
    "LongPolynomial",
    "M0nbar",
    "Macaulay2Doc",
    "Maintainer",
    "MakeDocumentation",
    "MakeHTML",
    "MakeInfo",
    "MakeLinks",
    "MakePDF",
    "MapleInterface",
    "Markov",
    "MatchingFields",
    "MatrixSchubert",
    "Matroids",
    "maxAllowableThreads",
    "maxExponent",
    "MaximalRank",
    "MaxReductionCount",
    "MCMApproximations",
    "MergeTeX",
    "minExponent",
    "MinimalGenerators",
    "MinimalMatrix",
    "minimalPresentationMap",
    "minimalPresentationMapInv",
    "MinimalPrimes",
    "Minimize",
    "MinimumVersion",
    "Miura",
    "MixedMultiplicity",
    "ModuleDeformations",
    "MonodromySolver",
    "Monomial",
    "MonomialAlgebras",
    "MonomialIntegerPrograms",
    "MonomialOrbits",
    "MonomialOrder",
    "Monomials",
    "MonomialSize",
    "Msolve",
    "MultigradedBGG",
    "MultigradedImplicitization",
    "MultiGradedRationalMap",
    "MultiplicitySequence",
    "MultiplierIdeals",
    "MultiplierIdealsDim2",
    "MultiprojectiveVarieties",
    "NAGtypes",
    "Name",
    "Nauty",
    "NautyGraphs",
    "NCAlgebra",
    "NCLex",
    "NewFromMethod",
    "newline",
    "NewMethod",
    "NewOfFromMethod",
    "NewOfMethod",
    "nil",
    "Node",
    "NoetherianOperators",
    "NoetherNormalization",
    "NonminimalComplexes",
    "NoPrint",
    "Normaliz",
    "NormalToricVarieties",
    "notify",
    "NTL",
    "null",
    "nullaryMethods",
    "NumericalAlgebraicGeometry",
    "NumericalCertification",
    "NumericalImplicitization",
    "NumericalLinearAlgebra",
    "NumericalSchubertCalculus",
    "NumericalSemigroups",
    "NumericSolutions",
    "numTBBThreads",
    "OIGroebnerBases",
    "OldPolyhedra",
    "OldToricVectorBundles",
    "OnlineLookup",
    "OO",
    "oo",
    "ooo",
    "oooo",
    "OpenMath",
    "operatorAttributes",
    "OptionalComponentsPresent",
    "Options",
    "Order",
    "order",
    "OutputDictionary",
    "Outputs",
    "PackageCitations",
    "PackageDictionary",
    "PackageExports",
    "PackageImports",
    "PackageTemplate",
    "PairLimit",
    "PairsRemaining",
    "ParallelF4",
    "ParallelizeByDegree",
    "Parametrization",
    "Parsing",
    "path",
    "PencilsOfQuadrics",
    "Permanents",
    "Permutations",
    "PHCpack",
    "PhylogeneticTrees",
    "pi",
    "PieriMaps",
    "PlaneCurveLinearSeries",
    "PlaneCurveSingularities",
    "Points",
    "Polyhedra",
    "Polymake",
    "PolyominoIdeals",
    "Posets",
    "Position",
    "PositivityToricBundles",
    "POSIX",
    "Postfix",
    "Pre",
    "Precision",
    "Prefix",
    "prefixDirectory",
    "prefixPath",
    "PrimaryDecomposition",
    "PrimaryTag",
    "PrimitiveElement",
    "Print",
    "printingAccuracy",
    "printingLeadLimit",
    "printingPrecision",
    "printingSeparator",
    "printingTimeLimit",
    "printingTrailLimit",
    "printWidth",
    "Probability",
    "profileSummary",
    "programPaths",
    "Projective",
    "Prune",
    "PruneComplex",
    "pruningMap",
    "PseudomonomialPrimaryDecomposition",
    "Pullback",
    "pullbackMaps",
    "PushForward",
    "pushoutMaps",
    "Python",
    "QthPower",
    "QuadraticIdealExamplesByRoos",
    "Quasidegrees",
    "QuaternaryQuartics",
    "QuillenSuslin",
    "quit",
    "Quotient",
    "Radical",
    "RadicalCodim1",
    "RaiseError",
    "RandomCanonicalCurves",
    "RandomComplexes",
    "RandomCurves",
    "RandomCurvesOverVerySmallFiniteFields",
    "RandomGenus14Curves",
    "RandomIdeals",
    "RandomMonomialIdeals",
    "RandomObjects",
    "RandomPlaneCurves",
    "RandomPoints",
    "RandomSpaceCurves",
    "Range",
    "RationalMaps",
    "RationalPoints",
    "RationalPoints2",
    "ReactionNetworks",
    "RealFP",
    "RealQP",
    "RealQP1",
    "RealRoots",
    "RealRR",
    "RealXD",
    "recursionLimit",
    "Reduce",
    "ReesAlgebra",
    "References",
    "ReflexivePolytopesDB",
    "Regularity",
    "RelativeCanonicalResolution",
    "Reload",
    "RemakeAllDocumentation",
    "RerunExamples",
    "ResidualIntersections",
    "ResLengthThree",
    "ResolutionsOfStanleyReisnerRings",
    "restart",
    "Result",
    "Resultants",
    "returnCode",
    "Reverse",
    "RevLex",
    "Right",
    "RInterface",
    "rootPath",
    "rootURI",
    "RunDirectory",
    "RunExamples",
    "RunExternalM2",
    "SagbiGbDetection",
    "Saturation",
    "SaturationMap",
    "Schubert2",
    "SchurComplexes",
    "SchurFunctors",
    "SchurRings",
    "SchurVeronese",
    "SCMAlgebras",
    "scriptCommandLine",
    "SCSCP",
    "SectionRing",
    "SeeAlso",
    "SegreClasses",
    "SemidefiniteProgramming",
    "Seminormalization",
    "SeparateExec",
    "Serialization",
    "sheafExt",
    "ShimoyamaYokoyama",
    "showClassStructure",
    "showStructure",
    "showUserStructure",
    "SimpleDoc",
    "SimplicialComplexes",
    "SimplicialDecomposability",
    "SimplicialPosets",
    "SimplifyFractions",
    "SizeLimit",
    "SkewCommutative",
    "SlackIdeals",
    "SLnEquivariantMatrices",
    "SLPexpressions",
    "Sort",
    "SortStrategy",
    "SourceCode",
    "SourceRing",
    "SpaceCurves",
    "SparseResultants",
    "SpechtModule",
    "SpecialFanoFourfolds",
    "SpectralSequences",
    "SRdeformations",
    "Standard",
    "StartWithOneMinor",
    "StatePolytope",
    "StatGraphs",
    "stderr",
    "stdio",
    "StopBeforeComputation",
    "stopIfError",
    "StopIteration",
    "StopWithMinimalGenerators",
    "Strategy",
    "Strict",
    "StronglyStableIdeals",
    "Style",
    "SubalgebraBases",
    "Subnodes",
    "SubringLimit",
    "subscript",
    "Sugarless",
    "SumsOfSquares",
    "SuperLinearAlgebra",
    "superscript",
    "SVDComplexes",
    "SwitchingFields",
    "SymbolicPowers",
    "SymmetricPolynomials",
    "Synopsis",
    "Syzygies",
    "SyzygyLimit",
    "SyzygyMatrix",
    "SyzygyRows",
    "TangentCone",
    "TateOnProducts",
    "TensorComplexes",
    "TerraciniLoci",
    "Test",
    "testExample",
    "TestIdeals",
    "TeXmacs",
    "Text",
    "ThinSincereQuivers",
    "ThreadedGB",
    "Threads",
    "Threshold",
    "Topcom",
    "topLevelMode",
    "Tor",
    "TorAlgebra",
    "Toric",
    "ToricInvariants",
    "ToricTopology",
    "ToricVectorBundles",
    "Torsion",
    "TorsionFree",
    "TotalPairs",
    "Tree",
    "TriangularSets",
    "Triangulations",
    "Tries",
    "Trim",
    "Triplets",
    "Tropical",
    "TropicalToric",
    "true",
    "Truncate",
    "Truncations",
    "TSpreadIdeals",
    "TypicalValue",
    "typicalValues",
    "Undo",
    "Unique",
    "Units",
    "Unmixed",
    "Up",
    "UpdateOnly",
    "UpperTriangular",
    "Usage",
    "UseCachedExampleOutput",
    "UseHilbertFunction",
    "UserMode",
    "UseSyzygies",
    "Valuations",
    "Variable",
    "VariableBaseName",
    "Variables",
    "Varieties",
    "Vasconcelos",
    "VectorFields",
    "VectorGraphics",
    "Verbose",
    "Verbosity",
    "Verify",
    "VersalDeformations",
    "Version",
    "version",
    "VerticalSpace",
    "viewHelp",
    "VirtualResolutions",
    "Visualize",
    "VNumber",
    "WebApp",
    "Weights",
    "WeylAlgebra",
    "WeylAlgebras",
    "WeylGroups",
    "WhitneyStratifications",
    "Wrap",
    "XML"
    )

class Macaulay2Lexer(RegexLexer):
    """Lexer for Macaulay2, a software system for research in algebraic geometry."""

    name = 'Macaulay2'
    url = 'https://macaulay2.com/'
    aliases = ['macaulay2']
    filenames = ['*.m2']
    version_added = '2.12'

    tokens = {
        'root': [
            (r'--.*$', Comment.Single),
            (r'-\*', Comment.Multiline, 'block comment'),
            (r'"', String, 'quote string'),
            (r'///', String, 'slash string'),
            (words(M2KEYWORDS, prefix=r'\b', suffix=r'\b'), Keyword),
            (words(M2DATATYPES, prefix=r'\b', suffix=r'\b'), Name.Builtin),
            (words(M2FUNCTIONS, prefix=r'\b', suffix=r'\b'), Name.Function),
            (words(M2CONSTANTS, prefix=r'\b', suffix=r'\b'), Name.Constant),
            (r'\s+', Text.Whitespace),
            (r'.', Text)
        ],
        'block comment' : [
            (r'[^*-]+', Comment.Multiline),
            (r'\*-', Comment.Multiline, '#pop'),
            (r'[*-]', Comment.Multiline)
        ],
        'quote string' : [
            (r'[^\\"]+', String),
            (r'"', String, '#pop'),
            (r'\\"?', String),
        ],
        'slash string' : [
            (r'[^/]+', String),
            (r'(//)+(?!/)', String),
            (r'/(//)+(?!/)', String, '#pop'),
            (r'/', String)
        ]
    }
