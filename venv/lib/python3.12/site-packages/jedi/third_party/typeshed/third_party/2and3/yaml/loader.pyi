from yaml.composer import Composer
from yaml.constructor import <PERSON><PERSON><PERSON><PERSON><PERSON>, Con<PERSON><PERSON>ctor, FullConstructor, SafeConstructor
from yaml.parser import Parser
from yaml.reader import Reader
from yaml.resolver import BaseResolver, Resolver
from yaml.scanner import Scanner

class BaseLoader(<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Composer, BaseConstructor, BaseResolver):
    def __init__(self, stream) -> None: ...

class FullLoader(<PERSON>, Sc<PERSON>r, <PERSON><PERSON><PERSON>, Composer, FullConstructor, Resolver):
    def __init__(self, stream) -> None: ...

class SafeLoader(<PERSON>, Scanner, Pa<PERSON><PERSON>, Composer, SafeConstructor, Resolver):
    def __init__(self, stream) -> None: ...

class Loader(<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Composer, Constructor, Resolver):
    def __init__(self, stream) -> None: ...
