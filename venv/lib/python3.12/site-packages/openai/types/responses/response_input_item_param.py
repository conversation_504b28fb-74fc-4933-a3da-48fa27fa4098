# File generated from our OpenAPI spec by <PERSON>ainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union, Iterable
from typing_extensions import Literal, Required, <PERSON><PERSON><PERSON><PERSON>, TypedDict

from .easy_input_message_param import EasyInputMessageParam
from .response_output_message_param import ResponseOutputMessageParam
from .response_reasoning_item_param import ResponseReasoningItemParam
from .response_computer_tool_call_param import ResponseComputer<PERSON>ool<PERSON>allParam
from .response_function_tool_call_param import ResponseFunctionToolCallParam
from .response_function_web_search_param import ResponseFunctionWebSearchParam
from .response_file_search_tool_call_param import ResponseFileSearchToolCallParam
from .response_input_message_content_list_param import ResponseInputMessageContentListParam
from .response_computer_tool_call_output_screenshot_param import ResponseComputerToolCallOutputScreenshotParam

__all__ = [
    "ResponseInputItemParam",
    "Message",
    "ComputerCallOutput",
    "ComputerCallOutputAcknowledgedSafetyCheck",
    "FunctionCallOutput",
    "ItemReference",
]


class Message(TypedDict, total=False):
    content: Required[ResponseInputMessageContentListParam]
    """
    A list of one or many input items to the model, containing different content
    types.
    """

    role: Required[Literal["user", "system", "developer"]]
    """The role of the message input. One of `user`, `system`, or `developer`."""

    status: Literal["in_progress", "completed", "incomplete"]
    """The status of item.

    One of `in_progress`, `completed`, or `incomplete`. Populated when items are
    returned via API.
    """

    type: Literal["message"]
    """The type of the message input. Always set to `message`."""


class ComputerCallOutputAcknowledgedSafetyCheck(TypedDict, total=False):
    id: Required[str]
    """The ID of the pending safety check."""

    code: Required[str]
    """The type of the pending safety check."""

    message: Required[str]
    """Details about the pending safety check."""


class ComputerCallOutput(TypedDict, total=False):
    call_id: Required[str]
    """The ID of the computer tool call that produced the output."""

    output: Required[ResponseComputerToolCallOutputScreenshotParam]
    """A computer screenshot image used with the computer use tool."""

    type: Required[Literal["computer_call_output"]]
    """The type of the computer tool call output. Always `computer_call_output`."""

    id: str
    """The ID of the computer tool call output."""

    acknowledged_safety_checks: Iterable[ComputerCallOutputAcknowledgedSafetyCheck]
    """
    The safety checks reported by the API that have been acknowledged by the
    developer.
    """

    status: Literal["in_progress", "completed", "incomplete"]
    """The status of the message input.

    One of `in_progress`, `completed`, or `incomplete`. Populated when input items
    are returned via API.
    """


class FunctionCallOutput(TypedDict, total=False):
    call_id: Required[str]
    """The unique ID of the function tool call generated by the model."""

    output: Required[str]
    """A JSON string of the output of the function tool call."""

    type: Required[Literal["function_call_output"]]
    """The type of the function tool call output. Always `function_call_output`."""

    id: str
    """The unique ID of the function tool call output.

    Populated when this item is returned via API.
    """

    status: Literal["in_progress", "completed", "incomplete"]
    """The status of the item.

    One of `in_progress`, `completed`, or `incomplete`. Populated when items are
    returned via API.
    """


class ItemReference(TypedDict, total=False):
    id: Required[str]
    """The ID of the item to reference."""

    type: Required[Literal["item_reference"]]
    """The type of item to reference. Always `item_reference`."""


ResponseInputItemParam: TypeAlias = Union[
    EasyInputMessageParam,
    Message,
    ResponseOutputMessageParam,
    ResponseFileSearchToolCallParam,
    ResponseComputerToolCallParam,
    ComputerCallOutput,
    ResponseFunctionWebSearchParam,
    ResponseFunctionToolCallParam,
    FunctionCallOutput,
    ResponseReasoningItemParam,
    ItemReference,
]
