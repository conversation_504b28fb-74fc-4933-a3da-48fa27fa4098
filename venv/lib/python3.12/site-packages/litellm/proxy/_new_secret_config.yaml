model_list:
  - model_name: gpt-4o-mini-tts
    litellm_params:
      model: openai/gpt-4o-mini-tts
      api_key: os.environ/OPENAI_API_KEY
  - model_name: gpt-3.5-turbo
    litellm_params:
      model: azure/chatgpt-v-3
      api_base: https://openai-gpt-4-test-v-1.openai.azure.com/
      api_version: "2023-05-15"
      api_key: os.environ/AZURE_API_KEY
  - model_name: "gpt-4o-azure"
    litellm_params:
      model: azure/gpt-4o
      api_key: os.environ/AZURE_API_KEY
      api_base: os.environ/AZURE_API_BASE
  - model_name: fake-openai-endpoint
    litellm_params:
      model: openai/fake
      api_key: fake-key
      api_base: https://exampleopenaiendpoint-production.up.railway.app/
  - model_name: "gpt-4o-mini-openai"
    litellm_params:
      model: gpt-4o-mini
      api_key: os.environ/OPENAI_API_KEY
  - model_name: "bedrock-nova"
    litellm_params:
      model: us.amazon.nova-pro-v1:0
  - model_name: openrouter_model
    litellm_params:
      model: openrouter/openrouter_model
      api_key: os.environ/OPENROUTER_API_KEY
      api_base: http://0.0.0.0:8090
  - model_name: dall-e-3-azure
    litellm_params:
      model: azure/dall-e-3-test
      api_version: "2023-12-01-preview"
      api_base: os.environ/AZURE_SWEDEN_API_BASE
      api_key: os.environ/AZURE_SWEDEN_API_KEY
    model_info:
      input_cost_per_pixel: 10
  - model_name: "claude-3-7-sonnet"
    litellm_params:
      model: databricks/databricks-claude-3-7-sonnet
      api_key: os.environ/DATABRICKS_API_KEY
      api_base: os.environ/DATABRICKS_API_BASE
  - model_name: "gpt-4.1"
    litellm_params:
      model: azure/gpt-4.1
      api_key: os.environ/AZURE_API_KEY_REALTIME
      api_base: https://krris-m2f9a9i7-eastus2.openai.azure.com/
  - model_name: "xai/*"
    litellm_params:
      model: xai/*
      api_key: os.environ/XAI_API_KEY
  - model_name: "text-embedding-ada-002"
    litellm_params:
      model: text-embedding-ada-002
      api_key: os.environ/OPENAI_API_KEY
  - model_name: gemini/gemini-2.0-flash
    litellm_params:
      model: gemini/gemini-2.0-flash

litellm_settings:
  num_retries: 0
  check_provider_endpoint: true
  cache: true

files_settings:
  - custom_llm_provider: gemini
    api_key: os.environ/GEMINI_API_KEY

general_settings:
  store_prompts_in_spend_logs: true