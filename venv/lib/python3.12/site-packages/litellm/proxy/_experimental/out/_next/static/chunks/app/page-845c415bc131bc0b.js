(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{1900:function(e,s,l){Promise.resolve().then(l.bind(l,5916))},12011:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return v}});var t=l(57437),a=l(2265),r=l(99376),n=l(20831),i=l(94789),o=l(12514),c=l(49804),d=l(67101),m=l(84264),u=l(49566),x=l(96761),h=l(84566),p=l(19250),g=l(14474),j=l(13634),f=l(73002),_=l(3914);function v(){let[e]=j.Z.useForm(),s=(0,r.useSearchParams)();(0,_.e)("token");let l=s.get("invitation_id"),[v,y]=(0,a.useState)(null),[b,Z]=(0,a.useState)(""),[N,w]=(0,a.useState)(""),[k,S]=(0,a.useState)(null),[C,I]=(0,a.useState)(""),[T,A]=(0,a.useState)("");return(0,a.useEffect)(()=>{l&&(0,p.W_)(l).then(e=>{let s=e.login_url;console.log("login_url:",s),I(s);let l=e.token,t=(0,g.o)(l);A(l),console.log("decoded:",t),y(t.key),console.log("decoded user email:",t.user_email),w(t.user_email),S(t.user_id)})},[l]),(0,t.jsx)("div",{className:"mx-auto w-full max-w-md mt-10",children:(0,t.jsxs)(o.Z,{children:[(0,t.jsx)(x.Z,{className:"text-sm mb-5 text-center",children:"\uD83D\uDE85 LiteLLM"}),(0,t.jsx)(x.Z,{className:"text-xl",children:"Sign up"}),(0,t.jsx)(m.Z,{children:"Claim your user account to login to Admin UI."}),(0,t.jsx)(i.Z,{className:"mt-4",title:"SSO",icon:h.GH$,color:"sky",children:(0,t.jsxs)(d.Z,{numItems:2,className:"flex justify-between items-center",children:[(0,t.jsx)(c.Z,{children:"SSO is under the Enterprise Tier."}),(0,t.jsx)(c.Z,{children:(0,t.jsx)(n.Z,{variant:"primary",className:"mb-2",children:(0,t.jsx)("a",{href:"https://forms.gle/W3U4PZpJGFHWtHyA9",target:"_blank",children:"Get Free Trial"})})})]})}),(0,t.jsxs)(j.Z,{className:"mt-10 mb-5 mx-auto",layout:"vertical",onFinish:e=>{console.log("in handle submit. accessToken:",v,"token:",T,"formValues:",e),v&&T&&(e.user_email=N,k&&l&&(0,p.m_)(v,l,k,e.password).then(e=>{let s="/ui/";s+="?login=success",document.cookie="token="+T,console.log("redirecting to:",s),window.location.href=s}))},children:[(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.Z.Item,{label:"Email Address",name:"user_email",children:(0,t.jsx)(u.Z,{type:"email",disabled:!0,value:N,defaultValue:N,className:"max-w-md"})}),(0,t.jsx)(j.Z.Item,{label:"Password",name:"password",rules:[{required:!0,message:"password required to sign up"}],help:"Create a password for your account",children:(0,t.jsx)(u.Z,{placeholder:"",type:"password",className:"max-w-md"})})]}),(0,t.jsx)("div",{className:"mt-10",children:(0,t.jsx)(f.ZP,{htmlType:"submit",children:"Sign Up"})})]})]})})}},5916:function(e,s,l){"use strict";l.r(s),l.d(s,{default:function(){return aH}});var t,a,r,n,i,o,c=l(57437),d=l(2265),m=l(99376),u=l(14474),x=l(21623),h=l(29827),p=l(27648),g=l(80795),j=l(15883),f=l(40428),_=l(3914),v=l(19250);let y=async e=>{if(!e)return null;try{return await (0,v.g)(e)}catch(e){return console.error("Error fetching proxy settings:",e),null}};var b=e=>{let{userID:s,userEmail:l,userRole:t,premiumUser:a,proxySettings:r,setProxySettings:n,accessToken:i}=e,[o,m]=(0,d.useState)("");(0,d.useEffect)(()=>{(async()=>{if(i){let e=await y(i);console.log("response from fetchProxySettings",e),e&&n(e)}})()},[i]),(0,d.useEffect)(()=>{m((null==r?void 0:r.PROXY_LOGOUT_URL)||"")},[r]);let u=[{key:"1",label:(0,c.jsxs)("div",{className:"py-1",children:[(0,c.jsxs)("p",{className:"text-sm text-gray-600",children:["Role: ",t]}),(0,c.jsxs)("p",{className:"text-sm text-gray-600",children:["Email: ",l||"Unknown"]}),(0,c.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,c.jsx)(j.Z,{})," ",s]}),(0,c.jsxs)("p",{className:"text-sm text-gray-600",children:["Premium User: ",String(a)]})]})},{key:"2",label:(0,c.jsxs)("p",{className:"text-sm hover:text-gray-900",onClick:()=>{(0,_.b)(),window.location.href=o},children:[(0,c.jsx)(f.Z,{})," Logout"]})}];return(0,c.jsx)("nav",{className:"bg-white border-b border-gray-200 sticky top-0 z-10",children:(0,c.jsx)("div",{className:"w-full",children:(0,c.jsxs)("div",{className:"flex items-center h-12 px-4",children:[(0,c.jsx)("div",{className:"flex items-center flex-shrink-0",children:(0,c.jsx)(p.default,{href:"/",className:"flex items-center",children:(0,c.jsx)("img",{src:"/get_image",alt:"LiteLLM Brand",className:"h-8 w-auto"})})}),(0,c.jsxs)("div",{className:"flex items-center space-x-5 ml-auto",children:[(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/",target:"_blank",rel:"noopener noreferrer",className:"text-[13px] text-gray-600 hover:text-gray-900 transition-colors",children:"Docs"}),(0,c.jsx)(g.Z,{menu:{items:u,style:{padding:"4px",marginTop:"4px"}},children:(0,c.jsxs)("button",{className:"inline-flex items-center text-[13px] text-gray-600 hover:text-gray-900 transition-colors",children:["User",(0,c.jsx)("svg",{className:"ml-1 w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M19 9l-7 7-7-7"})})]})})]})]})})})};let Z=async(e,s,l,t,a)=>{let r;r="Admin"!=l&&"Admin Viewer"!=l?await (0,v.It)(e,(null==t?void 0:t.organization_id)||null,s):await (0,v.It)(e,(null==t?void 0:t.organization_id)||null),console.log("givenTeams: ".concat(r)),a(r)};var N=l(49804),w=l(67101),k=l(20831),S=l(49566),C=l(87452),I=l(88829),T=l(72208),A=l(84264),E=l(96761),P=l(29233),O=l(52787),L=l(13634),D=l(41021),M=l(51369),F=l(29967),R=l(73002),q=l(56632),U=l(30150),z=e=>{let{step:s=.01,style:l={width:"100%"},placeholder:t="Enter a numerical value",min:a,max:r,onChange:n,...i}=e;return(0,c.jsx)(U.Z,{onWheel:e=>e.currentTarget.blur(),step:s,style:l,placeholder:t,min:a,max:r,onChange:n,...i})};let V=async(e,s,l)=>{try{if(null===e||null===s)return;if(null!==l){let t=(await (0,v.So)(l,e,s,!0)).data.map(e=>e.id),a=[],r=[];return t.forEach(e=>{e.endsWith("/*")?a.push(e):r.push(e)}),[...a,...r]}}catch(e){console.error("Error fetching user models:",e)}},K=e=>{if(e.endsWith("/*")){let s=e.replace("/*","");return"All ".concat(s," models")}return e},B=(e,s)=>{let l=[],t=[];return console.log("teamModels",e),console.log("allModels",s),e.forEach(e=>{if(e.endsWith("/*")){let a=e.replace("/*",""),r=s.filter(e=>e.startsWith(a+"/"));t.push(...r),l.push(e)}else t.push(e)}),[...l,...t].filter((e,s,l)=>l.indexOf(e)===s)};var H=l(20577),J=l(15424),W=l(75957);let G=(e,s)=>["metadata","config","enforced_params","aliases"].includes(e)||"json"===s.format,Y=e=>{if(!e)return!0;try{return JSON.parse(e),!0}catch(e){return!1}},$=(e,s,l)=>{let t={max_budget:"Enter maximum budget in USD (e.g., 100.50)",budget_duration:"Select a time period for budget reset",tpm_limit:"Enter maximum tokens per minute (whole number)",rpm_limit:"Enter maximum requests per minute (whole number)",duration:"Enter duration (e.g., 30s, 24h, 7d)",metadata:'Enter JSON object with key-value pairs\nExample: {"team": "research", "project": "nlp"}',config:'Enter configuration as JSON object\nExample: {"setting": "value"}',permissions:"Enter comma-separated permission strings",enforced_params:'Enter parameters as JSON object\nExample: {"param": "value"}',blocked:"Enter true/false or specific block conditions",aliases:'Enter aliases as JSON object\nExample: {"alias1": "value1", "alias2": "value2"}',models:"Select one or more model names",key_alias:"Enter a unique identifier for this key",tags:"Enter comma-separated tag strings"}[e]||({string:"Text input",number:"Numeric input",integer:"Whole number input",boolean:"True/False value"})[l]||"Text input";return G(e,s)?"".concat(t,"\nMust be valid JSON format"):s.enum?"Select from available options\nAllowed values: ".concat(s.enum.join(", ")):t};var X=e=>{let{schemaComponent:s,excludedFields:l=[],form:t,overrideLabels:a={},overrideTooltips:r={},customValidation:n={},defaultValues:i={}}=e,[o,m]=(0,d.useState)(null),[u,x]=(0,d.useState)(null);(0,d.useEffect)(()=>{(async()=>{try{let e=(await (0,v.lP)()).components.schemas[s];if(!e)throw Error('Schema component "'.concat(s,'" not found'));m(e);let a={};Object.keys(e.properties).filter(e=>!l.includes(e)&&void 0!==i[e]).forEach(e=>{a[e]=i[e]}),t.setFieldsValue(a)}catch(e){console.error("Schema fetch error:",e),x(e instanceof Error?e.message:"Failed to fetch schema")}})()},[s,t,l]);let h=e=>{if(e.type)return e.type;if(e.anyOf){let s=e.anyOf.map(e=>e.type);if(s.includes("number")||s.includes("integer"))return"number";s.includes("string")}return"string"},p=(e,s)=>{var l;let t;let d=h(s),m=null==o?void 0:null===(l=o.required)||void 0===l?void 0:l.includes(e),u=a[e]||s.title||e,x=r[e]||s.description,p=[];m&&p.push({required:!0,message:"".concat(u," is required")}),n[e]&&p.push({validator:n[e]}),G(e,s)&&p.push({validator:async(e,s)=>{if(s&&!Y(s))throw Error("Please enter valid JSON")}});let g=x?(0,c.jsxs)("span",{children:[u," ",(0,c.jsx)(W.Z,{title:x,children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}):u;return t=G(e,s)?(0,c.jsx)(q.default.TextArea,{rows:4,placeholder:"Enter as JSON",className:"font-mono"}):s.enum?(0,c.jsx)(O.default,{children:s.enum.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:e},e))}):"number"===d||"integer"===d?(0,c.jsx)(H.Z,{style:{width:"100%"},precision:"integer"===d?0:void 0}):"duration"===e?(0,c.jsx)(S.Z,{placeholder:"eg: 30s, 30h, 30d"}):(0,c.jsx)(S.Z,{placeholder:x||""}),(0,c.jsx)(L.Z.Item,{label:g,name:e,className:"mt-8",rules:p,initialValue:i[e],help:(0,c.jsx)("div",{className:"text-xs text-gray-500",children:$(e,s,d)}),children:t},e)};return u?(0,c.jsxs)("div",{className:"text-red-500",children:["Error: ",u]}):(null==o?void 0:o.properties)?(0,c.jsx)("div",{children:Object.entries(o.properties).filter(e=>{let[s]=e;return!l.includes(s)}).map(e=>{let[s,l]=e;return p(s,l)})}):null},Q=e=>{let{teams:s,value:l,onChange:t}=e;return(0,c.jsx)(O.default,{showSearch:!0,placeholder:"Search or select a team",value:l,onChange:t,filterOption:(e,s)=>{var l,t,a;return!!s&&((null===(a=s.children)||void 0===a?void 0:null===(t=a[0])||void 0===t?void 0:null===(l=t.props)||void 0===l?void 0:l.children)||"").toLowerCase().includes(e.toLowerCase())},optionFilterProp:"children",children:null==s?void 0:s.map(e=>(0,c.jsxs)(O.default.Option,{value:e.team_id,children:[(0,c.jsx)("span",{className:"font-medium",children:e.team_alias})," ",(0,c.jsxs)("span",{className:"text-gray-500",children:["(",e.team_id,")"]})]},e.team_id))})},ee=l(57365),es=l(93192);function el(e){let{isInvitationLinkModalVisible:s,setIsInvitationLinkModalVisible:l,baseUrl:t,invitationLinkData:a}=e,{Title:r,Paragraph:n}=es.default,i=()=>(null==a?void 0:a.has_user_setup_sso)?new URL("/ui",t).toString():new URL("/ui?invitation_id=".concat(null==a?void 0:a.id),t).toString();return(0,c.jsxs)(M.Z,{title:"Invitation Link",visible:s,width:800,footer:null,onOk:()=>{l(!1)},onCancel:()=>{l(!1)},children:[(0,c.jsx)(n,{children:"Copy and send the generated link to onboard this user to the proxy."}),(0,c.jsxs)("div",{className:"flex justify-between pt-5 pb-2",children:[(0,c.jsx)(A.Z,{className:"text-base",children:"User ID"}),(0,c.jsx)(A.Z,{children:null==a?void 0:a.user_id})]}),(0,c.jsxs)("div",{className:"flex justify-between pt-5 pb-2",children:[(0,c.jsx)(A.Z,{children:"Invitation Link"}),(0,c.jsx)(A.Z,{children:(0,c.jsx)(A.Z,{children:i()})})]}),(0,c.jsx)("div",{className:"flex justify-end mt-5",children:(0,c.jsx)(P.CopyToClipboard,{text:i(),onCopy:()=>D.ZP.success("Copied!"),children:(0,c.jsx)(k.Z,{variant:"primary",children:"Copy invitation link"})})})]})}var et=l(77388),ea=l(1709),er=l(73879),en=l(3632),ei=l(15452),eo=l.n(ei),ec=l(71157),ed=l(44643),em=e=>{let{accessToken:s,teams:l,possibleUIRoles:t,onUsersCreated:a}=e,[r,n]=(0,d.useState)(!1),[i,o]=(0,d.useState)([]),[m,u]=(0,d.useState)(!1),[x,h]=(0,d.useState)(null),[p,g]=(0,d.useState)(null),[j,f]=(0,d.useState)("http://localhost:4000");(0,d.useEffect)(()=>{(async()=>{try{let e=await (0,v.g)(s);g(e)}catch(e){console.error("Error fetching UI settings:",e)}})(),f(new URL("/",window.location.href).toString())},[s]);let _=async()=>{u(!0);let e=i.map(e=>({...e,status:"pending"}));o(e);let l=!1;for(let a=0;a<e.length;a++){var t,r,n;let i=e[a];try{let e={...i};e.teams&&"string"==typeof e.teams&&(e.teams=e.teams.split(",").map(e=>e.trim())),e.models&&"string"==typeof e.models&&(e.models=e.models.split(",").map(e=>e.trim())),e.max_budget&&""!==e.max_budget.toString().trim()&&(e.max_budget=parseFloat(e.max_budget.toString()));let r=await (0,v.Ov)(s,null,e);if(console.log("Full response:",r),r&&(r.key||r.user_id)){l=!0,console.log("Success case triggered");let e=(null===(t=r.data)||void 0===t?void 0:t.user_id)||r.user_id;try{if(null==p?void 0:p.SSO_ENABLED){let e=new URL("/ui",j).toString();o(s=>s.map((s,l)=>l===a?{...s,status:"success",key:r.key||r.user_id,invitation_link:e}:s))}else{let l=await (0,v.XO)(s,e),t=new URL("/ui?invitation_id=".concat(l.id),j).toString();o(e=>e.map((e,s)=>s===a?{...e,status:"success",key:r.key||r.user_id,invitation_link:t}:e))}}catch(e){console.error("Error creating invitation:",e),o(e=>e.map((e,s)=>s===a?{...e,status:"success",key:r.key||r.user_id,error:"User created but failed to generate invitation link"}:e))}}else{console.log("Error case triggered");let e=(null==r?void 0:r.error)||"Failed to create user";console.log("Error message:",e),o(s=>s.map((s,l)=>l===a?{...s,status:"failed",error:e}:s))}}catch(s){console.error("Caught error:",s);let e=(null==s?void 0:null===(n=s.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.error)||(null==s?void 0:s.message)||String(s);o(s=>s.map((s,l)=>l===a?{...s,status:"failed",error:e}:s))}}u(!1),l&&a&&a()};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(k.Z,{className:"mx-auto mb-0",onClick:()=>n(!0),children:"+ Bulk Invite Users"}),(0,c.jsx)(M.Z,{title:"Bulk Invite Users",visible:r,width:800,onCancel:()=>n(!1),bodyStyle:{maxHeight:"70vh",overflow:"auto"},footer:null,children:(0,c.jsx)("div",{className:"flex flex-col",children:0===i.length?(0,c.jsxs)("div",{className:"mb-6",children:[(0,c.jsxs)("div",{className:"flex items-center mb-4",children:[(0,c.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center mr-3",children:"1"}),(0,c.jsx)("h3",{className:"text-lg font-medium",children:"Download and fill the template"})]}),(0,c.jsxs)("div",{className:"ml-11 mb-6",children:[(0,c.jsx)("p",{className:"mb-4",children:"Add multiple users at once by following these steps:"}),(0,c.jsxs)("ol",{className:"list-decimal list-inside space-y-2 ml-2 mb-4",children:[(0,c.jsx)("li",{children:"Download our CSV template"}),(0,c.jsx)("li",{children:"Add your users' information to the spreadsheet"}),(0,c.jsx)("li",{children:"Save the file and upload it here"}),(0,c.jsx)("li",{children:"After creation, download the results file containing the API keys for each user"})]}),(0,c.jsxs)("div",{className:"bg-gray-50 p-4 rounded-md border border-gray-200 mb-4",children:[(0,c.jsx)("h4",{className:"font-medium mb-2",children:"Template Column Names"}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,c.jsxs)("div",{className:"flex items-start",children:[(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500 mt-1.5 mr-2 flex-shrink-0"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"font-medium",children:"user_email"}),(0,c.jsx)("p",{className:"text-sm text-gray-600",children:"User's email address (required)"})]})]}),(0,c.jsxs)("div",{className:"flex items-start",children:[(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500 mt-1.5 mr-2 flex-shrink-0"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"font-medium",children:"user_role"}),(0,c.jsx)("p",{className:"text-sm text-gray-600",children:'User\'s role (one of: "proxy_admin", "proxy_admin_view_only", "internal_user", "internal_user_view_only")'})]})]}),(0,c.jsxs)("div",{className:"flex items-start",children:[(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-300 mt-1.5 mr-2 flex-shrink-0"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"font-medium",children:"teams"}),(0,c.jsx)("p",{className:"text-sm text-gray-600",children:'Comma-separated team IDs (e.g., "team-1,team-2")'})]})]}),(0,c.jsxs)("div",{className:"flex items-start",children:[(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-300 mt-1.5 mr-2 flex-shrink-0"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"font-medium",children:"max_budget"}),(0,c.jsx)("p",{className:"text-sm text-gray-600",children:'Maximum budget as a number (e.g., "100")'})]})]}),(0,c.jsxs)("div",{className:"flex items-start",children:[(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-300 mt-1.5 mr-2 flex-shrink-0"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"font-medium",children:"budget_duration"}),(0,c.jsx)("p",{className:"text-sm text-gray-600",children:'Budget reset period (e.g., "30d", "1mo")'})]})]}),(0,c.jsxs)("div",{className:"flex items-start",children:[(0,c.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-300 mt-1.5 mr-2 flex-shrink-0"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"font-medium",children:"models"}),(0,c.jsx)("p",{className:"text-sm text-gray-600",children:'Comma-separated allowed models (e.g., "gpt-3.5-turbo,gpt-4")'})]})]})]})]}),(0,c.jsxs)(k.Z,{onClick:()=>{let e=new Blob([eo().unparse([["user_email","user_role","teams","max_budget","budget_duration","models"],["<EMAIL>","internal_user","team-id-1,team-id-2","100","30d","gpt-3.5-turbo,gpt-4"]])],{type:"text/csv"}),s=window.URL.createObjectURL(e),l=document.createElement("a");l.href=s,l.download="bulk_users_template.csv",document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(s)},size:"lg",className:"w-full md:w-auto",children:[(0,c.jsx)(er.Z,{className:"mr-2"})," Download CSV Template"]})]}),(0,c.jsxs)("div",{className:"flex items-center mb-4",children:[(0,c.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center mr-3",children:"2"}),(0,c.jsx)("h3",{className:"text-lg font-medium",children:"Upload your completed CSV"})]}),(0,c.jsx)("div",{className:"ml-11",children:(0,c.jsx)(et.Z,{beforeUpload:e=>(h(null),eo().parse(e,{complete:e=>{let s=e.data[0],l=["user_email","user_role"].filter(e=>!s.includes(e));if(l.length>0){h("Your CSV is missing these required columns: ".concat(l.join(", "))),o([]);return}try{let l=e.data.slice(1).map((e,l)=>{var t,a,r,n,i,o;let c={user_email:(null===(t=e[s.indexOf("user_email")])||void 0===t?void 0:t.trim())||"",user_role:(null===(a=e[s.indexOf("user_role")])||void 0===a?void 0:a.trim())||"",teams:null===(r=e[s.indexOf("teams")])||void 0===r?void 0:r.trim(),max_budget:null===(n=e[s.indexOf("max_budget")])||void 0===n?void 0:n.trim(),budget_duration:null===(i=e[s.indexOf("budget_duration")])||void 0===i?void 0:i.trim(),models:null===(o=e[s.indexOf("models")])||void 0===o?void 0:o.trim(),rowNumber:l+2,isValid:!0,error:""},d=[];c.user_email||d.push("Email is required"),c.user_role||d.push("Role is required"),c.user_email&&!c.user_email.includes("@")&&d.push("Invalid email format");let m=["proxy_admin","proxy_admin_view_only","internal_user","internal_user_view_only"];return c.user_role&&!m.includes(c.user_role)&&d.push("Invalid role. Must be one of: ".concat(m.join(", "))),c.max_budget&&isNaN(parseFloat(c.max_budget.toString()))&&d.push("Max budget must be a number"),d.length>0&&(c.isValid=!1,c.error=d.join(", ")),c}),t=l.filter(e=>e.isValid);o(l),0===t.length?h("No valid users found in the CSV. Please check the errors below."):t.length<l.length?h("Found ".concat(l.length-t.length," row(s) with errors. Please correct them before proceeding.")):D.ZP.success("Successfully parsed ".concat(t.length," users"))}catch(s){let e=s instanceof Error?s.message:"Unknown error";h("Error parsing CSV: ".concat(e)),o([])}},error:e=>{h("Failed to parse CSV file: ".concat(e.message)),o([])},header:!1}),!1),accept:".csv",maxCount:1,showUploadList:!1,children:(0,c.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors cursor-pointer",children:[(0,c.jsx)(en.Z,{className:"text-3xl text-gray-400 mb-2"}),(0,c.jsx)("p",{className:"mb-1",children:"Drag and drop your CSV file here"}),(0,c.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:"or"}),(0,c.jsx)(k.Z,{size:"sm",children:"Browse files"})]})})})]}):(0,c.jsxs)("div",{className:"mb-6",children:[(0,c.jsxs)("div",{className:"flex items-center mb-4",children:[(0,c.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center mr-3",children:"3"}),(0,c.jsx)("h3",{className:"text-lg font-medium",children:i.some(e=>"success"===e.status||"failed"===e.status)?"User Creation Results":"Review and create users"})]}),x&&(0,c.jsx)("div",{className:"ml-11 mb-4 p-4 bg-red-50 border border-red-200 rounded-md",children:(0,c.jsx)(A.Z,{className:"text-red-600 font-medium",children:x})}),(0,c.jsxs)("div",{className:"ml-11",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,c.jsx)("div",{className:"flex items-center",children:i.some(e=>"success"===e.status||"failed"===e.status)?(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(A.Z,{className:"text-lg font-medium mr-3",children:"Creation Summary"}),(0,c.jsxs)(A.Z,{className:"text-sm bg-green-100 text-green-800 px-2 py-1 rounded mr-2",children:[i.filter(e=>"success"===e.status).length," Successful"]}),i.some(e=>"failed"===e.status)&&(0,c.jsxs)(A.Z,{className:"text-sm bg-red-100 text-red-800 px-2 py-1 rounded",children:[i.filter(e=>"failed"===e.status).length," Failed"]})]}):(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(A.Z,{className:"text-lg font-medium mr-3",children:"User Preview"}),(0,c.jsxs)(A.Z,{className:"text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:[i.filter(e=>e.isValid).length," of ",i.length," users valid"]})]})}),!i.some(e=>"success"===e.status||"failed"===e.status)&&(0,c.jsxs)("div",{className:"flex space-x-3",children:[(0,c.jsx)(k.Z,{onClick:()=>{o([]),h(null)},variant:"secondary",children:"Back"}),(0,c.jsx)(k.Z,{onClick:_,disabled:0===i.filter(e=>e.isValid).length||m,children:m?"Creating...":"Create ".concat(i.filter(e=>e.isValid).length," Users")})]})]}),i.some(e=>"success"===e.status)&&(0,c.jsx)("div",{className:"mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md",children:(0,c.jsxs)("div",{className:"flex items-start",children:[(0,c.jsx)("div",{className:"mr-3 mt-1",children:(0,c.jsx)(ed.Z,{className:"h-5 w-5 text-blue-500"})}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium text-blue-800",children:"User creation complete"}),(0,c.jsxs)(A.Z,{className:"block text-sm text-blue-700 mt-1",children:[(0,c.jsx)("span",{className:"font-medium",children:"Next step:"})," Download the credentials file containing API keys and invitation links. Users will need these API keys to make LLM requests through LiteLLM."]})]})]})}),(0,c.jsx)(ea.Z,{dataSource:i,columns:[{title:"Row",dataIndex:"rowNumber",key:"rowNumber",width:80},{title:"Email",dataIndex:"user_email",key:"user_email"},{title:"Role",dataIndex:"user_role",key:"user_role"},{title:"Teams",dataIndex:"teams",key:"teams"},{title:"Budget",dataIndex:"max_budget",key:"max_budget"},{title:"Status",key:"status",render:(e,s)=>s.isValid?s.status&&"pending"!==s.status?"success"===s.status?(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(ed.Z,{className:"h-5 w-5 text-green-500 mr-2"}),(0,c.jsx)("span",{className:"text-green-500",children:"Success"})]}),s.invitation_link&&(0,c.jsx)("div",{className:"mt-1",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("span",{className:"text-xs text-gray-500 truncate max-w-[150px]",children:s.invitation_link}),(0,c.jsx)(P.CopyToClipboard,{text:s.invitation_link,onCopy:()=>D.ZP.success("Invitation link copied!"),children:(0,c.jsx)("button",{className:"ml-1 text-blue-500 text-xs hover:text-blue-700",children:"Copy"})})]})})]}):(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(ec.Z,{className:"h-5 w-5 text-red-500 mr-2"}),(0,c.jsx)("span",{className:"text-red-500",children:"Failed"})]}),s.error&&(0,c.jsx)("span",{className:"text-sm text-red-500 ml-7",children:JSON.stringify(s.error)})]}):(0,c.jsx)("span",{className:"text-gray-500",children:"Pending"}):(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(ec.Z,{className:"h-5 w-5 text-red-500 mr-2"}),(0,c.jsx)("span",{className:"text-red-500",children:"Invalid"})]}),s.error&&(0,c.jsx)("span",{className:"text-sm text-red-500 ml-7",children:s.error})]})}],size:"small",pagination:{pageSize:5},scroll:{y:300},rowClassName:e=>e.isValid?"":"bg-red-50"}),!i.some(e=>"success"===e.status||"failed"===e.status)&&(0,c.jsxs)("div",{className:"flex justify-end mt-4",children:[(0,c.jsx)(k.Z,{onClick:()=>{o([]),h(null)},variant:"secondary",className:"mr-3",children:"Back"}),(0,c.jsx)(k.Z,{onClick:_,disabled:0===i.filter(e=>e.isValid).length||m,children:m?"Creating...":"Create ".concat(i.filter(e=>e.isValid).length," Users")})]}),i.some(e=>"success"===e.status||"failed"===e.status)&&(0,c.jsxs)("div",{className:"flex justify-end mt-4",children:[(0,c.jsx)(k.Z,{onClick:()=>{o([]),h(null)},variant:"secondary",className:"mr-3",children:"Start New Bulk Import"}),(0,c.jsxs)(k.Z,{onClick:()=>{let e=i.map(e=>({user_email:e.user_email,user_role:e.user_role,status:e.status,key:e.key||"",invitation_link:e.invitation_link||"",error:e.error||""})),s=new Blob([eo().unparse(e)],{type:"text/csv"}),l=window.URL.createObjectURL(s),t=document.createElement("a");t.href=l,t.download="bulk_users_results.csv",document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(l)},variant:"primary",className:"flex items-center",children:[(0,c.jsx)(er.Z,{className:"mr-2"})," Download User Credentials"]})]})]})]})})})]})};let{Option:eu}=O.default;var ex=e=>{let{userID:s,accessToken:l,teams:t,possibleUIRoles:a,onUserCreated:r,isEmbedded:n=!1}=e,i=(0,h.NL)(),[o,u]=(0,d.useState)(null),[x]=L.Z.useForm(),[p,g]=(0,d.useState)(!1),[j,f]=(0,d.useState)(!1),[_,y]=(0,d.useState)([]),[b,Z]=(0,d.useState)(!1),[N,w]=(0,d.useState)(null),P=(0,m.useRouter)(),[F,U]=(0,d.useState)("http://localhost:4000");(0,d.useEffect)(()=>{(async()=>{try{let e=await (0,v.So)(l,s,"any"),t=[];for(let s=0;s<e.data.length;s++){let l=e.data[s];t.push(l.id)}console.log("Model data response:",e.data),console.log("Available models:",t),y(t);let a=await (0,v.g)(l);console.log("uiSettingsResponse:",a),u(a)}catch(e){console.error("Error fetching model data:",e)}})()},[]),(0,d.useEffect)(()=>{P&&U(new URL("/",window.location.href).toString())},[P]);let z=async e=>{var t,a,c;try{D.ZP.info("Making API Call"),n||g(!0),e.models&&0!==e.models.length||"proxy_admin"===e.user_role||(console.log("formValues.user_role",e.user_role),e.models=["no-default-models"]),console.log("formValues in create user:",e);let a=await (0,v.Ov)(l,null,e);await i.invalidateQueries({queryKey:["userList"]}),console.log("user create Response:",a),f(!0);let c=(null===(t=a.data)||void 0===t?void 0:t.user_id)||a.user_id;if(r&&n){r(c),x.resetFields();return}if(null==o?void 0:o.SSO_ENABLED){let e={id:crypto.randomUUID(),user_id:c,is_accepted:!1,accepted_at:null,expires_at:new Date(Date.now()+6048e5),created_at:new Date,created_by:s,updated_at:new Date,updated_by:s,has_user_setup_sso:!0};w(e),Z(!0)}else(0,v.XO)(l,c).then(e=>{e.has_user_setup_sso=!1,w(e),Z(!0)});D.ZP.success("API user Created"),x.resetFields(),localStorage.removeItem("userData"+s)}catch(s){let e=(null===(c=s.response)||void 0===c?void 0:null===(a=c.data)||void 0===a?void 0:a.detail)||(null==s?void 0:s.message)||"Error creating the user";D.ZP.error(e),console.error("Error creating the user:",s)}};return n?(0,c.jsxs)(L.Z,{form:x,onFinish:z,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsx)(L.Z.Item,{label:"User Email",name:"user_email",children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsx)(L.Z.Item,{label:"User Role",name:"user_role",children:(0,c.jsx)(O.default,{children:a&&Object.entries(a).map(e=>{let[s,{ui_label:l,description:t}]=e;return(0,c.jsx)(ee.Z,{value:s,title:l,children:(0,c.jsxs)("div",{className:"flex",children:[l," ",(0,c.jsx)("p",{className:"ml-2",style:{color:"gray",fontSize:"12px"},children:t})]})},s)})})}),(0,c.jsx)(L.Z.Item,{label:"Team ID",name:"team_id",children:(0,c.jsx)(O.default,{placeholder:"Select Team ID",style:{width:"100%"},children:t?t.map(e=>(0,c.jsx)(eu,{value:e.team_id,children:e.team_alias},e.team_id)):(0,c.jsx)(eu,{value:null,children:"Default Team"},"default")})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",children:(0,c.jsx)(q.default.TextArea,{rows:4,placeholder:"Enter metadata as JSON"})}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Create User"})})]}):(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(k.Z,{className:"mx-auto mb-0",onClick:()=>g(!0),children:"+ Invite User"}),(0,c.jsx)(em,{accessToken:l,teams:t,possibleUIRoles:a}),(0,c.jsxs)(M.Z,{title:"Invite User",visible:p,width:800,footer:null,onOk:()=>{g(!1),x.resetFields()},onCancel:()=>{g(!1),f(!1),x.resetFields()},children:[(0,c.jsx)(A.Z,{className:"mb-1",children:"Create a User who can own keys"}),(0,c.jsxs)(L.Z,{form:x,onFinish:z,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsx)(L.Z.Item,{label:"User Email",name:"user_email",children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Global Proxy Role"," ",(0,c.jsx)(W.Z,{title:"This is the role that the user will globally on the proxy. This role is independent of any team/org specific roles.",children:(0,c.jsx)(J.Z,{})})]}),name:"user_role",children:(0,c.jsx)(O.default,{children:a&&Object.entries(a).map(e=>{let[s,{ui_label:l,description:t}]=e;return(0,c.jsx)(ee.Z,{value:s,title:l,children:(0,c.jsxs)("div",{className:"flex",children:[l," ",(0,c.jsx)("p",{className:"ml-2",style:{color:"gray",fontSize:"12px"},children:t})]})},s)})})}),(0,c.jsx)(L.Z.Item,{label:"Team ID",className:"gap-2",name:"team_id",help:"If selected, user will be added as a 'user' role to the team.",children:(0,c.jsx)(O.default,{placeholder:"Select Team ID",style:{width:"100%"},children:t?t.map(e=>(0,c.jsx)(eu,{value:e.team_id,children:e.team_alias},e.team_id)):(0,c.jsx)(eu,{value:null,children:"Default Team"},"default")})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",children:(0,c.jsx)(q.default.TextArea,{rows:4,placeholder:"Enter metadata as JSON"})}),(0,c.jsxs)(C.Z,{children:[(0,c.jsx)(T.Z,{children:(0,c.jsx)(E.Z,{children:"Personal Key Creation"})}),(0,c.jsx)(I.Z,{children:(0,c.jsx)(L.Z.Item,{className:"gap-2",label:(0,c.jsxs)("span",{children:["Models"," ",(0,c.jsx)(W.Z,{title:"Models user has access to, outside of team scope.",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"models",help:"Models user has access to, outside of team scope.",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",style:{width:"100%"},children:[(0,c.jsx)(O.default.Option,{value:"all-proxy-models",children:"All Proxy Models"},"all-proxy-models"),_.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:K(e)},e))]})})})]}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Create User"})})]})]}),j&&(0,c.jsx)(el,{isInvitationLinkModalVisible:b,setIsInvitationLinkModalVisible:Z,baseUrl:F,invitationLinkData:N})]})},eh=l(7310),ep=l.n(eh),eg=l(20347);let{Option:ej}=O.default,ef=e=>e?({"24h":"daily","7d":"weekly","30d":"monthly"})[e]||e:"Not set";var e_=e=>{let{value:s,onChange:l,className:t="",style:a={}}=e;return(0,c.jsxs)(O.default,{style:{width:"100%",...a},value:s||void 0,onChange:l,className:t,placeholder:"n/a",children:[(0,c.jsx)(ej,{value:"24h",children:"daily"}),(0,c.jsx)(ej,{value:"7d",children:"weekly"}),(0,c.jsx)(ej,{value:"30d",children:"monthly"})]})};let{Option:ev}=O.default,ey=e=>{let s=[];if(console.log("data:",JSON.stringify(e)),e)for(let l of e)l.metadata&&l.metadata.tags&&s.push(...l.metadata.tags);let l=Array.from(new Set(s)).map(e=>({value:e,label:e}));return console.log("uniqueTags:",l),l},eb=async(e,s,l,t)=>{try{if(null===e||null===s)return[];if(null!==l){let a=(await (0,v.So)(l,e,s,!0,t)).data.map(e=>e.id);return console.log("available_model_names:",a),a}return[]}catch(e){return console.error("Error fetching user models:",e),[]}},eZ=async(e,s,l,t)=>{try{if(null===e||null===s)return;if(null!==l){let a=(await (0,v.So)(l,e,s)).data.map(e=>e.id);console.log("available_model_names:",a),t(a)}}catch(e){console.error("Error fetching user models:",e)}};var eN=e=>{let{userID:s,team:l,teams:t,userRole:a,accessToken:r,data:n,addKey:i}=e,[o]=L.Z.useForm(),[m,u]=(0,d.useState)(!1),[x,h]=(0,d.useState)(null),[p,g]=(0,d.useState)(null),[j,f]=(0,d.useState)([]),[_,y]=(0,d.useState)([]),[b,Z]=(0,d.useState)("you"),[U,V]=(0,d.useState)(ey(n)),[B,H]=(0,d.useState)([]),[G,Y]=(0,d.useState)(l),[$,ee]=(0,d.useState)(!1),[es,el]=(0,d.useState)(null),[et,ea]=(0,d.useState)({}),[er,en]=(0,d.useState)([]),[ei,eo]=(0,d.useState)(!1),ec=()=>{u(!1),o.resetFields()},ed=()=>{u(!1),h(null),Y(null),o.resetFields()};(0,d.useEffect)(()=>{s&&a&&r&&eZ(s,a,r,f)},[r,s,a]),(0,d.useEffect)(()=>{(async()=>{try{let e=(await (0,v.t3)(r)).guardrails.map(e=>e.guardrail_name);H(e)}catch(e){console.error("Failed to fetch guardrails:",e)}})()},[r]),(0,d.useEffect)(()=>{(async()=>{try{if(r){let e=sessionStorage.getItem("possibleUserRoles");if(e)ea(JSON.parse(e));else{let e=await (0,v.lg)(r);sessionStorage.setItem("possibleUserRoles",JSON.stringify(e)),ea(e)}}}catch(e){console.error("Error fetching possible user roles:",e)}})()},[r]);let em=async e=>{try{var l,t,a;let c=null!==(l=null==e?void 0:e.key_alias)&&void 0!==l?l:"",d=null!==(t=null==e?void 0:e.team_id)&&void 0!==t?t:null;if((null!==(a=null==n?void 0:n.filter(e=>e.team_id===d).map(e=>e.key_alias))&&void 0!==a?a:[]).includes(c))throw Error("Key alias ".concat(c," already exists for team with ID ").concat(d,", please provide another key alias"));if(D.ZP.info("Making API Call"),u(!0),"you"===b&&(e.user_id=s),"service_account"===b){let s={};try{s=JSON.parse(e.metadata||"{}")}catch(e){console.error("Error parsing metadata:",e)}s.service_account_id=e.key_alias,e.metadata=JSON.stringify(s)}let m=await (0,v.wX)(r,s,e);console.log("key create Response:",m),i(m),h(m.key),g(m.soft_budget),D.ZP.success("API Key Created"),o.resetFields(),localStorage.removeItem("userData"+s)}catch(e){console.log("error in create key:",e),D.ZP.error("Error creating the key: ".concat(e))}};(0,d.useEffect)(()=>{if(s&&a&&r){var e;eb(s,a,r,null!==(e=null==G?void 0:G.team_id)&&void 0!==e?e:null).then(e=>{var s;y(Array.from(new Set([...null!==(s=null==G?void 0:G.models)&&void 0!==s?s:[],...e])))})}o.setFieldValue("models",[])},[G,r,s,a]);let eu=async e=>{if(!e){en([]);return}eo(!0);try{let s=new URLSearchParams;if(s.append("user_email",e),null==r)return;let l=(await (0,v.u5)(r,s)).map(e=>({label:"".concat(e.user_email," (").concat(e.user_id,")"),value:e.user_id,user:e}));en(l)}catch(e){console.error("Error fetching users:",e),D.ZP.error("Failed to search for users")}finally{eo(!1)}},eh=(0,d.useCallback)(ep()(e=>eu(e),300),[r]),ej=(e,s)=>{let l=s.user;o.setFieldsValue({user_id:l.user_id})};return(0,c.jsxs)("div",{children:[a&&eg.LQ.includes(a)&&(0,c.jsx)(k.Z,{className:"mx-auto",onClick:()=>u(!0),children:"+ Create New Key"}),(0,c.jsx)(M.Z,{visible:m,width:1e3,footer:null,onOk:ec,onCancel:ed,children:(0,c.jsxs)(L.Z,{form:o,onFinish:em,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsxs)("div",{className:"mb-8",children:[(0,c.jsx)(E.Z,{className:"mb-4",children:"Key Ownership"}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Owned By"," ",(0,c.jsx)(W.Z,{title:"Select who will own this API key",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),className:"mb-4",children:(0,c.jsxs)(F.ZP.Group,{onChange:e=>Z(e.target.value),value:b,children:[(0,c.jsx)(F.ZP,{value:"you",children:"You"}),(0,c.jsx)(F.ZP,{value:"service_account",children:"Service Account"}),"Admin"===a&&(0,c.jsx)(F.ZP,{value:"another_user",children:"Another User"})]})}),"another_user"===b&&(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["User ID"," ",(0,c.jsx)(W.Z,{title:"The user who will own this key and be responsible for its usage",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"user_id",className:"mt-4",rules:[{required:"another_user"===b,message:"Please input the user ID of the user you are assigning the key to"}],children:(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{style:{display:"flex",marginBottom:"8px"},children:[(0,c.jsx)(O.default,{showSearch:!0,placeholder:"Type email to search for users",filterOption:!1,onSearch:e=>{eh(e)},onSelect:(e,s)=>ej(e,s),options:er,loading:ei,allowClear:!0,style:{width:"100%"},notFoundContent:ei?"Searching...":"No users found"}),(0,c.jsx)(R.ZP,{onClick:()=>ee(!0),style:{marginLeft:"8px"},children:"Create User"})]}),(0,c.jsx)("div",{className:"text-xs text-gray-500",children:"Search by email to find users"})]})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Team"," ",(0,c.jsx)(W.Z,{title:"The team this key belongs to, which determines available models and budget limits",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"team_id",initialValue:l?l.team_id:null,className:"mt-4",children:(0,c.jsx)(Q,{teams:t,onChange:e=>{Y((null==t?void 0:t.find(s=>s.team_id===e))||null)}})})]}),(0,c.jsxs)("div",{className:"mb-8",children:[(0,c.jsx)(E.Z,{className:"mb-4",children:"Key Details"}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["you"===b||"another_user"===b?"Key Name":"Service Account ID"," ",(0,c.jsx)(W.Z,{title:"you"===b||"another_user"===b?"A descriptive name to identify this key":"Unique identifier for this service account",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"key_alias",rules:[{required:!0,message:"Please input a ".concat("you"===b?"key name":"service account ID")}],help:"required",children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Models"," ",(0,c.jsx)(W.Z,{title:"Select which models this key can access. Choose 'All Team Models' to grant access to all models available to the team",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"models",rules:[{required:!0,message:"Please select a model"}],help:"required",className:"mt-4",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",style:{width:"100%"},onChange:e=>{e.includes("all-team-models")&&o.setFieldsValue({models:["all-team-models"]})},children:[(0,c.jsx)(ev,{value:"all-team-models",children:"All Team Models"},"all-team-models"),_.map(e=>(0,c.jsx)(ev,{value:e,children:K(e)},e))]})})]}),(0,c.jsx)("div",{className:"mb-8",children:(0,c.jsxs)(C.Z,{className:"mt-4 mb-4",children:[(0,c.jsx)(T.Z,{children:(0,c.jsx)(E.Z,{className:"m-0",children:"Optional Settings"})}),(0,c.jsxs)(I.Z,{children:[(0,c.jsx)(L.Z.Item,{className:"mt-4",label:(0,c.jsxs)("span",{children:["Max Budget (USD)"," ",(0,c.jsx)(W.Z,{title:"Maximum amount in USD this key can spend. When reached, the key will be blocked from making further requests",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"max_budget",help:"Budget cannot exceed team max budget: $".concat((null==l?void 0:l.max_budget)!==null&&(null==l?void 0:l.max_budget)!==void 0?null==l?void 0:l.max_budget:"unlimited"),rules:[{validator:async(e,s)=>{if(s&&l&&null!==l.max_budget&&s>l.max_budget)throw Error("Budget cannot exceed team max budget: $".concat(l.max_budget))}}],children:(0,c.jsx)(z,{step:.01,precision:2,width:200})}),(0,c.jsx)(L.Z.Item,{className:"mt-4",label:(0,c.jsxs)("span",{children:["Reset Budget"," ",(0,c.jsx)(W.Z,{title:"How often the budget should reset. For example, setting 'daily' will reset the budget every 24 hours",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"budget_duration",help:"Team Reset Budget: ".concat((null==l?void 0:l.budget_duration)!==null&&(null==l?void 0:l.budget_duration)!==void 0?null==l?void 0:l.budget_duration:"None"),children:(0,c.jsx)(e_,{onChange:e=>o.setFieldValue("budget_duration",e)})}),(0,c.jsx)(L.Z.Item,{className:"mt-4",label:(0,c.jsxs)("span",{children:["Tokens per minute Limit (TPM)"," ",(0,c.jsx)(W.Z,{title:"Maximum number of tokens this key can process per minute. Helps control usage and costs",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"tpm_limit",help:"TPM cannot exceed team TPM limit: ".concat((null==l?void 0:l.tpm_limit)!==null&&(null==l?void 0:l.tpm_limit)!==void 0?null==l?void 0:l.tpm_limit:"unlimited"),rules:[{validator:async(e,s)=>{if(s&&l&&null!==l.tpm_limit&&s>l.tpm_limit)throw Error("TPM limit cannot exceed team TPM limit: ".concat(l.tpm_limit))}}],children:(0,c.jsx)(z,{step:1,width:400})}),(0,c.jsx)(L.Z.Item,{className:"mt-4",label:(0,c.jsxs)("span",{children:["Requests per minute Limit (RPM)"," ",(0,c.jsx)(W.Z,{title:"Maximum number of API requests this key can make per minute. Helps prevent abuse and manage load",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"rpm_limit",help:"RPM cannot exceed team RPM limit: ".concat((null==l?void 0:l.rpm_limit)!==null&&(null==l?void 0:l.rpm_limit)!==void 0?null==l?void 0:l.rpm_limit:"unlimited"),rules:[{validator:async(e,s)=>{if(s&&l&&null!==l.rpm_limit&&s>l.rpm_limit)throw Error("RPM limit cannot exceed team RPM limit: ".concat(l.rpm_limit))}}],children:(0,c.jsx)(z,{step:1,width:400})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Expire Key"," ",(0,c.jsx)(W.Z,{title:"Set when this key should expire. Format: 30s (seconds), 30m (minutes), 30h (hours), 30d (days)",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"duration",className:"mt-4",children:(0,c.jsx)(S.Z,{placeholder:"e.g., 30d"})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Guardrails"," ",(0,c.jsx)(W.Z,{title:"Apply safety guardrails to this key to filter content or enforce policies",children:(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/guardrails/quick_start",target:"_blank",rel:"noopener noreferrer",onClick:e=>e.stopPropagation(),children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})})]}),name:"guardrails",className:"mt-4",help:"Select existing guardrails or enter new ones",children:(0,c.jsx)(O.default,{mode:"tags",style:{width:"100%"},placeholder:"Select or enter guardrails",options:B.map(e=>({value:e,label:e}))})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Metadata"," ",(0,c.jsx)(W.Z,{title:"JSON object with additional information about this key. Used for tracking or custom logic",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"metadata",className:"mt-4",children:(0,c.jsx)(q.default.TextArea,{rows:4,placeholder:"Enter metadata as JSON"})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Tags"," ",(0,c.jsx)(W.Z,{title:"Tags for tracking spend and/or doing tag-based routing. Used for analytics and filtering",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"tags",className:"mt-4",help:"Tags for tracking spend and/or doing tag-based routing.",children:(0,c.jsx)(O.default,{mode:"tags",style:{width:"100%"},placeholder:"Enter tags",tokenSeparators:[","],options:U})}),(0,c.jsxs)(C.Z,{className:"mt-4 mb-4",children:[(0,c.jsx)(T.Z,{children:(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsx)("b",{children:"Advanced Settings"}),(0,c.jsx)(W.Z,{title:(0,c.jsxs)("span",{children:["Learn more about advanced settings in our"," ",(0,c.jsx)("a",{href:v.H2?"".concat(v.H2,"/#/key%20management/generate_key_fn_key_generate_post"):"/#/key%20management/generate_key_fn_key_generate_post",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300",children:"documentation"})]}),children:(0,c.jsx)(J.Z,{className:"text-gray-400 hover:text-gray-300 cursor-help"})})]})}),(0,c.jsx)(I.Z,{children:(0,c.jsx)(X,{schemaComponent:"GenerateKeyRequest",form:o,excludedFields:["key_alias","team_id","models","duration","metadata","tags","guardrails","max_budget","budget_duration","tpm_limit","rpm_limit"]})})]})]})]})}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Create Key"})})]})}),$&&(0,c.jsx)(M.Z,{title:"Create New User",visible:$,onCancel:()=>ee(!1),footer:null,width:800,children:(0,c.jsx)(ex,{userID:s,accessToken:r,teams:t,possibleUIRoles:et,onUserCreated:e=>{el(e),o.setFieldsValue({user_id:e}),ee(!1)},isEmbedded:!0})}),x&&(0,c.jsx)(M.Z,{visible:m,onOk:ec,onCancel:ed,footer:null,children:(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 w-full",children:[(0,c.jsx)(E.Z,{children:"Save your Key"}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)("p",{children:["Please save this secret key somewhere safe and accessible. For security reasons, ",(0,c.jsx)("b",{children:"you will not be able to view it again"})," ","through your LiteLLM account. If you lose this secret key, you will need to generate a new one."]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:null!=x?(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"mt-3",children:"API Key:"}),(0,c.jsx)("div",{style:{background:"#f8f8f8",padding:"10px",borderRadius:"5px",marginBottom:"10px"},children:(0,c.jsx)("pre",{style:{wordWrap:"break-word",whiteSpace:"normal"},children:x})}),(0,c.jsx)(P.CopyToClipboard,{text:x,onCopy:()=>{D.ZP.success("API Key copied to clipboard")},children:(0,c.jsx)(k.Z,{className:"mt-3",children:"Copy API Key"})})]}):(0,c.jsx)(A.Z,{children:"Key being created, this might take 30s"})})]})})]})},ew=l(7366),ek=e=>{let{selectedTeam:s,currentOrg:l,selectedKeyAlias:t,accessToken:a,createClicked:r}=e,[n,i]=(0,d.useState)({keys:[],total_count:0,current_page:1,total_pages:0}),[o,c]=(0,d.useState)(!0),[m,u]=(0,d.useState)(null),x=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{if(console.log("calling fetchKeys"),!a){console.log("accessToken",a);return}c(!0);let s="number"==typeof e.page?e.page:1,l="number"==typeof e.pageSize?e.pageSize:100,t=await (0,v.OD)(a,null,null,null,null,null,s,l);console.log("data",t),i(t),u(null)}catch(e){u(e instanceof Error?e:Error("An error occurred"))}finally{c(!1)}};return(0,d.useEffect)(()=>{x(),console.log("selectedTeam",s,"currentOrg",l,"accessToken",a,"selectedKeyAlias",t)},[s,l,a,t,r]),{keys:n.keys,isLoading:o,error:m,pagination:{currentPage:n.current_page,totalPages:n.total_pages,totalCount:n.total_count},refresh:x,setKeys:e=>{i(s=>{let l="function"==typeof e?e(s.keys):e;return{...s,keys:l}})}}},eS=l(27281),eC=l(41649),eI=l(12514),eT=l(12485),eA=l(18135),eE=l(35242),eP=l(29706),eO=l(77991),eL=l(10900),eD=l(23628),eM=l(74998);function eF(e){var s,l;let{keyData:t,onCancel:a,onSubmit:r,teams:n,accessToken:i,userID:o,userRole:m}=e,[u]=L.Z.useForm(),[x,h]=(0,d.useState)([]),p=null==n?void 0:n.find(e=>e.team_id===t.team_id),[g,j]=(0,d.useState)([]);(0,d.useEffect)(()=>{(async()=>{if(o&&m&&i)try{if(null===t.team_id){let e=(await (0,v.So)(i,o,m)).data.map(e=>e.id);j(e)}else if(null==p?void 0:p.team_id){let e=await eb(o,m,i,p.team_id);j(Array.from(new Set([...p.models,...e])))}}catch(e){console.error("Error fetching models:",e)}})()},[o,m,i,p,t.team_id]);let f={...t,budget_duration:(l=t.budget_duration)&&({"24h":"daily","7d":"weekly","30d":"monthly"})[l]||null,metadata:t.metadata?JSON.stringify(t.metadata,null,2):"",guardrails:(null===(s=t.metadata)||void 0===s?void 0:s.guardrails)||[]};return(0,c.jsxs)(L.Z,{form:u,onFinish:r,initialValues:f,layout:"vertical",children:[(0,c.jsx)(L.Z.Item,{label:"Key Alias",name:"key_alias",children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"Models",name:"models",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",style:{width:"100%"},children:[g.length>0&&(0,c.jsx)(O.default.Option,{value:"all-team-models",children:"All Team Models"}),g.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:e},e))]})}),(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(z,{step:.01,style:{width:"100%"},placeholder:"Enter a numerical value"})}),(0,c.jsx)(L.Z.Item,{label:"Reset Budget",name:"budget_duration",children:(0,c.jsxs)(O.default,{placeholder:"n/a",children:[(0,c.jsx)(O.default.Option,{value:"daily",children:"Daily"}),(0,c.jsx)(O.default.Option,{value:"weekly",children:"Weekly"}),(0,c.jsx)(O.default.Option,{value:"monthly",children:"Monthly"})]})}),(0,c.jsx)(L.Z.Item,{label:"TPM Limit",name:"tpm_limit",children:(0,c.jsx)(z,{min:0})}),(0,c.jsx)(L.Z.Item,{label:"RPM Limit",name:"rpm_limit",children:(0,c.jsx)(z,{min:0})}),(0,c.jsx)(L.Z.Item,{label:"Max Parallel Requests",name:"max_parallel_requests",children:(0,c.jsx)(z,{min:0})}),(0,c.jsx)(L.Z.Item,{label:"Model TPM Limit",name:"model_tpm_limit",children:(0,c.jsx)(q.default.TextArea,{rows:4,placeholder:'{"gpt-4": 100, "claude-v1": 200}'})}),(0,c.jsx)(L.Z.Item,{label:"Model RPM Limit",name:"model_rpm_limit",children:(0,c.jsx)(q.default.TextArea,{rows:4,placeholder:'{"gpt-4": 100, "claude-v1": 200}'})}),(0,c.jsx)(L.Z.Item,{label:"Guardrails",name:"guardrails",children:(0,c.jsx)(O.default,{mode:"tags",style:{width:"100%"},placeholder:"Select or enter guardrails"})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",children:(0,c.jsx)(q.default.TextArea,{rows:10})}),(0,c.jsx)(L.Z.Item,{name:"token",hidden:!0,children:(0,c.jsx)(q.default,{})}),(0,c.jsxs)("div",{className:"flex justify-end gap-2 mt-6",children:[(0,c.jsx)(k.Z,{variant:"light",onClick:a,children:"Cancel"}),(0,c.jsx)(k.Z,{children:"Save Changes"})]})]})}function eR(e){let{selectedToken:s,visible:l,onClose:t,accessToken:a}=e,[r]=L.Z.useForm(),[n,i]=(0,d.useState)(null),[o,m]=(0,d.useState)(null),[u,x]=(0,d.useState)(null),[h,p]=(0,d.useState)(!1);(0,d.useEffect)(()=>{l&&s&&r.setFieldsValue({key_alias:s.key_alias,max_budget:s.max_budget,tpm_limit:s.tpm_limit,rpm_limit:s.rpm_limit,duration:s.duration||""})},[l,s,r]),(0,d.useEffect)(()=>{l||(i(null),p(!1),r.resetFields())},[l,r]),(0,d.useEffect)(()=>{(null==o?void 0:o.duration)?x((e=>{if(!e)return null;try{let s;let l=new Date;if(e.endsWith("s"))s=(0,ew.Z)(l,{seconds:parseInt(e)});else if(e.endsWith("h"))s=(0,ew.Z)(l,{hours:parseInt(e)});else if(e.endsWith("d"))s=(0,ew.Z)(l,{days:parseInt(e)});else throw Error("Invalid duration format");return s.toLocaleString()}catch(e){return null}})(o.duration)):x(null)},[null==o?void 0:o.duration]);let g=async()=>{if(s&&a){p(!0);try{let e=await r.validateFields(),l=await (0,v.s0)(a,s.token,e);i(l.key),D.ZP.success("API Key regenerated successfully")}catch(e){console.error("Error regenerating key:",e),D.ZP.error("Failed to regenerate API Key"),p(!1)}}},j=()=>{i(null),p(!1),r.resetFields(),t()};return(0,c.jsx)(M.Z,{title:"Regenerate API Key",open:l,onCancel:j,footer:n?[(0,c.jsx)(k.Z,{onClick:j,children:"Close"},"close")]:[(0,c.jsx)(k.Z,{onClick:j,className:"mr-2",children:"Cancel"},"cancel"),(0,c.jsx)(k.Z,{onClick:g,disabled:h,children:h?"Regenerating...":"Regenerate"},"regenerate")],children:n?(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 w-full",children:[(0,c.jsx)(E.Z,{children:"Regenerated Key"}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)("p",{children:["Please replace your old key with the new key generated. For security reasons, ",(0,c.jsx)("b",{children:"you will not be able to view it again"})," ","through your LiteLLM account. If you lose this secret key, you will need to generate a new one."]})}),(0,c.jsxs)(N.Z,{numColSpan:1,children:[(0,c.jsx)(A.Z,{className:"mt-3",children:"Key Alias:"}),(0,c.jsx)("div",{className:"bg-gray-100 p-2 rounded mb-2",children:(0,c.jsx)("pre",{className:"break-words whitespace-normal",children:(null==s?void 0:s.key_alias)||"No alias set"})}),(0,c.jsx)(A.Z,{className:"mt-3",children:"New API Key:"}),(0,c.jsx)("div",{className:"bg-gray-100 p-2 rounded mb-2",children:(0,c.jsx)("pre",{className:"break-words whitespace-normal",children:n})}),(0,c.jsx)(P.CopyToClipboard,{text:n,onCopy:()=>D.ZP.success("API Key copied to clipboard"),children:(0,c.jsx)(k.Z,{className:"mt-3",children:"Copy API Key"})})]})]}):(0,c.jsxs)(L.Z,{form:r,layout:"vertical",onValuesChange:e=>{"duration"in e&&m(s=>({...s,duration:e.duration}))},children:[(0,c.jsx)(L.Z.Item,{name:"key_alias",label:"Key Alias",children:(0,c.jsx)(S.Z,{disabled:!0})}),(0,c.jsx)(L.Z.Item,{name:"max_budget",label:"Max Budget (USD)",children:(0,c.jsx)(H.Z,{step:.01,precision:2,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{name:"tpm_limit",label:"TPM Limit",children:(0,c.jsx)(H.Z,{style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{name:"rpm_limit",label:"RPM Limit",children:(0,c.jsx)(H.Z,{style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{name:"duration",label:"Expire Key (eg: 30s, 30h, 30d)",className:"mt-8",children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:["Current expiry: ",(null==s?void 0:s.expires)?new Date(s.expires).toLocaleString():"Never"]}),u&&(0,c.jsxs)("div",{className:"mt-2 text-sm text-green-600",children:["New expiry: ",u]})]})})}function eq(e){var s,l;let{keyId:t,onClose:a,keyData:r,accessToken:n,userID:i,userRole:o,teams:m,onKeyDataUpdate:u,onDelete:x}=e,[h,p]=(0,d.useState)(!1),[g]=L.Z.useForm(),[j,f]=(0,d.useState)(!1),[_,y]=(0,d.useState)(!1);if(!r)return(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsx)(k.Z,{icon:eL.Z,variant:"light",onClick:a,className:"mb-4",children:"Back to Keys"}),(0,c.jsx)(A.Z,{children:"Key not found"})]});let b=async e=>{try{var s,l;if(!n)return;let t=e.token;if(e.key=t,e.metadata&&"string"==typeof e.metadata)try{let l=JSON.parse(e.metadata);e.metadata={...l,...(null===(s=e.guardrails)||void 0===s?void 0:s.length)>0?{guardrails:e.guardrails}:{}}}catch(e){console.error("Error parsing metadata JSON:",e),D.ZP.error("Invalid metadata JSON");return}else e.metadata={...e.metadata||{},...(null===(l=e.guardrails)||void 0===l?void 0:l.length)>0?{guardrails:e.guardrails}:{}};e.budget_duration&&(e.budget_duration=({daily:"24h",weekly:"7d",monthly:"30d"})[e.budget_duration]);let a=await (0,v.Nc)(n,e);u&&u(a),D.ZP.success("Key updated successfully"),p(!1)}catch(e){D.ZP.error("Failed to update key"),console.error("Error updating key:",e)}},Z=async()=>{try{if(!n)return;await (0,v.I1)(n,r.token),D.ZP.success("Key deleted successfully"),x&&x(),a()}catch(e){console.error("Error deleting the key:",e),D.ZP.error("Failed to delete key")}};return(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(k.Z,{icon:eL.Z,variant:"light",onClick:a,className:"mb-4",children:"Back to Keys"}),(0,c.jsx)(E.Z,{children:r.key_alias||"API Key"}),(0,c.jsx)(A.Z,{className:"text-gray-500 font-mono",children:r.token})]}),o&&eg.LQ.includes(o)&&(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(k.Z,{icon:eD.Z,variant:"secondary",onClick:()=>y(!0),className:"flex items-center",children:"Regenerate Key"}),(0,c.jsx)(k.Z,{icon:eM.Z,variant:"secondary",onClick:()=>f(!0),className:"flex items-center",children:"Delete Key"})]})]}),(0,c.jsx)(eR,{selectedToken:r,visible:_,onClose:()=>y(!1),accessToken:n}),j&&(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Key"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this key?"})})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:Z,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:()=>f(!1),children:"Cancel"})]})]})]})}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{className:"mb-4",children:[(0,c.jsx)(eT.Z,{children:"Overview"}),(0,c.jsx)(eT.Z,{children:"Settings"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:1,numItemsSm:2,numItemsLg:3,className:"gap-6",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Spend"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(E.Z,{children:["$",Number(r.spend).toFixed(4)]}),(0,c.jsxs)(A.Z,{children:["of ",null!==r.max_budget?"$".concat(r.max_budget):"Unlimited"]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Rate Limits"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(A.Z,{children:["TPM: ",null!==r.tpm_limit?r.tpm_limit:"Unlimited"]}),(0,c.jsxs)(A.Z,{children:["RPM: ",null!==r.rpm_limit?r.rpm_limit:"Unlimited"]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Models"}),(0,c.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:r.models&&r.models.length>0?r.models.map((e,s)=>(0,c.jsx)(eC.Z,{color:"red",children:e},s)):(0,c.jsx)(A.Z,{children:"No models specified"})})]})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,c.jsx)(E.Z,{children:"Key Settings"}),!h&&o&&eg.LQ.includes(o)&&(0,c.jsx)(k.Z,{variant:"light",onClick:()=>p(!0),children:"Edit Settings"})]}),h?(0,c.jsx)(eF,{keyData:r,onCancel:()=>p(!1),onSubmit:b,teams:m,accessToken:n,userID:i,userRole:o}):(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Key ID"}),(0,c.jsx)(A.Z,{className:"font-mono",children:r.token})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Key Alias"}),(0,c.jsx)(A.Z,{children:r.key_alias||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Secret Key"}),(0,c.jsx)(A.Z,{className:"font-mono",children:r.key_name})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Team ID"}),(0,c.jsx)(A.Z,{children:r.team_id||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Organization"}),(0,c.jsx)(A.Z,{children:r.organization_id||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Created"}),(0,c.jsx)(A.Z,{children:new Date(r.created_at).toLocaleString()})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Expires"}),(0,c.jsx)(A.Z,{children:r.expires?new Date(r.expires).toLocaleString():"Never"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Spend"}),(0,c.jsxs)(A.Z,{children:["$",Number(r.spend).toFixed(4)," USD"]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Budget"}),(0,c.jsx)(A.Z,{children:null!==r.max_budget?"$".concat(r.max_budget," USD"):"Unlimited"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Models"}),(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:r.models&&r.models.length>0?r.models.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:e},s)):(0,c.jsx)(A.Z,{children:"No models specified"})})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Rate Limits"}),(0,c.jsxs)(A.Z,{children:["TPM: ",null!==r.tpm_limit?r.tpm_limit:"Unlimited"]}),(0,c.jsxs)(A.Z,{children:["RPM: ",null!==r.rpm_limit?r.rpm_limit:"Unlimited"]}),(0,c.jsxs)(A.Z,{children:["Max Parallel Requests: ",null!==r.max_parallel_requests?r.max_parallel_requests:"Unlimited"]}),(0,c.jsxs)(A.Z,{children:["Model TPM Limits: ",(null===(s=r.metadata)||void 0===s?void 0:s.model_tpm_limit)?JSON.stringify(r.metadata.model_tpm_limit):"Unlimited"]}),(0,c.jsxs)(A.Z,{children:["Model RPM Limits: ",(null===(l=r.metadata)||void 0===l?void 0:l.model_rpm_limit)?JSON.stringify(r.metadata.model_rpm_limit):"Unlimited"]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Metadata"}),(0,c.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto mt-1",children:JSON.stringify(r.metadata,null,2)})]})]})]})})]})]})]})}var eU=l(2356),ez=e=>{let{options:s,onApplyFilters:l,onResetFilters:t,initialValues:a={},buttonLabel:r="Filters"}=e,[n,i]=(0,d.useState)(!1),[o,m]=(0,d.useState)(a),[u,x]=(0,d.useState)({}),[h,p]=(0,d.useState)({}),[g,j]=(0,d.useState)({}),f=(0,d.useCallback)(ep()(async(e,s)=>{if(s.isSearchable&&s.searchFn){p(e=>({...e,[s.name]:!0}));try{let l=await s.searchFn(e);x(e=>({...e,[s.name]:l}))}catch(e){console.error("Error searching:",e),x(e=>({...e,[s.name]:[]}))}finally{p(e=>({...e,[s.name]:!1}))}}},300),[]),_=(e,s)=>{let t={...o,[e]:s};m(t),l(t)};return(0,c.jsxs)("div",{className:"w-full",children:[(0,c.jsxs)("div",{className:"flex items-center gap-2 mb-6",children:[(0,c.jsx)(R.ZP,{icon:(0,c.jsx)(eU.Z,{className:"h-4 w-4"}),onClick:()=>i(!n),className:"flex items-center gap-2",children:r}),(0,c.jsx)(R.ZP,{onClick:()=>{let e={};s.forEach(s=>{e[s.name]=""}),m(e),t()},children:"Reset Filters"})]}),n&&(0,c.jsx)("div",{className:"grid grid-cols-3 gap-x-6 gap-y-4 mb-6",children:["Team ID","Organization ID","Key Alias","User ID","Key Hash"].map(e=>{let l=s.find(s=>s.label===e||s.name===e);return l?(0,c.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,c.jsx)("label",{className:"text-sm text-gray-600",children:l.label||l.name}),l.isSearchable?(0,c.jsx)(O.default,{showSearch:!0,className:"w-full",placeholder:"Search ".concat(l.label||l.name,"..."),value:o[l.name]||void 0,onChange:e=>_(l.name,e),onSearch:e=>{j(s=>({...s,[l.name]:e})),l.searchFn&&f(e,l)},filterOption:!1,loading:h[l.name],options:u[l.name]||[],allowClear:!0}):(0,c.jsx)(q.default,{className:"w-full",placeholder:"Enter ".concat(l.label||l.name,"..."),value:o[l.name]||"",onChange:e=>_(l.name,e.target.value),allowClear:!0})]},l.name):null})})]})},eV=l(16593);let eK=async e=>{if(!e)return[];try{let s=[],l=1,t=!0;for(;t;){let a=await (0,v.OD)(e,null,"",null,null,null,l,100),r=a.keys.map(e=>e.key_alias).filter(Boolean);s=[...s,...r],l<a.total_pages?l++:t=!1}return Array.from(new Set(s))}catch(e){return console.error("Error fetching all key aliases:",e),[]}},eB=async(e,s)=>{if(!e)return[];try{let l=[],t=1,a=!0;for(;a;){let r=await (0,v.It)(e,s||null,null);l=[...l,...r],t<r.total_pages?t++:a=!1}return l}catch(e){return console.error("Error fetching all teams:",e),[]}},eH=async e=>{if(!e)return[];try{let s=[],l=1,t=!0;for(;t;){let a=await (0,v.r6)(e);s=[...s,...a],l<a.total_pages?l++:t=!1}return s}catch(e){return console.error("Error fetching all organizations:",e),[]}},eJ=()=>{let[e,s]=(0,d.useState)("http://localhost:4000");return(0,d.useEffect)(()=>{{let{protocol:e,host:l}=window.location;s("".concat(e,"//").concat(l))}},[]),e};function eW(e,s){let l=structuredClone(e);for(let[e,t]of Object.entries(s))e in l&&(l[e]=t);return l}var eG=l(71594),eY=l(24525),e$=l(21626),eX=l(97214),eQ=l(28241),e0=l(58834),e1=l(69552),e2=l(71876),e4=l(44633),e5=l(86462),e3=l(49084);function e6(e){let{keys:s,setKeys:l,isLoading:t=!1,pagination:a,onPageChange:r,pageSize:n=50,teams:i,selectedTeam:o,setSelectedTeam:m,selectedKeyAlias:u,setSelectedKeyAlias:x,accessToken:h,userID:p,userRole:g,organizations:j,setCurrentOrg:f,refresh:_,onSortChange:y,currentSort:b}=e,[Z,N]=(0,d.useState)(null),[w,S]=(0,d.useState)([]),[C,I]=d.useState(()=>b?[{id:b.sortBy,desc:"desc"===b.sortOrder}]:[{id:"created_at",desc:!0}]),{filters:T,filteredKeys:A,allKeyAliases:E,allTeams:P,allOrganizations:O,handleFilterChange:L,handleFilterReset:D}=function(e){let{keys:s,teams:l,organizations:t,accessToken:a}=e,r={"Team ID":"","Organization ID":"","Key Alias":"","User ID":"","Sort By":"created_at","Sort Order":"desc"},[n,i]=(0,d.useState)(r),[o,c]=(0,d.useState)(l||[]),[m,u]=(0,d.useState)(t||[]),[x,h]=(0,d.useState)(s),p=(0,d.useRef)(0),g=(0,d.useCallback)(ep()(async e=>{if(!a)return;let s=Date.now();p.current=s;try{let l=await (0,v.OD)(a,e["Organization ID"]||null,e["Team ID"]||null,e["Key Alias"]||null,e["User ID"]||null,e["Key Hash"]||null,1,25,e["Sort By"]||null,e["Sort Order"]||null);s===p.current&&l&&(h(l.keys),console.log("called from debouncedSearch filters:",JSON.stringify(e)),console.log("called from debouncedSearch data:",JSON.stringify(l)))}catch(e){console.error("Error searching users:",e)}},300),[a]);(0,d.useEffect)(()=>{if(!s){h([]);return}let e=[...s];n["Team ID"]&&(e=e.filter(e=>e.team_id===n["Team ID"])),n["Organization ID"]&&(e=e.filter(e=>e.organization_id===n["Organization ID"])),h(e)},[s,n]),(0,d.useEffect)(()=>{let e=async()=>{let e=await eB(a);e.length>0&&c(e);let s=await eH(a);s.length>0&&u(s)};a&&e()},[a]);let j=(0,eV.a)({queryKey:["allKeys"],queryFn:async()=>{if(!a)throw Error("Access token required");return await eK(a)},enabled:!!a}).data||[];return(0,d.useEffect)(()=>{l&&l.length>0&&c(e=>e.length<l.length?l:e)},[l]),(0,d.useEffect)(()=>{t&&t.length>0&&u(e=>e.length<t.length?t:e)},[t]),{filters:n,filteredKeys:x,allKeyAliases:j,allTeams:o,allOrganizations:m,handleFilterChange:e=>{i({"Team ID":e["Team ID"]||"","Organization ID":e["Organization ID"]||"","Key Alias":e["Key Alias"]||"","User ID":e["User ID"]||"","Sort By":e["Sort By"]||"created_at","Sort Order":e["Sort Order"]||"desc"}),g({...n,...e})},handleFilterReset:()=>{i(r),g(r)}}}({keys:s,teams:i,organizations:j,accessToken:h});(0,d.useEffect)(()=>{if(h){let e=s.map(e=>e.user_id).filter(e=>null!==e);(async()=>{S((await (0,v.Of)(h,e,1,100)).users)})()}},[h,s]),(0,d.useEffect)(()=>{if(_){let e=()=>{_()};return window.addEventListener("storage",e),()=>{window.removeEventListener("storage",e)}}},[_]);let M=[{id:"expander",header:()=>null,cell:e=>{let{row:s}=e;return s.getCanExpand()?(0,c.jsx)("button",{onClick:s.getToggleExpandedHandler(),style:{cursor:"pointer"},children:s.getIsExpanded()?"▼":"▶"}):null}},{id:"token",accessorKey:"token",header:"Key ID",cell:e=>(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsx)(W.Z,{title:e.getValue(),children:(0,c.jsx)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left overflow-hidden truncate max-w-[200px]",onClick:()=>N(e.getValue()),children:e.getValue()?"".concat(e.getValue().slice(0,7),"..."):"-"})})})},{id:"key_alias",accessorKey:"key_alias",header:"Key Alias",cell:e=>{let s=e.getValue();return(0,c.jsx)(W.Z,{title:s,children:s?s.length>20?"".concat(s.slice(0,20),"..."):s:"-"})}},{id:"key_name",accessorKey:"key_name",header:"Secret Key",cell:e=>(0,c.jsx)("span",{className:"font-mono text-xs",children:e.getValue()})},{id:"team_alias",accessorKey:"team_id",header:"Team Alias",cell:e=>{let{row:s,getValue:l}=e,t=l(),a=null==i?void 0:i.find(e=>e.team_id===t);return(null==a?void 0:a.team_alias)||"Unknown"}},{id:"team_id",accessorKey:"team_id",header:"Team ID",cell:e=>(0,c.jsx)(W.Z,{title:e.getValue(),children:e.getValue()?"".concat(e.getValue().slice(0,7),"..."):"-"})},{id:"organization_id",accessorKey:"organization_id",header:"Organization ID",cell:e=>e.getValue()?e.renderValue():"-"},{id:"user_email",accessorKey:"user_id",header:"User Email",cell:e=>{let s=e.getValue(),l=w.find(e=>e.user_id===s);return(null==l?void 0:l.user_email)?l.user_email:"-"}},{id:"user_id",accessorKey:"user_id",header:"User ID",cell:e=>{let s=e.getValue();return s?(0,c.jsx)(W.Z,{title:s,children:(0,c.jsxs)("span",{children:[s.slice(0,7),"..."]})}):"-"}},{id:"created_at",accessorKey:"created_at",header:"Created At",cell:e=>{let s=e.getValue();return s?new Date(s).toLocaleDateString():"-"}},{id:"created_by",accessorKey:"created_by",header:"Created By",cell:e=>e.getValue()||"Unknown"},{id:"expires",accessorKey:"expires",header:"Expires",cell:e=>{let s=e.getValue();return s?new Date(s).toLocaleDateString():"Never"}},{id:"spend",accessorKey:"spend",header:"Spend (USD)",cell:e=>Number(e.getValue()).toFixed(4)},{id:"max_budget",accessorKey:"max_budget",header:"Budget (USD)",cell:e=>null!==e.getValue()&&void 0!==e.getValue()?e.getValue():"Unlimited"},{id:"budget_reset_at",accessorKey:"budget_reset_at",header:"Budget Reset",cell:e=>{let s=e.getValue();return s?new Date(s).toLocaleString():"Never"}},{id:"models",accessorKey:"models",header:"Models",cell:e=>{let s=e.getValue();return(0,c.jsx)("div",{className:"flex flex-wrap gap-1",children:s&&s.length>0?s.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:e},s)):"-"})}},{id:"rate_limits",header:"Rate Limits",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{children:["TPM: ",null!==l.tpm_limit?l.tpm_limit:"Unlimited"]}),(0,c.jsxs)("div",{children:["RPM: ",null!==l.rpm_limit?l.rpm_limit:"Unlimited"]})]})}}];console.log("keys: ".concat(JSON.stringify(s)));let F=(0,eG.b7)({data:A,columns:M.filter(e=>"expander"!==e.id),state:{sorting:C},onSortingChange:e=>{let s="function"==typeof e?e(C):e;if(console.log("newSorting: ".concat(JSON.stringify(s))),I(s),s&&s.length>0){let e=s[0],l=e.id,t=e.desc?"desc":"asc";console.log("sortBy: ".concat(l,", sortOrder: ").concat(t)),L({...T,"Sort By":l,"Sort Order":t}),null==y||y(l,t)}},getCoreRowModel:(0,eY.sC)(),getSortedRowModel:(0,eY.tj)(),enableSorting:!0,manualSorting:!1});return d.useEffect(()=>{b&&I([{id:b.sortBy,desc:"desc"===b.sortOrder}])},[b]),(0,c.jsx)("div",{className:"w-full h-full overflow-hidden",children:Z?(0,c.jsx)(eq,{keyId:Z,onClose:()=>N(null),keyData:A.find(e=>e.token===Z),onKeyDataUpdate:e=>{l(s=>s.map(s=>s.token===e.token?eW(s,e):s))},onDelete:()=>{l(e=>e.filter(e=>e.token!==Z))},accessToken:h,userID:p,userRole:g,teams:P}):(0,c.jsxs)("div",{className:"border-b py-4 flex-1 overflow-hidden",children:[(0,c.jsx)("div",{className:"w-full mb-6",children:(0,c.jsx)(ez,{options:[{name:"Team ID",label:"Team ID",isSearchable:!0,searchFn:async e=>P&&0!==P.length?P.filter(s=>s.team_id.toLowerCase().includes(e.toLowerCase())||s.team_alias&&s.team_alias.toLowerCase().includes(e.toLowerCase())).map(e=>({label:"".concat(e.team_alias||e.team_id," (").concat(e.team_id,")"),value:e.team_id})):[]},{name:"Organization ID",label:"Organization ID",isSearchable:!0,searchFn:async e=>O&&0!==O.length?O.filter(s=>{var l,t;return null!==(t=null===(l=s.organization_id)||void 0===l?void 0:l.toLowerCase().includes(e.toLowerCase()))&&void 0!==t&&t}).filter(e=>null!==e.organization_id&&void 0!==e.organization_id).map(e=>({label:"".concat(e.organization_id||"Unknown"," (").concat(e.organization_id,")"),value:e.organization_id})):[]},{name:"Key Alias",label:"Key Alias",isSearchable:!0,searchFn:async e=>E.filter(s=>s.toLowerCase().includes(e.toLowerCase())).map(e=>({label:e,value:e}))},{name:"User ID",label:"User ID",isSearchable:!1},{name:"Key Hash",label:"Key Hash",isSearchable:!1}],onApplyFilters:L,initialValues:T,onResetFilters:D})}),(0,c.jsxs)("div",{className:"flex items-center justify-between w-full mb-4",children:[(0,c.jsxs)("span",{className:"inline-flex text-sm text-gray-700",children:["Showing ",t?"...":"".concat((a.currentPage-1)*n+1," - ").concat(Math.min(a.currentPage*n,a.totalCount))," of ",t?"...":a.totalCount," results"]}),(0,c.jsxs)("div",{className:"inline-flex items-center gap-2",children:[(0,c.jsxs)("span",{className:"text-sm text-gray-700",children:["Page ",t?"...":a.currentPage," of ",t?"...":a.totalPages]}),(0,c.jsx)("button",{onClick:()=>r(a.currentPage-1),disabled:t||1===a.currentPage,className:"px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,c.jsx)("button",{onClick:()=>r(a.currentPage+1),disabled:t||a.currentPage===a.totalPages,className:"px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]}),(0,c.jsx)("div",{className:"h-[75vh] overflow-auto",children:(0,c.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,c.jsx)("div",{className:"overflow-x-auto",children:(0,c.jsxs)(e$.Z,{className:"[&_td]:py-0.5 [&_th]:py-1",children:[(0,c.jsx)(e0.Z,{children:F.getHeaderGroups().map(e=>(0,c.jsx)(e2.Z,{children:e.headers.map(e=>(0,c.jsx)(e1.Z,{className:"py-1 h-8 ".concat("actions"===e.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),onClick:e.column.getToggleSortingHandler(),children:(0,c.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,c.jsx)("div",{className:"flex items-center",children:e.isPlaceholder?null:(0,eG.ie)(e.column.columnDef.header,e.getContext())}),"actions"!==e.id&&(0,c.jsx)("div",{className:"w-4",children:e.column.getIsSorted()?({asc:(0,c.jsx)(e4.Z,{className:"h-4 w-4 text-blue-500"}),desc:(0,c.jsx)(e5.Z,{className:"h-4 w-4 text-blue-500"})})[e.column.getIsSorted()]:(0,c.jsx)(e3.Z,{className:"h-4 w-4 text-gray-400"})})]})},e.id))},e.id))}),(0,c.jsx)(eX.Z,{children:t?(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:M.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"\uD83D\uDE85 Loading keys..."})})})}):A.length>0?F.getRowModel().rows.map(e=>(0,c.jsx)(e2.Z,{className:"h-8",children:e.getVisibleCells().map(e=>(0,c.jsx)(eQ.Z,{className:"py-0.5 max-h-8 overflow-hidden text-ellipsis whitespace-nowrap ".concat("actions"===e.column.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),children:(0,eG.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:M.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"No keys found"})})})})})]})})})})]})})}console.log=function(){};var e8=e=>{let{userID:s,userRole:l,accessToken:t,selectedTeam:a,setSelectedTeam:r,data:n,setData:i,teams:o,premiumUser:m,currentOrg:u,organizations:x,setCurrentOrg:h,selectedKeyAlias:p,setSelectedKeyAlias:g,createClicked:j}=e,[f,_]=(0,d.useState)(!1),[y,b]=(0,d.useState)(!1),[Z,C]=(0,d.useState)(null),[I,T]=(0,d.useState)(null),[O,F]=(0,d.useState)(null),[R,q]=(0,d.useState)((null==a?void 0:a.team_id)||"");(0,d.useEffect)(()=>{q((null==a?void 0:a.team_id)||"")},[a]);let{keys:U,isLoading:z,error:K,pagination:B,refresh:J,setKeys:W}=ek({selectedTeam:a,currentOrg:u,selectedKeyAlias:p,accessToken:t,createClicked:j}),[G,Y]=(0,d.useState)(!1),[$,X]=(0,d.useState)(!1),[Q,ee]=(0,d.useState)(null),[es,el]=(0,d.useState)([]),et=new Set,[ea,er]=(0,d.useState)(!1),[en,ei]=(0,d.useState)(!1),[eo,ec]=(0,d.useState)(null),[ed,em]=(0,d.useState)(null),[eu]=L.Z.useForm(),[ex,eh]=(0,d.useState)(null),[ep,eg]=(0,d.useState)(et),[ej,ef]=(0,d.useState)([]);(0,d.useEffect)(()=>{console.log("in calculateNewExpiryTime for selectedToken",Q),(null==ed?void 0:ed.duration)?eh((e=>{if(!e)return null;try{let s;let l=new Date;if(e.endsWith("s"))s=(0,ew.Z)(l,{seconds:parseInt(e)});else if(e.endsWith("h"))s=(0,ew.Z)(l,{hours:parseInt(e)});else if(e.endsWith("d"))s=(0,ew.Z)(l,{days:parseInt(e)});else throw Error("Invalid duration format");return s.toLocaleString("en-US",{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:!0})}catch(e){return null}})(ed.duration)):eh(null),console.log("calculateNewExpiryTime:",ex)},[Q,null==ed?void 0:ed.duration]),(0,d.useEffect)(()=>{(async()=>{try{if(null===s||null===l||null===t)return;let e=await V(s,l,t);e&&el(e)}catch(e){console.error("Error fetching user models:",e)}})()},[t,s,l]),(0,d.useEffect)(()=>{if(o){let e=new Set;o.forEach((s,l)=>{let t=s.team_id;e.add(t)}),eg(e)}},[o]);let e_=async()=>{if(null!=Z&&null!=n){try{await (0,v.I1)(t,Z);let e=n.filter(e=>e.token!==Z);i(e)}catch(e){console.error("Error deleting the key:",e)}b(!1),C(null)}},ev=(e,s)=>{em(l=>({...l,[e]:s}))},ey=async()=>{if(!m){D.ZP.error("Regenerate API Key is an Enterprise feature. Please upgrade to use this feature.");return}if(null!=Q)try{let e=await eu.validateFields(),s=await (0,v.s0)(t,Q.token,e);if(ec(s.key),n){let l=n.map(l=>l.token===(null==Q?void 0:Q.token)?{...l,key_name:s.key_name,...e}:l);i(l)}ei(!1),eu.resetFields(),D.ZP.success("API Key regenerated successfully")}catch(e){console.error("Error regenerating key:",e),D.ZP.error("Failed to regenerate API Key")}};return(0,c.jsxs)("div",{children:[(0,c.jsx)(e6,{keys:U,setKeys:W,isLoading:z,pagination:B,onPageChange:e=>{J({page:e})},pageSize:100,teams:o,selectedTeam:a,setSelectedTeam:r,accessToken:t,userID:s,userRole:l,organizations:x,setCurrentOrg:h,refresh:J,selectedKeyAlias:p,setSelectedKeyAlias:g}),y&&(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Key"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this key ?"})})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:e_,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:()=>{b(!1),C(null)},children:"Cancel"})]})]})]})}),(0,c.jsx)(M.Z,{title:"Regenerate API Key",visible:en,onCancel:()=>{ei(!1),eu.resetFields()},footer:[(0,c.jsx)(k.Z,{onClick:()=>{ei(!1),eu.resetFields()},className:"mr-2",children:"Cancel"},"cancel"),(0,c.jsx)(k.Z,{onClick:ey,disabled:!m,children:m?"Regenerate":"Upgrade to Regenerate"},"regenerate")],children:m?(0,c.jsxs)(L.Z,{form:eu,layout:"vertical",onValuesChange:(e,s)=>{"duration"in e&&ev("duration",e.duration)},children:[(0,c.jsx)(L.Z.Item,{name:"key_alias",label:"Key Alias",children:(0,c.jsx)(S.Z,{disabled:!0})}),(0,c.jsx)(L.Z.Item,{name:"max_budget",label:"Max Budget (USD)",children:(0,c.jsx)(H.Z,{step:.01,precision:2,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{name:"tpm_limit",label:"TPM Limit",children:(0,c.jsx)(H.Z,{style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{name:"rpm_limit",label:"RPM Limit",children:(0,c.jsx)(H.Z,{style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{name:"duration",label:"Expire Key (eg: 30s, 30h, 30d)",className:"mt-8",children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:["Current expiry:"," ",(null==Q?void 0:Q.expires)!=null?new Date(Q.expires).toLocaleString():"Never"]}),ex&&(0,c.jsxs)("div",{className:"mt-2 text-sm text-green-600",children:["New expiry: ",ex]})]}):(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"mb-2 text-gray-500 italic text-[12px]",children:"Upgrade to use this feature"}),(0,c.jsx)(k.Z,{variant:"primary",className:"mb-2",children:(0,c.jsx)("a",{href:"https://calendly.com/d/4mp-gd3-k5k/litellm-1-1-onboarding-chat",target:"_blank",children:"Get Free Trial"})})]})}),eo&&(0,c.jsx)(M.Z,{visible:!!eo,onCancel:()=>ec(null),footer:[(0,c.jsx)(k.Z,{onClick:()=>ec(null),children:"Close"},"close")],children:(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 w-full",children:[(0,c.jsx)(E.Z,{children:"Regenerated Key"}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)("p",{children:["Please replace your old key with the new key generated. For security reasons, ",(0,c.jsx)("b",{children:"you will not be able to view it again"})," ","through your LiteLLM account. If you lose this secret key, you will need to generate a new one."]})}),(0,c.jsxs)(N.Z,{numColSpan:1,children:[(0,c.jsx)(A.Z,{className:"mt-3",children:"Key Alias:"}),(0,c.jsx)("div",{style:{background:"#f8f8f8",padding:"10px",borderRadius:"5px",marginBottom:"10px"},children:(0,c.jsx)("pre",{style:{wordWrap:"break-word",whiteSpace:"normal"},children:(null==Q?void 0:Q.key_alias)||"No alias set"})}),(0,c.jsx)(A.Z,{className:"mt-3",children:"New API Key:"}),(0,c.jsx)("div",{style:{background:"#f8f8f8",padding:"10px",borderRadius:"5px",marginBottom:"10px"},children:(0,c.jsx)("pre",{style:{wordWrap:"break-word",whiteSpace:"normal"},children:eo})}),(0,c.jsx)(P.CopyToClipboard,{text:eo,onCopy:()=>D.ZP.success("API Key copied to clipboard"),children:(0,c.jsx)(k.Z,{className:"mt-3",children:"Copy API Key"})})]})]})})]})},e7=l(12011);console.log=function(){},console.log("isLocal:",!1);var e9=e=>{let{userID:s,userRole:l,teams:t,keys:a,setUserRole:r,userEmail:n,setUserEmail:i,setTeams:o,setKeys:x,premiumUser:h,organizations:p,addKey:g,createClicked:j}=e,[f,y]=(0,d.useState)(null),[b,k]=(0,d.useState)(null),S=(0,m.useSearchParams)(),C=function(e){console.log("COOKIES",document.cookie);let s=document.cookie.split("; ").find(s=>s.startsWith(e+"="));return s?s.split("=")[1]:null}("token"),I=S.get("invitation_id"),[T,A]=(0,d.useState)(null),[E,P]=(0,d.useState)(null),[O,L]=(0,d.useState)([]),[D,M]=(0,d.useState)(null),[F,R]=(0,d.useState)(null),[q,U]=(0,d.useState)(null);if(window.addEventListener("beforeunload",function(){sessionStorage.clear()}),(0,d.useEffect)(()=>{if(C){let e=(0,u.o)(C);if(e){if(console.log("Decoded token:",e),console.log("Decoded key:",e.key),A(e.key),e.user_role){let s=function(e){if(!e)return"Undefined Role";switch(console.log("Received user role: ".concat(e)),e.toLowerCase()){case"app_owner":case"demo_app_owner":return"App Owner";case"app_admin":case"proxy_admin":return"Admin";case"proxy_admin_viewer":return"Admin Viewer";case"app_user":return"App User";case"internal_user":return"Internal User";case"internal_user_viewer":return"Internal Viewer";default:return"Unknown Role"}}(e.user_role);console.log("Decoded user_role:",s),r(s)}else console.log("User role not defined");e.user_email?i(e.user_email):console.log("User Email is not set ".concat(e))}}if(s&&T&&l&&!a&&!f){let e=sessionStorage.getItem("userModels"+s);e?L(JSON.parse(e)):(console.log("currentOrg: ".concat(JSON.stringify(b))),(async()=>{try{let e=await (0,v.g)(T);M(e);let t=await (0,v.Br)(T,s,l,!1,null,null);y(t.user_info),console.log("userSpendData: ".concat(JSON.stringify(f))),(null==t?void 0:t.teams[0].keys)?x(t.keys.concat(t.teams.filter(e=>"Admin"===l||e.user_id===s).flatMap(e=>e.keys))):x(t.keys),sessionStorage.setItem("userData"+s,JSON.stringify(t.keys)),sessionStorage.setItem("userSpendData"+s,JSON.stringify(t.user_info));let a=(await (0,v.So)(T,s,l)).data.map(e=>e.id);console.log("available_model_names:",a),L(a),console.log("userModels:",O),sessionStorage.setItem("userModels"+s,JSON.stringify(a))}catch(e){console.error("There was an error fetching the data",e),e.message.includes("Invalid proxy server token passed")&&z()}})(),Z(T,s,l,b,o))}},[s,C,T,a,l]),(0,d.useEffect)(()=>{T&&(async()=>{try{let e=await (0,v.e2)(T,[T]);console.log("keyInfo: ",e)}catch(e){e.message.includes("Invalid proxy server token passed")&&z()}})()},[T]),(0,d.useEffect)(()=>{console.log("currentOrg: ".concat(JSON.stringify(b),", accessToken: ").concat(T,", userID: ").concat(s,", userRole: ").concat(l)),T&&(console.log("fetching teams"),Z(T,s,l,b,o))},[b]),(0,d.useEffect)(()=>{if(null!==a&&null!=F&&null!==F.team_id){let e=0;for(let s of(console.log("keys: ".concat(JSON.stringify(a))),a))F.hasOwnProperty("team_id")&&null!==s.team_id&&s.team_id===F.team_id&&(e+=s.spend);console.log("sum: ".concat(e)),P(e)}else if(null!==a){let e=0;for(let s of a)e+=s.spend;P(e)}},[F]),null!=I)return(0,c.jsx)(e7.default,{});function z(){(0,_.b)();let e="/sso/key/generate";return console.log("Full URL:",e),window.location.href=e,null}if(null==C)return console.log("All cookies before redirect:",document.cookie),z(),null;try{let e=(0,u.o)(C);console.log("Decoded token:",e);let s=e.exp,l=Math.floor(Date.now()/1e3);if(s&&l>=s){console.log("Token expired, redirecting to login"),(0,_.b)();let e="/sso/key/generate";return console.log("Full URL for expired token:",e),window.location.href=e,null}}catch(s){console.error("Error decoding token:",s),(0,_.b)();let e="/sso/key/generate";return console.log("Full URL after token decode error:",e),window.location.href=e,null}if(null==T)return null;if(null==s)return(0,c.jsx)("h1",{children:"User ID is not set"});if(null==l&&r("App Owner"),l&&"Admin Viewer"==l){let{Title:e,Paragraph:s}=es.default;return(0,c.jsxs)("div",{children:[(0,c.jsx)(e,{level:1,children:"Access Denied"}),(0,c.jsx)(s,{children:"Ask your proxy admin for access to create keys"})]})}return console.log("inside user dashboard, selected team",F),console.log("All cookies after redirect:",document.cookie),(0,c.jsx)("div",{className:"w-full mx-4 h-[75vh]",children:(0,c.jsx)(w.Z,{numItems:1,className:"gap-2 p-8 w-full mt-2",children:(0,c.jsxs)(N.Z,{numColSpan:1,className:"flex flex-col gap-2",children:[(0,c.jsx)(eN,{userID:s,team:F,teams:t,userRole:l,accessToken:T,data:a,addKey:g},F?F.team_id:null),(0,c.jsx)(e8,{userID:s,userRole:l,accessToken:T,selectedTeam:F||null,setSelectedTeam:R,selectedKeyAlias:q,setSelectedKeyAlias:U,data:a,setData:x,premiumUser:h,teams:t,currentOrg:b,setCurrentOrg:k,organizations:p,createClicked:j})]})})})},se=l(97765);(t=n||(n={})).OpenAI="OpenAI",t.OpenAI_Compatible="OpenAI-Compatible Endpoints (Together AI, etc.)",t.OpenAI_Text="OpenAI Text Completion",t.OpenAI_Text_Compatible="OpenAI-Compatible Text Completion Models (Together AI, etc.)",t.Azure="Azure",t.Azure_AI_Studio="Azure AI Foundry (Studio)",t.Anthropic="Anthropic",t.Vertex_AI="Vertex AI (Anthropic, Gemini, etc.)",t.Google_AI_Studio="Google AI Studio",t.Bedrock="Amazon Bedrock",t.Groq="Groq",t.MistralAI="Mistral AI",t.Deepseek="Deepseek",t.Cohere="Cohere",t.Databricks="Databricks",t.Ollama="Ollama",t.xAI="xAI",t.AssemblyAI="AssemblyAI",t.Cerebras="Cerebras",t.Sambanova="Sambanova",t.Perplexity="Perplexity",t.TogetherAI="TogetherAI",t.Openrouter="Openrouter",t.FireworksAI="Fireworks AI",t.Triton="Triton";let ss={OpenAI:"openai",OpenAI_Text:"text-completion-openai",Azure:"azure",Azure_AI_Studio:"azure_ai",Anthropic:"anthropic",Google_AI_Studio:"gemini",Bedrock:"bedrock",Groq:"groq",MistralAI:"mistral",Cohere:"cohere_chat",OpenAI_Compatible:"openai",OpenAI_Text_Compatible:"text-completion-openai",Vertex_AI:"vertex_ai",Databricks:"databricks",xAI:"xai",Deepseek:"deepseek",Ollama:"ollama",AssemblyAI:"assemblyai",Cerebras:"cerebras",Sambanova:"sambanova",Perplexity:"perplexity",TogetherAI:"together_ai",Openrouter:"openrouter",FireworksAI:"fireworks_ai",Triton:"triton"},sl="/ui/assets/logos/",st={Anthropic:"".concat(sl,"anthropic.svg"),AssemblyAI:"".concat(sl,"assemblyai_small.png"),Azure:"".concat(sl,"microsoft_azure.svg"),"Azure AI Foundry (Studio)":"".concat(sl,"microsoft_azure.svg"),"Amazon Bedrock":"".concat(sl,"bedrock.svg"),Cerebras:"".concat(sl,"cerebras.svg"),Cohere:"".concat(sl,"cohere.svg"),Databricks:"".concat(sl,"databricks.svg"),Deepseek:"".concat(sl,"deepseek.svg"),"Fireworks AI":"".concat(sl,"fireworks.svg"),Groq:"".concat(sl,"groq.svg"),"Google AI Studio":"".concat(sl,"google.svg"),"Mistral AI":"".concat(sl,"mistral.svg"),Ollama:"".concat(sl,"ollama.svg"),OpenAI:"".concat(sl,"openai_small.svg"),"OpenAI Text Completion":"".concat(sl,"openai_small.svg"),"OpenAI-Compatible Text Completion Models (Together AI, etc.)":"".concat(sl,"openai_small.svg"),"OpenAI-Compatible Endpoints (Together AI, etc.)":"".concat(sl,"openai_small.svg"),Openrouter:"".concat(sl,"openrouter.svg"),Perplexity:"".concat(sl,"perplexity-ai.svg"),Sambanova:"".concat(sl,"sambanova.svg"),TogetherAI:"".concat(sl,"togetherai.svg"),"Vertex AI (Anthropic, Gemini, etc.)":"".concat(sl,"google.svg"),xAI:"".concat(sl,"xai.svg"),Triton:"".concat(sl,"nvidia_triton.png")},sa=e=>{if(!e)return{logo:"",displayName:"-"};if("gemini"===e.toLowerCase()){let e="Google AI Studio";return{logo:st[e],displayName:e}}let s=Object.keys(ss).find(s=>ss[s].toLowerCase()===e.toLowerCase());if(!s)return{logo:"",displayName:e};let l=n[s];return{logo:st[l],displayName:l}},sr=e=>"Vertex AI (Anthropic, Gemini, etc.)"===e?"gemini-pro":"Anthropic"==e||"Amazon Bedrock"==e?"claude-3-opus":"Google AI Studio"==e?"gemini-pro":"Azure AI Foundry (Studio)"==e?"azure_ai/command-r-plus":"Azure"==e?"azure/my-deployment":"gpt-3.5-turbo",sn=(e,s)=>{console.log("Provider key: ".concat(e));let l=ss[e];console.log("Provider mapped to: ".concat(l));let t=[];return e&&"object"==typeof s&&(Object.entries(s).forEach(e=>{let[s,a]=e;null!==a&&"object"==typeof a&&"litellm_provider"in a&&(a.litellm_provider===l||a.litellm_provider.includes(l))&&t.push(s)}),"Cohere"==e&&(console.log("Adding cohere chat models"),Object.entries(s).forEach(e=>{let[s,l]=e;null!==l&&"object"==typeof l&&"litellm_provider"in l&&"cohere"===l.litellm_provider&&t.push(s)}))),t},si=async(e,s,l)=>{try{console.log("handling submit for formValues:",e);let s=e.model_mappings||[];if("model_mappings"in e&&delete e.model_mappings,e.model&&e.model.includes("all-wildcard")){let l=ss[e.custom_llm_provider]+"/*";e.model_name=l,s.push({public_name:l,litellm_model:l}),e.model=l}let l=[];for(let t of s){let s={},a={},r=t.public_name;for(let[l,r]of(s.model=t.litellm_model,e.input_cost_per_token&&(e.input_cost_per_token=Number(e.input_cost_per_token)/1e6),e.output_cost_per_token&&(e.output_cost_per_token=Number(e.output_cost_per_token)/1e6),s.model=t.litellm_model,console.log("formValues add deployment:",e),Object.entries(e)))if(""!==r&&"custom_pricing"!==l&&"pricing_model"!==l&&"cache_control"!==l){if("model_name"==l)s.model=r;else if("custom_llm_provider"==l){console.log("custom_llm_provider:",r);let e=ss[r];s.custom_llm_provider=e,console.log("custom_llm_provider mappingResult:",e)}else if("model"==l)continue;else if("base_model"===l)a[l]=r;else if("team_id"===l)a.team_id=r;else if("mode"==l)console.log("placing mode in modelInfo"),a.mode=r,delete s.mode;else if("custom_model_name"===l)s.model=r;else if("litellm_extra_params"==l){console.log("litellm_extra_params:",r);let e={};if(r&&void 0!=r){try{e=JSON.parse(r)}catch(e){throw D.ZP.error("Failed to parse LiteLLM Extra Params: "+e,10),Error("Failed to parse litellm_extra_params: "+e)}for(let[l,t]of Object.entries(e))s[l]=t}}else if("model_info_params"==l){console.log("model_info_params:",r);let e={};if(r&&void 0!=r){try{e=JSON.parse(r)}catch(e){throw D.ZP.error("Failed to parse LiteLLM Extra Params: "+e,10),Error("Failed to parse litellm_extra_params: "+e)}for(let[s,l]of Object.entries(e))a[s]=l}}else if("input_cost_per_token"===l||"output_cost_per_token"===l||"input_cost_per_second"===l){r&&(s[l]=Number(r));continue}else s[l]=r}l.push({litellmParamsObj:s,modelInfoObj:a,modelName:r})}return l}catch(e){D.ZP.error("Failed to create model: "+e,10)}},so=async(e,s,l,t)=>{try{let a=await si(e,s,l);if(!a||0===a.length)return;for(let e of a){let{litellmParamsObj:l,modelInfoObj:t,modelName:a}=e,r={model_name:a,litellm_params:l,model_info:t},n=await (0,v.kK)(s,r);console.log("response for model create call: ".concat(n.data))}t&&t(),l.resetFields()}catch(e){D.ZP.error("Failed to add model: "+e,10)}};var sc=l(53410),sd=l(47451),sm=l(69410);let{Link:su}=es.default,sx={[n.OpenAI]:[{key:"api_base",label:"API Base",type:"select",options:["https://api.openai.com/v1","https://eu.api.openai.com"],defaultValue:"https://api.openai.com/v1"},{key:"organization",label:"OpenAI Organization ID",placeholder:"[OPTIONAL] my-unique-org"},{key:"api_key",label:"OpenAI API Key",type:"password",required:!0}],[n.OpenAI_Text]:[{key:"api_base",label:"API Base",type:"select",options:["https://api.openai.com/v1","https://eu.api.openai.com"],defaultValue:"https://api.openai.com/v1"},{key:"organization",label:"OpenAI Organization ID",placeholder:"[OPTIONAL] my-unique-org"},{key:"api_key",label:"OpenAI API Key",type:"password",required:!0}],[n.Vertex_AI]:[{key:"vertex_project",label:"Vertex Project",placeholder:"adroit-cadet-1234..",required:!0},{key:"vertex_location",label:"Vertex Location",placeholder:"us-east-1",required:!0},{key:"vertex_credentials",label:"Vertex Credentials",required:!0,type:"upload"}],[n.AssemblyAI]:[{key:"api_base",label:"API Base",type:"select",required:!0,options:["https://api.assemblyai.com","https://api.eu.assemblyai.com"]},{key:"api_key",label:"AssemblyAI API Key",type:"password",required:!0}],[n.Azure]:[{key:"api_base",label:"API Base",placeholder:"https://...",required:!0},{key:"api_version",label:"API Version",placeholder:"2023-07-01-preview",tooltip:"By default litellm will use the latest version. If you want to use a different version, you can specify it here"},{key:"base_model",label:"Base Model",placeholder:"azure/gpt-3.5-turbo"},{key:"api_key",label:"Azure API Key",type:"password",required:!0}],[n.Azure_AI_Studio]:[{key:"api_base",label:"API Base",placeholder:"https://<test>.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-10-21",tooltip:"Enter your full Target URI from Azure Foundry here. Example:  https://litellm8397336933.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-10-21",required:!0},{key:"api_key",label:"Azure API Key",type:"password",required:!0}],[n.OpenAI_Compatible]:[{key:"api_base",label:"API Base",placeholder:"https://...",required:!0},{key:"api_key",label:"OpenAI API Key",type:"password",required:!0}],[n.OpenAI_Text_Compatible]:[{key:"api_base",label:"API Base",placeholder:"https://...",required:!0},{key:"api_key",label:"OpenAI API Key",type:"password",required:!0}],[n.Bedrock]:[{key:"aws_access_key_id",label:"AWS Access Key ID",required:!0,tooltip:"You can provide the raw key or the environment variable (e.g. `os.environ/MY_SECRET_KEY`)."},{key:"aws_secret_access_key",label:"AWS Secret Access Key",required:!0,tooltip:"You can provide the raw key or the environment variable (e.g. `os.environ/MY_SECRET_KEY`)."},{key:"aws_region_name",label:"AWS Region Name",placeholder:"us-east-1",required:!0,tooltip:"You can provide the raw key or the environment variable (e.g. `os.environ/MY_SECRET_KEY`)."}],[n.Ollama]:[],[n.Anthropic]:[{key:"api_key",label:"API Key",placeholder:"sk-",type:"password",required:!0}],[n.Google_AI_Studio]:[{key:"api_key",label:"API Key",placeholder:"aig-",type:"password",required:!0}],[n.Groq]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.MistralAI]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Deepseek]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Cohere]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Databricks]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.xAI]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Cerebras]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Sambanova]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Perplexity]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.TogetherAI]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Openrouter]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.FireworksAI]:[{key:"api_key",label:"API Key",type:"password",required:!0}],[n.Triton]:[{key:"api_key",label:"API Key",type:"password",required:!1},{key:"api_base",label:"API Base",placeholder:"http://localhost:8000/generate",required:!1}]};var sh=e=>{let{selectedProvider:s,uploadProps:l}=e,t=n[s],a=L.Z.useFormInstance(),r=d.useMemo(()=>sx[t]||[],[t]),i={name:"file",accept:".json",beforeUpload:e=>{if("application/json"===e.type){let s=new FileReader;s.onload=e=>{if(e.target){let s=e.target.result;console.log("Setting field value from JSON, length: ".concat(s.length)),a.setFieldsValue({vertex_credentials:s}),console.log("Form values after setting:",a.getFieldsValue())}},s.readAsText(e)}return!1},onChange(e){console.log("Upload onChange triggered in ProviderSpecificFields"),console.log("Current form values:",a.getFieldsValue()),"uploading"!==e.file.status&&console.log(e.file,e.fileList)}};return(0,c.jsx)(c.Fragment,{children:r.map(e=>{var s;return(0,c.jsxs)(d.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:e.label,name:e.key,rules:e.required?[{required:!0,message:"Required"}]:void 0,tooltip:e.tooltip,className:"vertex_credentials"===e.key?"mb-0":void 0,children:"select"===e.type?(0,c.jsx)(O.default,{placeholder:e.placeholder,defaultValue:e.defaultValue,children:null===(s=e.options)||void 0===s?void 0:s.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:e},e))}):"upload"===e.type?(0,c.jsx)(et.Z,{...i,onChange:s=>{(null==l?void 0:l.onChange)&&l.onChange(s),setTimeout(()=>{let s=a.getFieldValue(e.key);console.log("".concat(e.key," value after upload:"),JSON.stringify(s))},500)},children:(0,c.jsx)(R.ZP,{icon:(0,c.jsx)(en.Z,{}),children:"Click to Upload"})}):(0,c.jsx)(S.Z,{placeholder:e.placeholder,type:"password"===e.type?"password":"text"})}),"vertex_credentials"===e.key&&(0,c.jsx)(sd.Z,{children:(0,c.jsx)(sm.Z,{children:(0,c.jsx)(A.Z,{className:"mb-3 mt-1",children:"Give a gcp service account(.json file)"})})}),"base_model"===e.key&&(0,c.jsxs)(sd.Z,{children:[(0,c.jsx)(sm.Z,{span:10}),(0,c.jsx)(sm.Z,{span:10,children:(0,c.jsxs)(A.Z,{className:"mb-2",children:["The actual model your azure deployment uses. Used for accurate cost tracking. Select name from"," ",(0,c.jsx)(su,{href:"https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json",target:"_blank",children:"here"})]})})]})]},e.key)})})};let{Title:sp,Link:sg}=es.default;var sj=e=>{let{isVisible:s,onCancel:l,onAddCredential:t,onUpdateCredential:a,uploadProps:r,addOrEdit:i,existingCredential:o}=e,[m]=L.Z.useForm(),[u,x]=(0,d.useState)(n.OpenAI),[h,p]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{o&&(m.setFieldsValue({credential_name:o.credential_name,custom_llm_provider:o.credential_info.custom_llm_provider,api_base:o.credential_values.api_base,api_version:o.credential_values.api_version,base_model:o.credential_values.base_model,api_key:o.credential_values.api_key}),x(o.credential_info.custom_llm_provider))},[o]),(0,c.jsx)(M.Z,{title:"add"===i?"Add New Credential":"Edit Credential",visible:s,onCancel:()=>{l(),m.resetFields()},footer:null,width:600,children:(0,c.jsxs)(L.Z,{form:m,onFinish:e=>{"add"===i?t(e):a(e),m.resetFields()},layout:"vertical",children:[(0,c.jsx)(L.Z.Item,{label:"Credential Name:",name:"credential_name",rules:[{required:!0,message:"Credential name is required"}],initialValue:null==o?void 0:o.credential_name,children:(0,c.jsx)(S.Z,{placeholder:"Enter a friendly name for these credentials",disabled:null!=o&&!!o.credential_name})}),(0,c.jsx)(L.Z.Item,{rules:[{required:!0,message:"Required"}],label:"Provider:",name:"custom_llm_provider",tooltip:"Helper to auto-populate provider specific fields",children:(0,c.jsx)(O.default,{onChange:e=>{x(e),m.setFieldValue("custom_llm_provider",e)},children:Object.entries(n).map(e=>{let[s,l]=e;return(0,c.jsx)(O.default.Option,{value:s,children:(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("img",{src:st[l],alt:"".concat(s," logo"),className:"w-5 h-5",onError:e=>{let s=e.target,t=s.parentElement;if(t){let e=document.createElement("div");e.className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs",e.textContent=l.charAt(0),t.replaceChild(e,s)}}}),(0,c.jsx)("span",{children:l})]})},s)})})}),(0,c.jsx)(sh,{selectedProvider:u,uploadProps:r}),(0,c.jsxs)("div",{className:"flex justify-between items-center",children:[(0,c.jsx)(W.Z,{title:"Get help on our github",children:(0,c.jsx)(sg,{href:"https://github.com/BerriAI/litellm/issues",children:"Need Help?"})}),(0,c.jsxs)("div",{children:[(0,c.jsx)(R.ZP,{onClick:()=>{l(),m.resetFields()},style:{marginRight:10},children:"Cancel"}),(0,c.jsx)(R.ZP,{htmlType:"submit",children:"add"===i?"Add Credential":"Update Credential"})]})]})]})})},sf=e=>{let{accessToken:s,uploadProps:l,credentialList:t,fetchCredentials:a}=e,[r,n]=(0,d.useState)(!1),[i,o]=(0,d.useState)(!1),[m,u]=(0,d.useState)(null),[x]=L.Z.useForm(),h=["credential_name","custom_llm_provider"],p=async e=>{if(!s)return;let l=Object.entries(e).filter(e=>{let[s]=e;return!h.includes(s)}).reduce((e,s)=>{let[l,t]=s;return{...e,[l]:t}},{}),t={credential_name:e.credential_name,credential_values:l,credential_info:{custom_llm_provider:e.custom_llm_provider}};await (0,v.eZ)(s,e.credential_name,t),D.ZP.success("Credential updated successfully"),o(!1),a(s)},g=async e=>{if(!s)return;let l=Object.entries(e).filter(e=>{let[s]=e;return!h.includes(s)}).reduce((e,s)=>{let[l,t]=s;return{...e,[l]:t}},{}),t={credential_name:e.credential_name,credential_values:l,credential_info:{custom_llm_provider:e.custom_llm_provider}};await (0,v.oC)(s,t),D.ZP.success("Credential added successfully"),n(!1),a(s)};(0,d.useEffect)(()=>{s&&a(s)},[s]);let j=e=>{let s={openai:"blue",azure:"indigo",anthropic:"purple",default:"gray"},l=s[e.toLowerCase()]||s.default;return(0,c.jsx)(eC.Z,{color:l,size:"xs",children:e})},f=async e=>{s&&(await (0,v.gX)(s,e),D.ZP.success("Credential deleted successfully"),a(s))};return(0,c.jsxs)("div",{className:"w-full mx-auto flex-auto overflow-y-auto m-8 p-2",children:[(0,c.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,c.jsxs)(A.Z,{children:["Configured credentials for different AI providers. Add and manage your API credentials."," ",(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/credentials",target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:text-blue-700 underline",children:"Docs"})]})}),(0,c.jsx)(eI.Z,{children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Credential Name"}),(0,c.jsx)(e1.Z,{children:"Provider"}),(0,c.jsx)(e1.Z,{children:"Description"})]})}),(0,c.jsx)(eX.Z,{children:t&&0!==t.length?t.map((e,s)=>{var l,t;return(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.credential_name}),(0,c.jsx)(eQ.Z,{children:j((null===(l=e.credential_info)||void 0===l?void 0:l.custom_llm_provider)||"-")}),(0,c.jsx)(eQ.Z,{children:(null===(t=e.credential_info)||void 0===t?void 0:t.description)||"-"}),(0,c.jsxs)(eQ.Z,{children:[(0,c.jsx)(k.Z,{icon:sc.Z,variant:"light",size:"sm",onClick:()=>{u(e),o(!0)}}),(0,c.jsx)(k.Z,{icon:eM.Z,variant:"light",size:"sm",onClick:()=>f(e.credential_name)})]})]},s)}):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:4,className:"text-center py-4 text-gray-500",children:"No credentials configured"})})})]})}),(0,c.jsx)(k.Z,{onClick:()=>n(!0),className:"mt-4",children:"Add Credential"}),r&&(0,c.jsx)(sj,{onAddCredential:g,isVisible:r,onCancel:()=>n(!1),uploadProps:l,addOrEdit:"add",onUpdateCredential:p,existingCredential:null}),i&&(0,c.jsx)(sj,{onAddCredential:g,isVisible:i,existingCredential:m,onUpdateCredential:p,uploadProps:l,onCancel:()=>o(!1),addOrEdit:"edit"})]})};let s_=e=>{var s;return(null==e?void 0:null===(s=e.model_info)||void 0===s?void 0:s.team_public_model_name)?e.model_info.team_public_model_name:(null==e?void 0:e.model_name)||"-"};var sv=l(53003),sy=l(47323),sb=l(75105),sZ=l(40278),sN=l(14301),sw=l(59664),sk=e=>{let{modelMetrics:s,modelMetricsCategories:l,customTooltip:t,premiumUser:a}=e;return(0,c.jsx)(sw.Z,{title:"Time to First token (s)",className:"h-72",data:s,index:"date",showLegend:!1,categories:l,colors:["indigo","rose"],connectNulls:!0,customTooltip:t})},sS=e=>{let{teamData:s,canEditTeam:l,handleMemberDelete:t,setSelectedEditMember:a,setIsEditMemberModalVisible:r,setIsAddMemberModalVisible:n}=e;return(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsx)(eI.Z,{className:"w-full mx-auto flex-auto overflow-y-auto max-h-[50vh]",children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"User ID"}),(0,c.jsx)(e1.Z,{children:"User Email"}),(0,c.jsx)(e1.Z,{children:"Role"}),(0,c.jsx)(e1.Z,{})]})}),(0,c.jsx)(eX.Z,{children:s.team_info.members_with_roles.map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{className:"font-mono",children:e.user_id})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{className:"font-mono",children:e.user_email})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{className:"font-mono",children:e.role})}),(0,c.jsx)(eQ.Z,{children:l&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>{a(e),r(!0)}}),(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>t(e)})]})})]},s))})]})}),(0,c.jsx)(k.Z,{onClick:()=>n(!0),children:"Add Member"})]})},sC=l(61994),sI=l(85180),sT=l(89245),sA=l(78355);let sE={"/key/generate":"Member can generate a virtual key for this team","/key/update":"Member can update a virtual key belonging to this team","/key/delete":"Member can delete a virtual key belonging to this team","/key/info":"Member can get info about a virtual key belonging to this team","/key/regenerate":"Member can regenerate a virtual key belonging to this team","/key/{key_id}/regenerate":"Member can regenerate a virtual key belonging to this team","/key/list":"Member can list virtual keys belonging to this team","/key/block":"Member can block a virtual key belonging to this team","/key/unblock":"Member can unblock a virtual key belonging to this team"},sP=e=>e.includes("/info")||e.includes("/list")?"GET":"POST",sO=e=>{let s=sP(e),l=sE[e];if(!l){for(let[s,t]of Object.entries(sE))if(e.includes(s)){l=t;break}}return l||(l="Access ".concat(e)),{method:s,endpoint:e,description:l,route:e}};var sL=e=>{let{teamId:s,accessToken:l,canEditTeam:t}=e,[a,r]=(0,d.useState)([]),[n,i]=(0,d.useState)([]),[o,m]=(0,d.useState)(!0),[u,x]=(0,d.useState)(!1),[h,p]=(0,d.useState)(!1),g=async()=>{try{if(m(!0),!l)return;let e=await (0,v.aC)(l,s),t=e.all_available_permissions||[];r(t);let a=e.team_member_permissions||[];i(a),p(!1)}catch(e){D.ZP.error("Failed to load permissions"),console.error("Error fetching permissions:",e)}finally{m(!1)}};(0,d.useEffect)(()=>{g()},[s,l]);let j=(e,s)=>{i(s?[...n,e]:n.filter(s=>s!==e)),p(!0)},f=async()=>{try{if(!l)return;x(!0),await (0,v.TF)(l,s,n),D.ZP.success("Permissions updated successfully"),p(!1)}catch(e){D.ZP.error("Failed to update permissions"),console.error("Error updating permissions:",e)}finally{x(!1)}};if(o)return(0,c.jsx)("div",{className:"p-6 text-center",children:"Loading permissions..."});let _=a.length>0;return(0,c.jsxs)(eI.Z,{className:"bg-white shadow-md rounded-md p-6",children:[(0,c.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center border-b pb-4 mb-6",children:[(0,c.jsx)(E.Z,{className:"mb-2 sm:mb-0",children:"Member Permissions"}),t&&h&&(0,c.jsxs)("div",{className:"flex gap-3",children:[(0,c.jsx)(R.ZP,{icon:(0,c.jsx)(sT.Z,{}),onClick:()=>{g()},children:"Reset"}),(0,c.jsxs)(k.Z,{onClick:f,loading:u,className:"flex items-center gap-2",children:[(0,c.jsx)(sA.Z,{})," Save Changes"]})]})]}),(0,c.jsx)(A.Z,{className:"mb-6 text-gray-600",children:"Control what team members can do when they are not team admins."}),_?(0,c.jsxs)(e$.Z,{className:"mt-4",children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Method"}),(0,c.jsx)(e1.Z,{children:"Endpoint"}),(0,c.jsx)(e1.Z,{children:"Description"}),(0,c.jsx)(e1.Z,{className:"text-right",children:"Access"})]})}),(0,c.jsx)(eX.Z,{children:a.map(e=>{let s=sO(e);return(0,c.jsxs)(e2.Z,{className:"hover:bg-gray-50 transition-colors",children:[(0,c.jsx)(eQ.Z,{children:(0,c.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat("GET"===s.method?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"),children:s.method})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)("span",{className:"font-mono text-sm text-gray-800",children:s.endpoint})}),(0,c.jsx)(eQ.Z,{className:"text-gray-700",children:s.description}),(0,c.jsx)(eQ.Z,{className:"text-right",children:(0,c.jsx)(sC.Z,{checked:n.includes(e),onChange:s=>j(e,s.target.checked),disabled:!t})})]},e)})})]}):(0,c.jsx)("div",{className:"py-12",children:(0,c.jsx)(sI.Z,{description:"No permissions available"})})]})},sD=e=>{var s,l,t;let{visible:a,onCancel:r,onSubmit:n,initialData:i,mode:o,config:m}=e,[u]=L.Z.useForm();console.log("Initial Data:",i),(0,d.useEffect)(()=>{if(a){if("edit"===o&&i)u.setFieldsValue({...i,role:i.role||m.defaultRole});else{var e;u.resetFields(),u.setFieldsValue({role:m.defaultRole||(null===(e=m.roleOptions[0])||void 0===e?void 0:e.value)})}}},[a,i,o,u,m.defaultRole,m.roleOptions]);let x=async e=>{try{let s=Object.entries(e).reduce((e,s)=>{let[l,t]=s;return{...e,[l]:"string"==typeof t?t.trim():t}},{});n(s),u.resetFields(),D.ZP.success("Successfully ".concat("add"===o?"added":"updated"," member"))}catch(e){D.ZP.error("Failed to submit form"),console.error("Form submission error:",e)}},h=e=>{switch(e.type){case"input":return(0,c.jsx)(q.default,{className:"px-3 py-2 border rounded-md w-full",onChange:e=>{e.target.value=e.target.value.trim()}});case"select":var s;return(0,c.jsx)(O.default,{children:null===(s=e.options)||void 0===s?void 0:s.map(e=>(0,c.jsx)(O.default.Option,{value:e.value,children:e.label},e.value))});default:return null}};return(0,c.jsx)(M.Z,{title:m.title||("add"===o?"Add Member":"Edit Member"),open:a,width:800,footer:null,onCancel:r,children:(0,c.jsxs)(L.Z,{form:u,onFinish:x,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[m.showEmail&&(0,c.jsx)(L.Z.Item,{label:"Email",name:"user_email",className:"mb-4",rules:[{type:"email",message:"Please enter a valid email!"}],children:(0,c.jsx)(q.default,{className:"px-3 py-2 border rounded-md w-full",placeholder:"<EMAIL>",onChange:e=>{e.target.value=e.target.value.trim()}})}),m.showEmail&&m.showUserId&&(0,c.jsx)("div",{className:"text-center mb-4",children:(0,c.jsx)(A.Z,{children:"OR"})}),m.showUserId&&(0,c.jsx)(L.Z.Item,{label:"User ID",name:"user_id",className:"mb-4",children:(0,c.jsx)(q.default,{className:"px-3 py-2 border rounded-md w-full",placeholder:"user_123",onChange:e=>{e.target.value=e.target.value.trim()}})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsx)("span",{children:"Role"}),"edit"===o&&i&&(0,c.jsxs)("span",{className:"text-gray-500 text-sm",children:["(Current: ",(l=i.role,(null===(t=m.roleOptions.find(e=>e.value===l))||void 0===t?void 0:t.label)||l),")"]})]}),name:"role",className:"mb-4",rules:[{required:!0,message:"Please select a role!"}],children:(0,c.jsx)(O.default,{children:"edit"===o&&i?[...m.roleOptions.filter(e=>e.value===i.role),...m.roleOptions.filter(e=>e.value!==i.role)].map(e=>(0,c.jsx)(O.default.Option,{value:e.value,children:e.label},e.value)):m.roleOptions.map(e=>(0,c.jsx)(O.default.Option,{value:e.value,children:e.label},e.value))})}),null===(s=m.additionalFields)||void 0===s?void 0:s.map(e=>(0,c.jsx)(L.Z.Item,{label:e.label,name:e.name,className:"mb-4",rules:e.rules,children:h(e)},e.name)),(0,c.jsxs)("div",{className:"text-right mt-6",children:[(0,c.jsx)(R.ZP,{onClick:r,className:"mr-2",children:"Cancel"}),(0,c.jsx)(R.ZP,{type:"default",htmlType:"submit",children:"add"===o?"Add Member":"Save Changes"})]})]})})},sM=e=>{let{isVisible:s,onCancel:l,onSubmit:t,accessToken:a,title:r="Add Team Member",roles:n=[{label:"admin",value:"admin",description:"Admin role. Can create team keys, add members, and manage settings."},{label:"user",value:"user",description:"User role. Can view team info, but not manage it."}],defaultRole:i="user"}=e,[o]=L.Z.useForm(),[m,u]=(0,d.useState)([]),[x,h]=(0,d.useState)(!1),[p,g]=(0,d.useState)("user_email"),j=async(e,s)=>{if(!e){u([]);return}h(!0);try{let l=new URLSearchParams;if(l.append(s,e),null==a)return;let t=(await (0,v.u5)(a,l)).map(e=>({label:"user_email"===s?"".concat(e.user_email):"".concat(e.user_id),value:"user_email"===s?e.user_email:e.user_id,user:e}));u(t)}catch(e){console.error("Error fetching users:",e)}finally{h(!1)}},f=(0,d.useCallback)(ep()((e,s)=>j(e,s),300),[]),_=(e,s)=>{g(s),f(e,s)},y=(e,s)=>{let l=s.user;o.setFieldsValue({user_email:l.user_email,user_id:l.user_id,role:o.getFieldValue("role")})};return(0,c.jsx)(M.Z,{title:r,open:s,onCancel:()=>{o.resetFields(),u([]),l()},footer:null,width:800,children:(0,c.jsxs)(L.Z,{form:o,onFinish:t,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",initialValues:{role:i},children:[(0,c.jsx)(L.Z.Item,{label:"Email",name:"user_email",className:"mb-4",children:(0,c.jsx)(O.default,{showSearch:!0,className:"w-full",placeholder:"Search by email",filterOption:!1,onSearch:e=>_(e,"user_email"),onSelect:(e,s)=>y(e,s),options:"user_email"===p?m:[],loading:x,allowClear:!0})}),(0,c.jsx)("div",{className:"text-center mb-4",children:"OR"}),(0,c.jsx)(L.Z.Item,{label:"User ID",name:"user_id",className:"mb-4",children:(0,c.jsx)(O.default,{showSearch:!0,className:"w-full",placeholder:"Search by user ID",filterOption:!1,onSearch:e=>_(e,"user_id"),onSelect:(e,s)=>y(e,s),options:"user_id"===p?m:[],loading:x,allowClear:!0})}),(0,c.jsx)(L.Z.Item,{label:"Member Role",name:"role",className:"mb-4",children:(0,c.jsx)(O.default,{defaultValue:i,children:n.map(e=>(0,c.jsx)(O.default.Option,{value:e.value,children:(0,c.jsxs)(W.Z,{title:e.description,children:[(0,c.jsx)("span",{className:"font-medium",children:e.label}),(0,c.jsxs)("span",{className:"ml-2 text-gray-500 text-sm",children:["- ",e.description]})]})},e.value))})}),(0,c.jsx)("div",{className:"text-right mt-4",children:(0,c.jsx)(R.ZP,{type:"default",htmlType:"submit",children:"Add Member"})})]})})},sF=e=>{var s;let{teamId:l,onClose:t,accessToken:a,is_team_admin:r,is_proxy_admin:n,userModels:i,editTeam:o}=e,[m,u]=(0,d.useState)(null),[x,h]=(0,d.useState)(!0),[p,g]=(0,d.useState)(!1),[j]=L.Z.useForm(),[f,_]=(0,d.useState)(!1),[y,b]=(0,d.useState)(null),[Z,N]=(0,d.useState)(!1);console.log("userModels in team info",i);let S=r||n,C=async()=>{try{if(h(!0),!a)return;let e=await (0,v.Xm)(a,l);u(e)}catch(e){D.ZP.error("Failed to load team information"),console.error("Error fetching team info:",e)}finally{h(!1)}};(0,d.useEffect)(()=>{C()},[l,a]);let I=async e=>{try{if(null==a)return;let s={user_email:e.user_email,user_id:e.user_id,role:e.role};await (0,v.cu)(a,l,s),D.ZP.success("Team member added successfully"),g(!1),j.resetFields(),C()}catch(e){D.ZP.error("Failed to add team member"),console.error("Error adding team member:",e)}},T=async e=>{try{if(null==a)return;let s={user_email:e.user_email,user_id:e.user_id,role:e.role};await (0,v.sN)(a,l,s),D.ZP.success("Team member updated successfully"),_(!1),C()}catch(e){D.ZP.error("Failed to update team member"),console.error("Error updating team member:",e)}},P=async e=>{try{if(null==a)return;await (0,v.Lp)(a,l,e),D.ZP.success("Team member removed successfully"),C()}catch(e){D.ZP.error("Failed to remove team member"),console.error("Error removing team member:",e)}},M=async e=>{try{if(!a)return;let s={};try{s=e.metadata?JSON.parse(e.metadata):{}}catch(e){D.ZP.error("Invalid JSON in metadata field");return}let t={team_id:l,team_alias:e.team_alias,models:e.models,tpm_limit:e.tpm_limit,rpm_limit:e.rpm_limit,max_budget:e.max_budget,budget_duration:e.budget_duration,metadata:{...s,guardrails:e.guardrails||[]},organization_id:e.organization_id};await (0,v.Gh)(a,t),D.ZP.success("Team settings updated successfully"),N(!1),C()}catch(e){console.error("Error updating team:",e)}};if(x)return(0,c.jsx)("div",{className:"p-4",children:"Loading..."});if(!(null==m?void 0:m.team_info))return(0,c.jsx)("div",{className:"p-4",children:"Team not found"});let{team_info:F}=m;return(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,c.jsxs)("div",{children:[(0,c.jsx)(R.ZP,{onClick:t,className:"mb-4",children:"← Back"}),(0,c.jsx)(E.Z,{children:F.team_alias}),(0,c.jsx)(A.Z,{className:"text-gray-500 font-mono",children:F.team_id})]})}),(0,c.jsxs)(eA.Z,{defaultIndex:o?3:0,children:[(0,c.jsx)(eE.Z,{className:"mb-4",children:[(0,c.jsx)(eT.Z,{children:"Overview"},"overview"),...S?[(0,c.jsx)(eT.Z,{children:"Members"},"members"),(0,c.jsx)(eT.Z,{children:"Member Permissions"},"member-permissions"),(0,c.jsx)(eT.Z,{children:"Settings"},"settings")]:[]]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:1,numItemsSm:2,numItemsLg:3,className:"gap-6",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Budget Status"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(E.Z,{children:["$",F.spend.toFixed(6)]}),(0,c.jsxs)(A.Z,{children:["of ",null===F.max_budget?"Unlimited":"$".concat(F.max_budget)]}),F.budget_duration&&(0,c.jsxs)(A.Z,{className:"text-gray-500",children:["Reset: ",F.budget_duration]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Rate Limits"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(A.Z,{children:["TPM: ",F.tpm_limit||"Unlimited"]}),(0,c.jsxs)(A.Z,{children:["RPM: ",F.rpm_limit||"Unlimited"]}),F.max_parallel_requests&&(0,c.jsxs)(A.Z,{children:["Max Parallel Requests: ",F.max_parallel_requests]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Models"}),(0,c.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:F.models.map((e,s)=>(0,c.jsx)(eC.Z,{color:"red",children:e},s))})]})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(sS,{teamData:m,canEditTeam:S,handleMemberDelete:P,setSelectedEditMember:b,setIsEditMemberModalVisible:_,setIsAddMemberModalVisible:g})}),S&&(0,c.jsx)(eP.Z,{children:(0,c.jsx)(sL,{teamId:l,accessToken:a,canEditTeam:S})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,c.jsx)(E.Z,{children:"Team Settings"}),S&&!Z&&(0,c.jsx)(k.Z,{onClick:()=>N(!0),children:"Edit Settings"})]}),Z?(0,c.jsxs)(L.Z,{form:j,onFinish:M,initialValues:{...F,team_alias:F.team_alias,models:F.models,tpm_limit:F.tpm_limit,rpm_limit:F.rpm_limit,max_budget:F.max_budget,budget_duration:F.budget_duration,guardrails:(null===(s=F.metadata)||void 0===s?void 0:s.guardrails)||[],metadata:F.metadata?JSON.stringify(F.metadata,null,2):"",organization_id:F.organization_id},layout:"vertical",children:[(0,c.jsx)(L.Z.Item,{label:"Team Name",name:"team_alias",rules:[{required:!0,message:"Please input a team name"}],children:(0,c.jsx)(q.default,{type:""})}),(0,c.jsx)(L.Z.Item,{label:"Models",name:"models",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",children:[(0,c.jsx)(O.default.Option,{value:"all-proxy-models",children:"All Proxy Models"},"all-proxy-models"),i.map((e,s)=>(0,c.jsx)(O.default.Option,{value:e,children:K(e)},s))]})}),(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(z,{step:.01,precision:2,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{label:"Reset Budget",name:"budget_duration",children:(0,c.jsxs)(O.default,{placeholder:"n/a",children:[(0,c.jsx)(O.default.Option,{value:"24h",children:"daily"}),(0,c.jsx)(O.default.Option,{value:"7d",children:"weekly"}),(0,c.jsx)(O.default.Option,{value:"30d",children:"monthly"})]})}),(0,c.jsx)(L.Z.Item,{label:"Tokens per minute Limit (TPM)",name:"tpm_limit",children:(0,c.jsx)(z,{step:1,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{label:"Requests per minute Limit (RPM)",name:"rpm_limit",children:(0,c.jsx)(z,{step:1,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Guardrails"," ",(0,c.jsx)(W.Z,{title:"Setup your first guardrail",children:(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/guardrails/quick_start",target:"_blank",rel:"noopener noreferrer",onClick:e=>e.stopPropagation(),children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})})]}),name:"guardrails",help:"Select existing guardrails or enter new ones",children:(0,c.jsx)(O.default,{mode:"tags",placeholder:"Select or enter guardrails"})}),(0,c.jsx)(L.Z.Item,{label:"Organization ID",name:"organization_id",children:(0,c.jsx)(q.default,{type:""})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",children:(0,c.jsx)(q.default.TextArea,{rows:10})}),(0,c.jsxs)("div",{className:"flex justify-end gap-2 mt-6",children:[(0,c.jsx)(R.ZP,{onClick:()=>N(!1),children:"Cancel"}),(0,c.jsx)(k.Z,{children:"Save Changes"})]})]}):(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Team Name"}),(0,c.jsx)("div",{children:F.team_alias})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Team ID"}),(0,c.jsx)("div",{className:"font-mono",children:F.team_id})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Created At"}),(0,c.jsx)("div",{children:new Date(F.created_at).toLocaleString()})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Models"}),(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:F.models.map((e,s)=>(0,c.jsx)(eC.Z,{color:"red",children:e},s))})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Rate Limits"}),(0,c.jsxs)("div",{children:["TPM: ",F.tpm_limit||"Unlimited"]}),(0,c.jsxs)("div",{children:["RPM: ",F.rpm_limit||"Unlimited"]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Budget"}),(0,c.jsxs)("div",{children:["Max: ",null!==F.max_budget?"$".concat(F.max_budget):"No Limit"]}),(0,c.jsxs)("div",{children:["Reset: ",F.budget_duration||"Never"]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Organization ID"}),(0,c.jsx)("div",{children:F.organization_id})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Status"}),(0,c.jsx)(eC.Z,{color:F.blocked?"red":"green",children:F.blocked?"Blocked":"Active"})]})]})]})})]})]}),(0,c.jsx)(sD,{visible:f,onCancel:()=>_(!1),onSubmit:T,initialData:y,mode:"edit",config:{title:"Edit Member",showEmail:!0,showUserId:!0,roleOptions:[{label:"Admin",value:"admin"},{label:"User",value:"user"}]}}),(0,c.jsx)(sM,{isVisible:p,onCancel:()=>g(!1),onSubmit:I,accessToken:a})]})},sR=l(45589);let{Title:sq,Link:sU}=es.default;var sz=e=>{let{isVisible:s,onCancel:l,onAddCredential:t,existingCredential:a,setIsCredentialModalOpen:r}=e,[n]=L.Z.useForm();return console.log("existingCredential in add credentials tab: ".concat(JSON.stringify(a))),(0,c.jsx)(M.Z,{title:"Reuse Credentials",visible:s,onCancel:()=>{l(),n.resetFields()},footer:null,width:600,children:(0,c.jsxs)(L.Z,{form:n,onFinish:e=>{t(e),n.resetFields(),r(!1)},layout:"vertical",children:[(0,c.jsx)(L.Z.Item,{label:"Credential Name:",name:"credential_name",rules:[{required:!0,message:"Credential name is required"}],initialValue:null==a?void 0:a.credential_name,children:(0,c.jsx)(S.Z,{placeholder:"Enter a friendly name for these credentials"})}),Object.entries((null==a?void 0:a.credential_values)||{}).map(e=>{let[s,l]=e;return(0,c.jsx)(L.Z.Item,{label:s,name:s,initialValue:l,children:(0,c.jsx)(S.Z,{placeholder:"Enter ".concat(s),disabled:!0})},s)}),(0,c.jsxs)("div",{className:"flex justify-between items-center",children:[(0,c.jsx)(W.Z,{title:"Get help on our github",children:(0,c.jsx)(sU,{href:"https://github.com/BerriAI/litellm/issues",children:"Need Help?"})}),(0,c.jsxs)("div",{children:[(0,c.jsx)(R.ZP,{onClick:()=>{l(),n.resetFields()},style:{marginRight:10},children:"Cancel"}),(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Reuse Credentials"})]})]})]})})},sV=l(63709),sK=l(45246),sB=l(96473);let{Text:sH}=es.default;var sJ=e=>{let{form:s,showCacheControl:l,onCacheControlChange:t}=e,a=e=>{let l=s.getFieldValue("litellm_extra_params");try{let t=l?JSON.parse(l):{};e.length>0?t.cache_control_injection_points=e:delete t.cache_control_injection_points,Object.keys(t).length>0?s.setFieldValue("litellm_extra_params",JSON.stringify(t,null,2)):s.setFieldValue("litellm_extra_params","")}catch(e){console.error("Error updating cache control points:",e)}};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"Cache Control Injection Points",name:"cache_control",valuePropName:"checked",className:"mb-4",tooltip:"Tell litellm where to inject cache control checkpoints. You can specify either by role (to apply to all messages of that role) or by specific message index.",children:(0,c.jsx)(sV.Z,{onChange:t,className:"bg-gray-600"})}),l&&(0,c.jsxs)("div",{className:"ml-6 pl-4 border-l-2 border-gray-200",children:[(0,c.jsx)(sH,{className:"text-sm text-gray-500 block mb-4",children:"Providers like Anthropic, Bedrock API require users to specify where to inject cache control checkpoints, litellm can automatically add them for you as a cost saving feature."}),(0,c.jsx)(L.Z.List,{name:"cache_control_injection_points",initialValue:[{location:"message"}],children:(e,l)=>{let{add:t,remove:r}=l;return(0,c.jsxs)(c.Fragment,{children:[e.map((l,t)=>(0,c.jsxs)("div",{className:"flex items-center mb-4 gap-4",children:[(0,c.jsx)(L.Z.Item,{...l,label:"Type",name:[l.name,"location"],initialValue:"message",className:"mb-0",style:{width:"180px"},children:(0,c.jsx)(O.default,{disabled:!0,options:[{value:"message",label:"Message"}]})}),(0,c.jsx)(L.Z.Item,{...l,label:"Role",name:[l.name,"role"],className:"mb-0",style:{width:"180px"},tooltip:"LiteLLM will mark all messages of this role as cacheable",children:(0,c.jsx)(O.default,{placeholder:"Select a role",allowClear:!0,options:[{value:"user",label:"User"},{value:"system",label:"System"},{value:"assistant",label:"Assistant"}],onChange:()=>{a(s.getFieldValue("cache_control_points"))}})}),(0,c.jsx)(L.Z.Item,{...l,label:"Index",name:[l.name,"index"],className:"mb-0",style:{width:"180px"},tooltip:"(Optional) If set litellm will mark the message at this index as cacheable",children:(0,c.jsx)(z,{type:"number",placeholder:"Optional",step:1,min:0,onChange:()=>{a(s.getFieldValue("cache_control_points"))}})}),e.length>1&&(0,c.jsx)(sK.Z,{className:"text-red-500 cursor-pointer text-lg ml-12",onClick:()=>{r(l.name),setTimeout(()=>{a(s.getFieldValue("cache_control_points"))},0)}})]},l.key)),(0,c.jsx)(L.Z.Item,{children:(0,c.jsxs)("button",{type:"button",className:"flex items-center justify-center w-full border border-dashed border-gray-300 py-2 px-4 text-gray-600 hover:text-blue-600 hover:border-blue-300 transition-all rounded",onClick:()=>t(),children:[(0,c.jsx)(sB.Z,{className:"mr-2"}),"Add Injection Point"]})})]})}})]})]})};function sW(e){var s,l,t,a,r,n,i,o,m,u,x,h,p,g,j,f,_,y,b,Z,N;let{modelId:C,onClose:I,modelData:T,accessToken:P,userID:O,userRole:F,editModel:q,setEditModalVisible:U,setSelectedModel:V,onModelUpdate:K}=e,[B]=L.Z.useForm(),[H,J]=(0,d.useState)(null),[W,G]=(0,d.useState)(!1),[Y,$]=(0,d.useState)(!1),[X,Q]=(0,d.useState)(!1),[ee,es]=(0,d.useState)(!1),[el,et]=(0,d.useState)(!1),[ea,er]=(0,d.useState)(null),[en,ei]=(0,d.useState)(!1),eo="Admin"===F||T.model_info.created_by===O,ec=(null===(s=T.litellm_params)||void 0===s?void 0:s.litellm_credential_name)!=null&&(null===(l=T.litellm_params)||void 0===l?void 0:l.litellm_credential_name)!=void 0;console.log("usingExistingCredential, ",ec),console.log("modelData.litellm_params.litellm_credential_name, ",T.litellm_params.litellm_credential_name),(0,d.useEffect)(()=>{let e=async()=>{var e;if(!P)return;let s=await (0,v.ix)(P,C);console.log("modelInfoResponse, ",s);let l=s.data[0];J(l),(null==l?void 0:null===(e=l.litellm_params)||void 0===e?void 0:e.cache_control_injection_points)&&ei(!0)};(async()=>{if(console.log("accessToken, ",P),!P||ec)return;let e=await (0,v.Qg)(P,null,C);console.log("existingCredentialResponse, ",e),er({credential_name:e.credential_name,credential_values:e.credential_values,credential_info:e.credential_info})})(),e()},[P,C]);let ed=async e=>{var s;if(console.log("values, ",e),!P)return;let l={credential_name:e.credential_name,model_id:C,credential_info:{custom_llm_provider:null===(s=H.litellm_params)||void 0===s?void 0:s.custom_llm_provider}};D.ZP.info("Storing credential.."),console.log("credentialResponse, ",await (0,v.oC)(P,l)),D.ZP.success("Credential stored successfully")},em=async e=>{try{var s;if(!P)return;es(!0),console.log("values.model_name, ",e.model_name);let l={...H.litellm_params,model:e.litellm_model_name,api_base:e.api_base,custom_llm_provider:e.custom_llm_provider,organization:e.organization,tpm:e.tpm,rpm:e.rpm,max_retries:e.max_retries,timeout:e.timeout,stream_timeout:e.stream_timeout,input_cost_per_token:e.input_cost/1e6,output_cost_per_token:e.output_cost/1e6};e.cache_control&&(null===(s=e.cache_control_injection_points)||void 0===s?void 0:s.length)>0?l.cache_control_injection_points=e.cache_control_injection_points:delete l.cache_control_injection_points;let t={model_name:e.model_name,litellm_params:l,model_info:{id:C}};await (0,v.$D)(P,t,C);let a={...H,model_name:e.model_name,litellm_model_name:e.litellm_model_name,litellm_params:l};J(a),K&&K(a),D.ZP.success("Model settings updated successfully"),Q(!1),et(!1)}catch(e){console.error("Error updating model:",e),D.ZP.error("Failed to update model settings")}finally{es(!1)}};if(!T)return(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsx)(R.ZP,{icon:(0,c.jsx)(eL.Z,{}),onClick:I,className:"mb-4",children:"Back to Models"}),(0,c.jsx)(A.Z,{children:"Model not found"})]});let eu=async()=>{try{if(!P)return;await (0,v.Og)(P,C),D.ZP.success("Model deleted successfully"),K&&K({deleted:!0,model_info:{id:C}}),I()}catch(e){console.error("Error deleting the model:",e),D.ZP.error("Failed to delete model")}};return(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(R.ZP,{icon:(0,c.jsx)(eL.Z,{}),onClick:I,className:"mb-4",children:"Back to Models"}),(0,c.jsxs)(E.Z,{children:["Public Model Name: ",s_(T)]}),(0,c.jsx)(A.Z,{className:"text-gray-500 font-mono",children:T.model_info.id})]}),(0,c.jsxs)("div",{className:"flex gap-2",children:["Admin"===F&&(0,c.jsx)(k.Z,{icon:sR.Z,variant:"secondary",onClick:()=>$(!0),className:"flex items-center",children:"Re-use Credentials"}),eo&&(0,c.jsx)(k.Z,{icon:eM.Z,variant:"secondary",onClick:()=>G(!0),className:"flex items-center",children:"Delete Model"})]})]}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{className:"mb-6",children:[(0,c.jsx)(eT.Z,{children:"Overview"}),(0,c.jsx)(eT.Z,{children:"Raw JSON"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)(w.Z,{numItems:1,numItemsSm:2,numItemsLg:3,className:"gap-6 mb-6",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Provider"}),(0,c.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:[T.provider&&(0,c.jsx)("img",{src:sa(T.provider).logo,alt:"".concat(T.provider," logo"),className:"w-4 h-4",onError:e=>{let s=e.target,l=s.parentElement;if(l){var t;let e=document.createElement("div");e.className="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs",e.textContent=(null===(t=T.provider)||void 0===t?void 0:t.charAt(0))||"-",l.replaceChild(e,s)}}}),(0,c.jsx)(E.Z,{children:T.provider||"Not Set"})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"LiteLLM Model"}),(0,c.jsx)("pre",{children:(0,c.jsx)(E.Z,{children:T.litellm_model_name||"Not Set"})})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Pricing"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(A.Z,{children:["Input: $",T.input_cost,"/1M tokens"]}),(0,c.jsxs)(A.Z,{children:["Output: $",T.output_cost,"/1M tokens"]})]})]})]}),(0,c.jsxs)("div",{className:"mb-6 text-sm text-gray-500 flex items-center gap-x-6",children:[(0,c.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Created At ",T.model_info.created_at?new Date(T.model_info.created_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):"Not Set"]}),(0,c.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Created By ",T.model_info.created_by||"Not Set"]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,c.jsx)(E.Z,{children:"Model Settings"}),eo&&!el&&(0,c.jsx)(k.Z,{variant:"secondary",onClick:()=>et(!0),className:"flex items-center",children:"Edit Model"})]}),H?(0,c.jsx)(L.Z,{form:B,onFinish:em,initialValues:{model_name:H.model_name,litellm_model_name:H.litellm_model_name,api_base:H.litellm_params.api_base,custom_llm_provider:H.litellm_params.custom_llm_provider,organization:H.litellm_params.organization,tpm:H.litellm_params.tpm,rpm:H.litellm_params.rpm,max_retries:H.litellm_params.max_retries,timeout:H.litellm_params.timeout,stream_timeout:H.litellm_params.stream_timeout,input_cost:H.litellm_params.input_cost_per_token?1e6*H.litellm_params.input_cost_per_token:(null===(t=H.model_info)||void 0===t?void 0:t.input_cost_per_token)*1e6||null,output_cost:(null===(a=H.litellm_params)||void 0===a?void 0:a.output_cost_per_token)?1e6*H.litellm_params.output_cost_per_token:(null===(r=H.model_info)||void 0===r?void 0:r.output_cost_per_token)*1e6||null,cache_control:null!==(n=H.litellm_params)&&void 0!==n&&!!n.cache_control_injection_points,cache_control_injection_points:(null===(i=H.litellm_params)||void 0===i?void 0:i.cache_control_injection_points)||[]},layout:"vertical",onValuesChange:()=>Q(!0),children:(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Model Name"}),el?(0,c.jsx)(L.Z.Item,{name:"model_name",className:"mb-0",children:(0,c.jsx)(S.Z,{placeholder:"Enter model name"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:H.model_name})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"LiteLLM Model Name"}),el?(0,c.jsx)(L.Z.Item,{name:"litellm_model_name",className:"mb-0",children:(0,c.jsx)(S.Z,{placeholder:"Enter LiteLLM model name"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:H.litellm_model_name})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Input Cost (per 1M tokens)"}),el?(0,c.jsx)(L.Z.Item,{name:"input_cost",className:"mb-0",children:(0,c.jsx)(z,{placeholder:"Enter input cost"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null==H?void 0:null===(o=H.litellm_params)||void 0===o?void 0:o.input_cost_per_token)?((null===(m=H.litellm_params)||void 0===m?void 0:m.input_cost_per_token)*1e6).toFixed(4):(null==H?void 0:null===(u=H.model_info)||void 0===u?void 0:u.input_cost_per_token)?(1e6*H.model_info.input_cost_per_token).toFixed(4):null})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Output Cost (per 1M tokens)"}),el?(0,c.jsx)(L.Z.Item,{name:"output_cost",className:"mb-0",children:(0,c.jsx)(z,{placeholder:"Enter output cost"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null==H?void 0:null===(x=H.litellm_params)||void 0===x?void 0:x.output_cost_per_token)?(1e6*H.litellm_params.output_cost_per_token).toFixed(4):(null==H?void 0:null===(h=H.model_info)||void 0===h?void 0:h.output_cost_per_token)?(1e6*H.model_info.output_cost_per_token).toFixed(4):null})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"API Base"}),el?(0,c.jsx)(L.Z.Item,{name:"api_base",className:"mb-0",children:(0,c.jsx)(S.Z,{placeholder:"Enter API base"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(p=H.litellm_params)||void 0===p?void 0:p.api_base)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Custom LLM Provider"}),el?(0,c.jsx)(L.Z.Item,{name:"custom_llm_provider",className:"mb-0",children:(0,c.jsx)(S.Z,{placeholder:"Enter custom LLM provider"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(g=H.litellm_params)||void 0===g?void 0:g.custom_llm_provider)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Organization"}),el?(0,c.jsx)(L.Z.Item,{name:"organization",className:"mb-0",children:(0,c.jsx)(S.Z,{placeholder:"Enter organization"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(j=H.litellm_params)||void 0===j?void 0:j.organization)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"TPM (Tokens per Minute)"}),el?(0,c.jsx)(L.Z.Item,{name:"tpm",className:"mb-0",children:(0,c.jsx)(z,{placeholder:"Enter TPM"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(f=H.litellm_params)||void 0===f?void 0:f.tpm)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"RPM (Requests per Minute)"}),el?(0,c.jsx)(L.Z.Item,{name:"rpm",className:"mb-0",children:(0,c.jsx)(z,{placeholder:"Enter RPM"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(_=H.litellm_params)||void 0===_?void 0:_.rpm)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Max Retries"}),el?(0,c.jsx)(L.Z.Item,{name:"max_retries",className:"mb-0",children:(0,c.jsx)(z,{placeholder:"Enter max retries"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(y=H.litellm_params)||void 0===y?void 0:y.max_retries)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Timeout (seconds)"}),el?(0,c.jsx)(L.Z.Item,{name:"timeout",className:"mb-0",children:(0,c.jsx)(z,{placeholder:"Enter timeout"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(b=H.litellm_params)||void 0===b?void 0:b.timeout)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Stream Timeout (seconds)"}),el?(0,c.jsx)(L.Z.Item,{name:"stream_timeout",className:"mb-0",children:(0,c.jsx)(z,{placeholder:"Enter stream timeout"})}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(Z=H.litellm_params)||void 0===Z?void 0:Z.stream_timeout)||"Not Set"})]}),el?(0,c.jsx)(sJ,{form:B,showCacheControl:en,onCacheControlChange:e=>ei(e)}):(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Cache Control"}),(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:(null===(N=H.litellm_params)||void 0===N?void 0:N.cache_control_injection_points)?(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{children:"Enabled"}),(0,c.jsx)("div",{className:"mt-2",children:H.litellm_params.cache_control_injection_points.map((e,s)=>(0,c.jsxs)("div",{className:"text-sm text-gray-600 mb-1",children:["Location: ",e.location,",",e.role&&(0,c.jsxs)("span",{children:[" Role: ",e.role]}),void 0!==e.index&&(0,c.jsxs)("span",{children:[" Index: ",e.index]})]},s))})]}):"Disabled"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Team ID"}),(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:T.model_info.team_id||"Not Set"})]})]}),el&&(0,c.jsxs)("div",{className:"mt-6 flex justify-end gap-2",children:[(0,c.jsx)(k.Z,{variant:"secondary",onClick:()=>{B.resetFields(),Q(!1),et(!1)},children:"Cancel"}),(0,c.jsx)(k.Z,{variant:"primary",onClick:()=>B.submit(),loading:ee,children:"Save Changes"})]})]})}):(0,c.jsx)(A.Z,{children:"Loading..."})]})]}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(eI.Z,{children:(0,c.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-xs overflow-auto",children:JSON.stringify(T,null,2)})})})]})]}),W&&(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Model"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this model?"})})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(R.ZP,{onClick:eu,className:"ml-2",danger:!0,children:"Delete"}),(0,c.jsx)(R.ZP,{onClick:()=>G(!1),children:"Cancel"})]})]})]})}),Y&&!ec?(0,c.jsx)(sz,{isVisible:Y,onCancel:()=>$(!1),onAddCredential:ed,existingCredential:ea,setIsCredentialModalOpen:$}):(0,c.jsx)(M.Z,{open:Y,onCancel:()=>$(!1),title:"Using Existing Credential",children:(0,c.jsx)(A.Z,{children:T.litellm_params.litellm_credential_name})})]})}var sG=l(67960),sY=e=>{let{selectedProvider:s,providerModels:l,getPlaceholder:t}=e,a=L.Z.useFormInstance(),r=e=>{let s=e.target.value,l=(a.getFieldValue("model_mappings")||[]).map(e=>"custom"===e.public_name||"custom"===e.litellm_model?{public_name:s,litellm_model:s}:e);a.setFieldsValue({model_mappings:l})};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)(L.Z.Item,{label:"LiteLLM Model Name(s)",tooltip:"Actual model name used for making litellm.completion() / litellm.embedding() call.",className:"mb-0",children:[(0,c.jsx)(L.Z.Item,{name:"model",rules:[{required:!0,message:"Please select at least one model."}],noStyle:!0,children:s===n.Azure||s===n.OpenAI_Compatible||s===n.Ollama?(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(S.Z,{placeholder:t(s)})}):l.length>0?(0,c.jsx)(O.default,{mode:"multiple",allowClear:!0,showSearch:!0,placeholder:"Select models",onChange:e=>{let s=Array.isArray(e)?e:[e];if(s.includes("all-wildcard"))a.setFieldsValue({model_name:void 0,model_mappings:[]});else if(JSON.stringify(a.getFieldValue("model"))!==JSON.stringify(s)){let e=s.map(e=>({public_name:e,litellm_model:e}));a.setFieldsValue({model:s,model_mappings:e})}},optionFilterProp:"children",filterOption:(e,s)=>{var l;return(null!==(l=null==s?void 0:s.label)&&void 0!==l?l:"").toLowerCase().includes(e.toLowerCase())},options:[{label:"Custom Model Name (Enter below)",value:"custom"},{label:"All ".concat(s," Models (Wildcard)"),value:"all-wildcard"},...l.map(e=>({label:e,value:e}))],style:{width:"100%"}}):(0,c.jsx)(S.Z,{placeholder:t(s)})}),(0,c.jsx)(L.Z.Item,{noStyle:!0,shouldUpdate:(e,s)=>e.model!==s.model,children:e=>{let{getFieldValue:s}=e,l=s("model")||[];return(Array.isArray(l)?l:[l]).includes("custom")&&(0,c.jsx)(L.Z.Item,{name:"custom_model_name",rules:[{required:!0,message:"Please enter a custom model name."}],className:"mt-2",children:(0,c.jsx)(S.Z,{placeholder:"Enter custom model name",onChange:r})})}})]}),(0,c.jsxs)(sd.Z,{children:[(0,c.jsx)(sm.Z,{span:10}),(0,c.jsx)(sm.Z,{span:10,children:(0,c.jsx)(A.Z,{className:"mb-3 mt-1",children:"Actual model name used for making litellm.completion() call. We loadbalance models with the same public name"})})]})]})},s$=()=>{let e=L.Z.useFormInstance(),[s,l]=(0,d.useState)(0),t=L.Z.useWatch("model",e)||[],a=Array.isArray(t)?t:[t],r=L.Z.useWatch("custom_model_name",e),n=!a.includes("all-wildcard");if((0,d.useEffect)(()=>{if(r&&a.includes("custom")){let s=(e.getFieldValue("model_mappings")||[]).map(e=>"custom"===e.public_name||"custom"===e.litellm_model?{public_name:r,litellm_model:r}:e);e.setFieldValue("model_mappings",s),l(e=>e+1)}},[r,a,e]),(0,d.useEffect)(()=>{if(a.length>0&&!a.includes("all-wildcard")){let s=e.getFieldValue("model_mappings")||[];if(s.length!==a.length||!a.every(e=>s.some(s=>s.public_name===e||"custom"===e&&s.public_name===r))){let s=a.map(e=>"custom"===e&&r?{public_name:r,litellm_model:r}:{public_name:e,litellm_model:e});e.setFieldValue("model_mappings",s),l(e=>e+1)}}},[a,r,e]),!n)return null;let i=[{title:"Public Name",dataIndex:"public_name",key:"public_name",render:(s,l,t)=>(0,c.jsx)(S.Z,{value:s,onChange:s=>{let l=[...e.getFieldValue("model_mappings")];l[t].public_name=s.target.value,e.setFieldValue("model_mappings",l)}})},{title:"LiteLLM Model",dataIndex:"litellm_model",key:"litellm_model"}];return(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(L.Z.Item,{label:"Model Mappings",name:"model_mappings",tooltip:"Map public model names to LiteLLM model names for load balancing",labelCol:{span:10},wrapperCol:{span:16},labelAlign:"left",required:!0,children:(0,c.jsx)(ea.Z,{dataSource:e.getFieldValue("model_mappings"),columns:i,pagination:!1,size:"small"},s)})})},sX=l(90464);let{Link:sQ}=es.default;var s0=e=>{let{showAdvancedSettings:s,setShowAdvancedSettings:l,teams:t}=e,[a]=L.Z.useForm(),[r,n]=d.useState(!1),[i,o]=d.useState("per_token"),[m,u]=d.useState(!1),x=(e,s)=>s&&(isNaN(Number(s))||0>Number(s))?Promise.reject("Please enter a valid positive number"):Promise.resolve(),h=(e,s)=>{if(!s)return Promise.resolve();try{return JSON.parse(s),Promise.resolve()}catch(e){return Promise.reject("Please enter valid JSON")}};return(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)(C.Z,{className:"mt-2 mb-4",children:[(0,c.jsx)(T.Z,{children:(0,c.jsx)("b",{children:"Advanced Settings"})}),(0,c.jsx)(I.Z,{children:(0,c.jsxs)("div",{className:"bg-white rounded-lg",children:[(0,c.jsx)(L.Z.Item,{label:"Custom Pricing",name:"custom_pricing",valuePropName:"checked",className:"mb-4",children:(0,c.jsx)(sV.Z,{onChange:e=>{n(e),e||a.setFieldsValue({input_cost_per_token:void 0,output_cost_per_token:void 0,input_cost_per_second:void 0})},className:"bg-gray-600"})}),r&&(0,c.jsxs)("div",{className:"ml-6 pl-4 border-l-2 border-gray-200",children:[(0,c.jsx)(L.Z.Item,{label:"Pricing Model",name:"pricing_model",className:"mb-4",children:(0,c.jsx)(O.default,{defaultValue:"per_token",onChange:e=>o(e),options:[{value:"per_token",label:"Per Million Tokens"},{value:"per_second",label:"Per Second"}]})}),"per_token"===i?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"Input Cost (per 1M tokens)",name:"input_cost_per_token",rules:[{validator:x}],className:"mb-4",children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"Output Cost (per 1M tokens)",name:"output_cost_per_token",rules:[{validator:x}],className:"mb-4",children:(0,c.jsx)(S.Z,{})})]}):(0,c.jsx)(L.Z.Item,{label:"Cost Per Second",name:"input_cost_per_second",rules:[{validator:x}],className:"mb-4",children:(0,c.jsx)(S.Z,{})})]}),(0,c.jsx)(L.Z.Item,{label:"Use in pass through routes",name:"use_in_pass_through",valuePropName:"checked",className:"mb-4 mt-4",tooltip:(0,c.jsxs)("span",{children:["Allow using these credentials in pass through routes."," ",(0,c.jsx)(sQ,{href:"https://docs.litellm.ai/docs/pass_through/vertex_ai",target:"_blank",children:"Learn more"})]}),children:(0,c.jsx)(sV.Z,{onChange:e=>{let s=a.getFieldValue("litellm_extra_params");try{let l=s?JSON.parse(s):{};e?l.use_in_pass_through=!0:delete l.use_in_pass_through,Object.keys(l).length>0?a.setFieldValue("litellm_extra_params",JSON.stringify(l,null,2)):a.setFieldValue("litellm_extra_params","")}catch(s){e?a.setFieldValue("litellm_extra_params",JSON.stringify({use_in_pass_through:!0},null,2)):a.setFieldValue("litellm_extra_params","")}},className:"bg-gray-600"})}),(0,c.jsx)(sJ,{form:a,showCacheControl:m,onCacheControlChange:e=>{if(u(e),!e){let e=a.getFieldValue("litellm_extra_params");try{let s=e?JSON.parse(e):{};delete s.cache_control_injection_points,Object.keys(s).length>0?a.setFieldValue("litellm_extra_params",JSON.stringify(s,null,2)):a.setFieldValue("litellm_extra_params","")}catch(e){a.setFieldValue("litellm_extra_params","")}}}}),(0,c.jsx)(L.Z.Item,{label:"LiteLLM Params",name:"litellm_extra_params",tooltip:"Optional litellm params used for making a litellm.completion() call.",className:"mb-4 mt-4",rules:[{validator:h}],children:(0,c.jsx)(sX.Z,{rows:4,placeholder:'{ "rpm": 100, "timeout": 0, "stream_timeout": 0 }'})}),(0,c.jsxs)(sd.Z,{className:"mb-4",children:[(0,c.jsx)(sm.Z,{span:10}),(0,c.jsx)(sm.Z,{span:10,children:(0,c.jsxs)(A.Z,{className:"text-gray-600 text-sm",children:["Pass JSON of litellm supported params"," ",(0,c.jsx)(sQ,{href:"https://docs.litellm.ai/docs/completion/input",target:"_blank",children:"litellm.completion() call"})]})})]}),(0,c.jsx)(L.Z.Item,{label:"Model Info",name:"model_info_params",tooltip:"Optional model info params. Returned when calling `/model/info` endpoint.",className:"mb-0",rules:[{validator:h}],children:(0,c.jsx)(sX.Z,{rows:4,placeholder:'{ "mode": "chat" }'})})]})})]})})},s1=l(29),s2=l.n(s1),s4=l(23496),s5=l(35291),s3=l(23639);let{Text:s6}=es.default;var s8=e=>{let{formValues:s,accessToken:l,testMode:t,modelName:a="this model",onClose:r,onTestComplete:n}=e,[i,o]=d.useState(null),[m,u]=d.useState(null),[x,h]=d.useState(null),[p,g]=d.useState(!0),[j,f]=d.useState(!1),[_,y]=d.useState(!1),b=async()=>{g(!0),y(!1),o(null),u(null),h(null),f(!1),await new Promise(e=>setTimeout(e,100));try{console.log("Testing connection with form values:",s);let a=await si(s,l,null);if(!a){console.log("No result from prepareModelAddRequest"),o("Failed to prepare model data. Please check your form inputs."),f(!1),g(!1);return}console.log("Result from prepareModelAddRequest:",a);let{litellmParamsObj:r,modelInfoObj:n,modelName:i}=a[0],c=await (0,v.Hx)(l,r,null==n?void 0:n.mode);if("success"===c.status)D.ZP.success("Connection test successful!"),o(null),f(!0);else{var e,t;let s=(null===(e=c.result)||void 0===e?void 0:e.error)||c.message||"Unknown error";o(s),u(r),h(null===(t=c.result)||void 0===t?void 0:t.raw_request_typed_dict),f(!1)}}catch(e){console.error("Test connection error:",e),o(e instanceof Error?e.message:String(e)),f(!1)}finally{g(!1),n&&n()}};d.useEffect(()=>{let e=setTimeout(()=>{b()},200);return()=>clearTimeout(e)},[]);let Z=e=>e?e.split("stack trace:")[0].trim().replace(/^litellm\.(.*?)Error: /,""):"Unknown error",N="string"==typeof i?Z(i):(null==i?void 0:i.message)?Z(i.message):"Unknown error",w=x?((e,s,l)=>{let t=JSON.stringify(s,null,2).split("\n").map(e=>"  ".concat(e)).join("\n"),a=Object.entries(l).map(e=>{let[s,l]=e;return"-H '".concat(s,": ").concat(l,"'")}).join(" \\\n  ");return"curl -X POST \\\n  ".concat(e," \\\n  ").concat(a?"".concat(a," \\\n  "):"","-H 'Content-Type: application/json' \\\n  -d '{\n").concat(t,"\n  }'")})(x.raw_request_api_base,x.raw_request_body,x.raw_request_headers||{}):"";return(0,c.jsxs)("div",{style:{padding:"24px",borderRadius:"8px",backgroundColor:"#fff"},children:[p?(0,c.jsxs)("div",{style:{textAlign:"center",padding:"32px 20px"},className:"jsx-776cdcbc0448e4ea",children:[(0,c.jsx)("div",{style:{marginBottom:"16px"},className:"jsx-776cdcbc0448e4ea loading-spinner",children:(0,c.jsx)("div",{style:{border:"3px solid #f3f3f3",borderTop:"3px solid #1890ff",borderRadius:"50%",width:"30px",height:"30px",animation:"spin 1s linear infinite",margin:"0 auto"},className:"jsx-776cdcbc0448e4ea"})}),(0,c.jsxs)(s6,{style:{fontSize:"16px"},children:["Testing connection to ",a,"..."]}),(0,c.jsx)(s2(),{id:"776cdcbc0448e4ea",children:"@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}"})]}):j?(0,c.jsxs)("div",{style:{textAlign:"center",padding:"32px 20px"},children:[(0,c.jsx)("div",{style:{color:"#52c41a",fontSize:"32px",marginBottom:"16px"},children:(0,c.jsx)("svg",{viewBox:"64 64 896 896",focusable:"false","data-icon":"check-circle",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",children:(0,c.jsx)("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"})})}),(0,c.jsxs)(s6,{type:"success",style:{fontSize:"18px",fontWeight:500},children:["Connection to ",a," successful!"]})]}):(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:"20px"},children:[(0,c.jsx)(s5.Z,{style:{color:"#ff4d4f",fontSize:"24px",marginRight:"12px"}}),(0,c.jsxs)(s6,{type:"danger",style:{fontSize:"18px",fontWeight:500},children:["Connection to ",a," failed"]})]}),(0,c.jsxs)("div",{style:{backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"8px",padding:"16px",marginBottom:"20px",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.03)"},children:[(0,c.jsx)(s6,{strong:!0,style:{display:"block",marginBottom:"8px"},children:"Error: "}),(0,c.jsx)(s6,{type:"danger",style:{fontSize:"14px",lineHeight:"1.5"},children:N}),i&&(0,c.jsx)("div",{style:{marginTop:"12px"},children:(0,c.jsx)(R.ZP,{type:"link",onClick:()=>y(!_),style:{paddingLeft:0,height:"auto"},children:_?"Hide Details":"Show Details"})})]}),_&&(0,c.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,c.jsx)(s6,{strong:!0,style:{display:"block",marginBottom:"8px",fontSize:"15px"},children:"Troubleshooting Details"}),(0,c.jsx)("pre",{style:{backgroundColor:"#f5f5f5",padding:"16px",borderRadius:"8px",fontSize:"13px",maxHeight:"200px",overflow:"auto",border:"1px solid #e8e8e8",lineHeight:"1.5"},children:"string"==typeof i?i:JSON.stringify(i,null,2)})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(s6,{strong:!0,style:{display:"block",marginBottom:"8px",fontSize:"15px"},children:"API Request"}),(0,c.jsx)("pre",{style:{backgroundColor:"#f5f5f5",padding:"16px",borderRadius:"8px",fontSize:"13px",maxHeight:"250px",overflow:"auto",border:"1px solid #e8e8e8",lineHeight:"1.5"},children:w||"No request data available"}),(0,c.jsx)(R.ZP,{style:{marginTop:"8px"},icon:(0,c.jsx)(s3.Z,{}),onClick:()=>{navigator.clipboard.writeText(w||""),D.ZP.success("Copied to clipboard")},children:"Copy to Clipboard"})]})]})}),(0,c.jsx)(s4.Z,{style:{margin:"24px 0 16px"}}),(0,c.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,c.jsx)(R.ZP,{type:"link",href:"https://docs.litellm.ai/docs/providers",target:"_blank",icon:(0,c.jsx)(J.Z,{}),children:"View Documentation"})})]})};let s7=[{value:"chat",label:"Chat - /chat/completions"},{value:"completion",label:"Completion - /completions"},{value:"embedding",label:"Embedding - /embeddings"},{value:"audio_speech",label:"Audio Speech - /audio/speech"},{value:"audio_transcription",label:"Audio Transcription - /audio/transcriptions"},{value:"image_generation",label:"Image Generation - /images/generations"},{value:"rerank",label:"Rerank - /rerank"},{value:"realtime",label:"Realtime - /realtime"}],{Title:s9,Link:le}=es.default;var ls=e=>{let{form:s,handleOk:l,selectedProvider:t,setSelectedProvider:a,providerModels:r,setProviderModelsFn:i,getPlaceholder:o,uploadProps:m,showAdvancedSettings:u,setShowAdvancedSettings:x,teams:h,credentials:p,accessToken:g,userRole:j}=e,[f,_]=(0,d.useState)("chat"),[v,y]=(0,d.useState)(!1),[b,Z]=(0,d.useState)(!1),[N,w]=(0,d.useState)(""),k=async()=>{Z(!0),w("test-".concat(Date.now())),y(!0)},S=eg.ZL.includes(j);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(s9,{level:2,children:"Add new model"}),(0,c.jsx)(sG.Z,{children:(0,c.jsx)(L.Z,{form:s,onFinish:l,labelCol:{span:10},wrapperCol:{span:16},labelAlign:"left",children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{rules:[{required:!0,message:"Required"}],label:"Provider:",name:"custom_llm_provider",tooltip:"E.g. OpenAI, Azure OpenAI, Anthropic, Bedrock, etc.",labelCol:{span:10},labelAlign:"left",children:(0,c.jsx)(O.default,{showSearch:!0,value:t,onChange:e=>{a(e),i(e),s.setFieldsValue({model:[],model_name:void 0})},children:Object.entries(n).map(e=>{let[s,l]=e;return(0,c.jsx)(O.default.Option,{value:s,children:(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("img",{src:st[l],alt:"".concat(s," logo"),className:"w-5 h-5",onError:e=>{let s=e.target,t=s.parentElement;if(t){let e=document.createElement("div");e.className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs",e.textContent=l.charAt(0),t.replaceChild(e,s)}}}),(0,c.jsx)("span",{children:l})]})},s)})})}),(0,c.jsx)(sY,{selectedProvider:t,providerModels:r,getPlaceholder:o}),(0,c.jsx)(s$,{}),(0,c.jsx)(L.Z.Item,{label:"Mode",name:"mode",className:"mb-1",children:(0,c.jsx)(O.default,{style:{width:"100%"},value:f,onChange:e=>_(e),options:s7})}),(0,c.jsxs)(sd.Z,{children:[(0,c.jsx)(sm.Z,{span:10}),(0,c.jsx)(sm.Z,{span:10,children:(0,c.jsxs)(A.Z,{className:"mb-5 mt-1",children:[(0,c.jsx)("strong",{children:"Optional"})," - LiteLLM endpoint to use when health checking this model ",(0,c.jsx)(le,{href:"https://docs.litellm.ai/docs/proxy/health#health",target:"_blank",children:"Learn more"})]})})]}),(0,c.jsx)("div",{className:"mb-4",children:(0,c.jsx)(es.default.Text,{className:"text-sm text-gray-500 mb-2",children:"Either select existing credentials OR enter new provider credentials below"})}),(0,c.jsx)(L.Z.Item,{label:"Existing Credentials",name:"litellm_credential_name",children:(0,c.jsx)(O.default,{showSearch:!0,placeholder:"Select or search for existing credentials",optionFilterProp:"children",filterOption:(e,s)=>{var l;return(null!==(l=null==s?void 0:s.label)&&void 0!==l?l:"").toLowerCase().includes(e.toLowerCase())},options:[{value:null,label:"None"},...p.map(e=>({value:e.credential_name,label:e.credential_name}))],allowClear:!0})}),(0,c.jsxs)("div",{className:"flex items-center my-4",children:[(0,c.jsx)("div",{className:"flex-grow border-t border-gray-200"}),(0,c.jsx)("span",{className:"px-4 text-gray-500 text-sm",children:"OR"}),(0,c.jsx)("div",{className:"flex-grow border-t border-gray-200"})]}),(0,c.jsx)(L.Z.Item,{noStyle:!0,shouldUpdate:(e,s)=>e.litellm_credential_name!==s.litellm_credential_name||e.provider!==s.provider,children:e=>{let{getFieldValue:s}=e,l=s("litellm_credential_name");return(console.log("\uD83D\uDD11 Credential Name Changed:",l),l)?(0,c.jsx)("div",{className:"text-gray-500 text-sm text-center",children:"Using existing credentials - no additional provider fields needed"}):(0,c.jsx)(sh,{selectedProvider:t,uploadProps:m})}}),(0,c.jsxs)("div",{className:"flex items-center my-4",children:[(0,c.jsx)("div",{className:"flex-grow border-t border-gray-200"}),(0,c.jsx)("span",{className:"px-4 text-gray-500 text-sm",children:"Team Settings"}),(0,c.jsx)("div",{className:"flex-grow border-t border-gray-200"})]}),(0,c.jsx)(L.Z.Item,{label:"Team",name:"team_id",className:"mb-4",tooltip:"Only keys for this team, will be able to call this model.",rules:[{required:!S,message:"Please select a team."}],children:(0,c.jsx)(Q,{teams:h})}),(0,c.jsx)(s0,{showAdvancedSettings:u,setShowAdvancedSettings:x,teams:h}),(0,c.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,c.jsx)(W.Z,{title:"Get help on our github",children:(0,c.jsx)(es.default.Link,{href:"https://github.com/BerriAI/litellm/issues",children:"Need Help?"})}),(0,c.jsxs)("div",{className:"space-x-2",children:[(0,c.jsx)(R.ZP,{onClick:k,loading:b,children:"Test Connect"}),(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Add Model"})]})]})]})})}),(0,c.jsx)(M.Z,{title:"Connection Test Results",open:v,onCancel:()=>{y(!1),Z(!1)},footer:[(0,c.jsx)(R.ZP,{onClick:()=>{y(!1),Z(!1)},children:"Close"},"close")],width:700,children:v&&(0,c.jsx)(s8,{formValues:s.getFieldsValue(),accessToken:g,testMode:f,modelName:s.getFieldValue("model_name")||s.getFieldValue("model"),onClose:()=>{y(!1),Z(!1)},onTestComplete:()=>Z(!1)},N)})]})};function ll(e){let{data:s=[],columns:l,isLoading:t=!1,table:a}=e,[r,n]=d.useState([{id:"model_info.created_at",desc:!0}]),[i]=d.useState("onChange"),[o,m]=d.useState({}),[u,x]=d.useState({}),h=(0,eG.b7)({data:s,columns:l,state:{sorting:r,columnSizing:o,columnVisibility:u},columnResizeMode:i,onSortingChange:n,onColumnSizingChange:m,onColumnVisibilityChange:x,getCoreRowModel:(0,eY.sC)(),getSortedRowModel:(0,eY.tj)(),enableSorting:!0,enableColumnResizing:!0,defaultColumn:{minSize:40,maxSize:500}});return d.useEffect(()=>{a&&(a.current=h)},[h,a]),(0,c.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,c.jsx)("div",{className:"overflow-x-auto",children:(0,c.jsx)("div",{className:"relative min-w-full",children:(0,c.jsxs)(e$.Z,{className:"[&_td]:py-0.5 [&_th]:py-1 w-full",children:[(0,c.jsx)(e0.Z,{children:h.getHeaderGroups().map(e=>(0,c.jsx)(e2.Z,{children:e.headers.map(e=>(0,c.jsxs)(e1.Z,{className:"py-1 h-8 relative ".concat("actions"===e.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)] z-20 w-[120px] ml-8":""),style:{width:"actions"===e.id?120:e.getSize(),position:"actions"===e.id?"sticky":"relative",right:"actions"===e.id?0:"auto"},onClick:e.column.getToggleSortingHandler(),children:[(0,c.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,c.jsx)("div",{className:"flex items-center",children:e.isPlaceholder?null:(0,eG.ie)(e.column.columnDef.header,e.getContext())}),"actions"!==e.id&&(0,c.jsx)("div",{className:"w-4",children:e.column.getIsSorted()?({asc:(0,c.jsx)(e4.Z,{className:"h-4 w-4 text-blue-500"}),desc:(0,c.jsx)(e5.Z,{className:"h-4 w-4 text-blue-500"})})[e.column.getIsSorted()]:(0,c.jsx)(e3.Z,{className:"h-4 w-4 text-gray-400"})})]}),e.column.getCanResize()&&(0,c.jsx)("div",{onMouseDown:e.getResizeHandler(),onTouchStart:e.getResizeHandler(),className:"absolute right-0 top-0 h-full w-2 cursor-col-resize select-none touch-none ".concat(e.column.getIsResizing()?"bg-blue-500":"hover:bg-blue-200")})]},e.id))},e.id))}),(0,c.jsx)(eX.Z,{children:t?(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:l.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"\uD83D\uDE85 Loading models..."})})})}):h.getRowModel().rows.length>0?h.getRowModel().rows.map(e=>(0,c.jsx)(e2.Z,{children:e.getVisibleCells().map(e=>(0,c.jsx)(eQ.Z,{className:"py-0.5 ".concat("actions"===e.column.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)] z-20 w-[120px] ml-8":""),style:{width:"actions"===e.column.id?120:e.column.getSize(),position:"actions"===e.column.id?"sticky":"relative",right:"actions"===e.column.id?0:"auto"},children:(0,eG.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:l.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"No models found"})})})})})]})})})})}let lt=(e,s,l,t,a,r,n,i,o)=>[{header:"Model ID",accessorKey:"model_info.id",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)(W.Z,{title:l.model_info.id,children:(0,c.jsx)("div",{className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left w-full truncate whitespace-nowrap cursor-pointer max-w-[15ch]",onClick:()=>t(l.model_info.id),children:l.model_info.id})})}},{header:"Public Model Name",accessorKey:"model_name",cell:e=>{let{row:s}=e,l=r(s.original)||"-";return(0,c.jsx)(W.Z,{title:l,children:(0,c.jsx)("div",{className:"text-xs truncate whitespace-nowrap",children:l})})}},{header:"Provider",accessorKey:"provider",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[l.provider&&(0,c.jsx)("img",{src:sa(l.provider).logo,alt:"".concat(l.provider," logo"),className:"w-4 h-4",onError:e=>{let s=e.target,t=s.parentElement;if(t){var a;let e=document.createElement("div");e.className="w-4 h-4 rounded-full bg-gray-200 flex items-center justify-center text-xs",e.textContent=(null===(a=l.provider)||void 0===a?void 0:a.charAt(0))||"-",t.replaceChild(e,s)}}}),(0,c.jsx)("p",{className:"text-xs",children:l.provider||"-"})]})}},{header:"LiteLLM Model Name",accessorKey:"litellm_model_name",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)(W.Z,{title:l.litellm_model_name,children:(0,c.jsx)("div",{className:"text-xs truncate whitespace-nowrap",children:l.litellm_model_name||"-"})})}},{header:"Created At",accessorKey:"model_info.created_at",sortingFn:"datetime",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("span",{className:"text-xs",children:l.model_info.created_at?new Date(l.model_info.created_at).toLocaleDateString():"-"})}},{header:"Updated At",accessorKey:"model_info.updated_at",sortingFn:"datetime",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("span",{className:"text-xs",children:l.model_info.updated_at?new Date(l.model_info.updated_at).toLocaleDateString():"-"})}},{header:"Created By",accessorKey:"model_info.created_by",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("span",{className:"text-xs",children:l.model_info.created_by||"-"})}},{header:()=>(0,c.jsx)(W.Z,{title:"Cost per 1M tokens",children:(0,c.jsx)("span",{children:"Input Cost"})}),accessorKey:"input_cost",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("pre",{className:"text-xs",children:l.input_cost||"-"})}},{header:()=>(0,c.jsx)(W.Z,{title:"Cost per 1M tokens",children:(0,c.jsx)("span",{children:"Output Cost"})}),accessorKey:"output_cost",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("pre",{className:"text-xs",children:l.output_cost||"-"})}},{header:"Team ID",accessorKey:"model_info.team_id",cell:e=>{let{row:s}=e,l=s.original;return l.model_info.team_id?(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsx)(W.Z,{title:l.model_info.team_id,children:(0,c.jsxs)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left overflow-hidden truncate max-w-[200px]",onClick:()=>a(l.model_info.team_id),children:[l.model_info.team_id.slice(0,7),"..."]})})}):"-"}},{header:"Credentials",accessorKey:"litellm_credential_name",cell:e=>{let{row:s}=e,l=s.original;return l.litellm_params&&l.litellm_params.litellm_credential_name?(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsxs)(W.Z,{title:l.litellm_params.litellm_credential_name,children:[l.litellm_params.litellm_credential_name.slice(0,7),"..."]})}):(0,c.jsx)("span",{className:"text-gray-400",children:"-"})}},{header:"Status",accessorKey:"model_info.db_model",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("div",{className:"\n          inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium\n          ".concat(l.model_info.db_model?"bg-blue-50 text-blue-600":"bg-gray-100 text-gray-600","\n        "),children:l.model_info.db_model?"DB Model":"Config Model"})}},{id:"actions",header:"",cell:l=>{var a;let{row:r}=l,n=r.original,i="Admin"===e||(null===(a=n.model_info)||void 0===a?void 0:a.created_by)===s;return(0,c.jsxs)("div",{className:"flex items-center justify-end gap-2 pr-4",children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>{i&&(t(n.model_info.id),o(!0))},className:i?"cursor-pointer":"opacity-50 cursor-not-allowed"}),(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>{i&&(t(n.model_info.id),o(!1))},className:i?"cursor-pointer":"opacity-50 cursor-not-allowed"})]})}}],{Title:la,Link:lr}=es.default,ln={"BadRequestError (400)":"BadRequestErrorRetries","AuthenticationError  (401)":"AuthenticationErrorRetries","TimeoutError (408)":"TimeoutErrorRetries","RateLimitError (429)":"RateLimitErrorRetries","ContentPolicyViolationError (400)":"ContentPolicyViolationErrorRetries","InternalServerError (500)":"InternalServerErrorRetries"};var li=e=>{let{accessToken:s,token:l,userRole:t,userID:a,modelData:r={data:[]},keys:i,setModelData:o,premiumUser:m,teams:u}=e,[x,h]=(0,d.useState)([]),[p]=L.Z.useForm(),[g,j]=(0,d.useState)(null),[f,_]=(0,d.useState)(""),[y,b]=(0,d.useState)([]);Object.values(n).filter(e=>isNaN(Number(e)));let[Z,N]=(0,d.useState)([]),[S,C]=(0,d.useState)(n.OpenAI),[I,T]=(0,d.useState)(""),[P,O]=(0,d.useState)(!1),[M,F]=(0,d.useState)(null),[R,q]=(0,d.useState)([]),[U,z]=(0,d.useState)([]),[V,K]=(0,d.useState)(null),[B,J]=(0,d.useState)([]),[W,G]=(0,d.useState)([]),[Y,$]=(0,d.useState)([]),[X,Q]=(0,d.useState)([]),[el,et]=(0,d.useState)([]),[ea,er]=(0,d.useState)([]),[en,ei]=(0,d.useState)([]),[eo,ec]=(0,d.useState)([]),[ed,em]=(0,d.useState)([]),[eu,ex]=(0,d.useState)({from:new Date(Date.now()-6048e5),to:new Date}),[eh,ep]=(0,d.useState)(null),[ej,ef]=(0,d.useState)(0),[e_,ev]=(0,d.useState)({}),[ey,eb]=(0,d.useState)([]),[eZ,eN]=(0,d.useState)(!1),[ew,ek]=(0,d.useState)(null),[eC,eL]=(0,d.useState)(null),[eM,eF]=(0,d.useState)([]),[eR,eq]=(0,d.useState)([]),[ez,eV]=(0,d.useState)(!1),[eK,eB]=(0,d.useState)(null),[eH,eJ]=(0,d.useState)(!1),[eW,eG]=(0,d.useState)(null),[eY,e4]=(0,d.useState)(null),[e5,e3]=(0,d.useState)(null),[e6,e8]=(0,d.useState)(!1),[e7,e9]=(0,d.useState)(!1),sl=(0,d.useRef)(null),st=(0,d.useRef)(null),sa=async(e,l,r)=>{if(console.log("Updating model metrics for group:",e),!s||!a||!t||!l||!r)return;console.log("inside updateModelMetrics - startTime:",l,"endTime:",r),K(e);let n=null==ew?void 0:ew.token;void 0===n&&(n=null);let i=eC;void 0===i&&(i=null),l.setHours(0),l.setMinutes(0),l.setSeconds(0),r.setHours(23),r.setMinutes(59),r.setSeconds(59);try{let o=await (0,v.o6)(s,a,t,e,l.toISOString(),r.toISOString(),n,i);console.log("Model metrics response:",o),G(o.data),$(o.all_api_bases);let c=await (0,v.Rg)(s,e,l.toISOString(),r.toISOString());Q(c.data),et(c.all_api_bases);let d=await (0,v.N8)(s,a,t,e,l.toISOString(),r.toISOString(),n,i);console.log("Model exceptions response:",d),er(d.data),ei(d.exception_types);let m=await (0,v.fP)(s,a,t,e,l.toISOString(),r.toISOString(),n,i);if(console.log("slowResponses:",m),em(m),e){let t=await (0,v.n$)(s,null==l?void 0:l.toISOString().split("T")[0],null==r?void 0:r.toISOString().split("T")[0],e);ev(t);let a=await (0,v.v9)(s,null==l?void 0:l.toISOString().split("T")[0],null==r?void 0:r.toISOString().split("T")[0],e);eb(a)}}catch(e){console.error("Failed to fetch model metrics",e)}},si=async e=>{try{let s=await (0,v.N3)(e);console.log("credentials: ".concat(JSON.stringify(s))),eq(s.credentials)}catch(e){console.error("Error fetching credentials:",e)}};(0,d.useEffect)(()=>{sa(V,eu.from,eu.to)},[ew,eC,eY]),(0,d.useEffect)(()=>{let e=e=>{sl.current&&!sl.current.contains(e.target)&&e9(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let sc={name:"file",accept:".json",beforeUpload:e=>{if("application/json"===e.type){let s=new FileReader;s.onload=e=>{if(e.target){let s=e.target.result;console.log("Resetting vertex_credentials to JSON; jsonStr: ".concat(s)),p.setFieldsValue({vertex_credentials:s}),console.log("Form values right after setting:",p.getFieldsValue())}},s.readAsText(e)}return!1},onChange(e){console.log("Upload onChange triggered with values:",e),console.log("Current form values:",p.getFieldsValue()),"uploading"!==e.file.status&&console.log(e.file,e.fileList),"done"===e.file.status?D.ZP.success("".concat(e.file.name," file uploaded successfully")):"error"===e.file.status&&D.ZP.error("".concat(e.file.name," file upload failed."))}},sd=()=>{_(new Date().toLocaleString())},su=async()=>{if(!s){console.error("Access token is missing");return}console.log("new modelGroupRetryPolicy:",eh);try{await (0,v.K_)(s,{router_settings:{model_group_retry_policy:eh}}),D.ZP.success("Retry settings saved successfully")}catch(e){console.error("Failed to save retry settings:",e),D.ZP.error("Failed to save retry settings")}};if((0,d.useEffect)(()=>{if(!s||!l||!t||!a)return;let e=async()=>{try{var e,l,r,n,i,c,d,m,u,x,h,p;let g=await (0,v.AZ)(s,a,t);console.log("Model data response:",g.data),o(g);let j=await (0,v.hy)(s);j&&N(j);let f=new Set;for(let e=0;e<g.data.length;e++){let s=g.data[e];f.add(s.model_name)}console.log("all_model_groups:",f);let _=Array.from(f);_=_.sort(),q(_),console.log("array_model_groups:",_);let y="all";_.length>0&&(y=_[_.length-1],console.log("_initial_model_group:",y)),console.log("selectedModelGroup:",V);let b=await (0,v.o6)(s,a,t,y,null===(e=eu.from)||void 0===e?void 0:e.toISOString(),null===(l=eu.to)||void 0===l?void 0:l.toISOString(),null==ew?void 0:ew.token,eC);console.log("Model metrics response:",b),G(b.data),$(b.all_api_bases);let Z=await (0,v.Rg)(s,y,null===(r=eu.from)||void 0===r?void 0:r.toISOString(),null===(n=eu.to)||void 0===n?void 0:n.toISOString());Q(Z.data),et(Z.all_api_bases);let w=await (0,v.N8)(s,a,t,y,null===(i=eu.from)||void 0===i?void 0:i.toISOString(),null===(c=eu.to)||void 0===c?void 0:c.toISOString(),null==ew?void 0:ew.token,eC);console.log("Model exceptions response:",w),er(w.data),ei(w.exception_types);let k=await (0,v.fP)(s,a,t,y,null===(d=eu.from)||void 0===d?void 0:d.toISOString(),null===(m=eu.to)||void 0===m?void 0:m.toISOString(),null==ew?void 0:ew.token,eC),S=await (0,v.n$)(s,null===(u=eu.from)||void 0===u?void 0:u.toISOString().split("T")[0],null===(x=eu.to)||void 0===x?void 0:x.toISOString().split("T")[0],y);ev(S);let C=await (0,v.v9)(s,null===(h=eu.from)||void 0===h?void 0:h.toISOString().split("T")[0],null===(p=eu.to)||void 0===p?void 0:p.toISOString().split("T")[0],y);eb(C),console.log("dailyExceptions:",S),console.log("dailyExceptionsPerDeplyment:",C),console.log("slowResponses:",k),em(k);let I=await (0,v.j2)(s);eF(null==I?void 0:I.end_users);let T=(await (0,v.BL)(s,a,t)).router_settings;console.log("routerSettingsInfo:",T);let A=T.model_group_retry_policy,E=T.num_retries;console.log("model_group_retry_policy:",A),console.log("default_retries:",E),ep(A),ef(E)}catch(e){console.error("There was an error fetching the model data",e)}};s&&l&&t&&a&&e();let r=async()=>{let e=await (0,v.qm)(s);console.log("received model cost map data: ".concat(Object.keys(e))),j(e)};null==g&&r(),sd()},[s,l,t,a,g,f,eY]),!r||!s||!l||!t||!a)return(0,c.jsx)("div",{children:"Loading..."});let sx=[],sh=[];for(let e=0;e<r.data.length;e++){var sp,sg,sj;let s=r.data[e],l=null==s?void 0:null===(sp=s.litellm_params)||void 0===sp?void 0:sp.model,t=null==s?void 0:null===(sg=s.litellm_params)||void 0===sg?void 0:sg.custom_llm_provider,a=null==s?void 0:s.model_info,n="",i="Undefined",o="Undefined",c="Undefined",d="Undefined",m={},u=e=>(console.log("GET PROVIDER CALLED! - ".concat(g)),null!=g&&"object"==typeof g&&e in g)?g[e].litellm_provider:"openai";if(l){let e=l.split("/"),s=e[0];(n=t)||(n=1===e.length?u(l):s)}else n="-";a&&(i=null==a?void 0:a.input_cost_per_token,o=null==a?void 0:a.output_cost_per_token,c=null==a?void 0:a.max_tokens,d=null==a?void 0:a.max_input_tokens),(null==s?void 0:s.litellm_params)&&(m=Object.fromEntries(Object.entries(null==s?void 0:s.litellm_params).filter(e=>{let[s]=e;return"model"!==s&&"api_base"!==s}))),r.data[e].provider=n,r.data[e].input_cost=i,r.data[e].output_cost=o,r.data[e].litellm_model_name=l,sh.push(n),r.data[e].input_cost&&(r.data[e].input_cost=(1e6*Number(r.data[e].input_cost)).toFixed(2)),r.data[e].output_cost&&(r.data[e].output_cost=(1e6*Number(r.data[e].output_cost)).toFixed(2)),r.data[e].max_tokens=c,r.data[e].max_input_tokens=d,r.data[e].api_base=null==s?void 0:null===(sj=s.litellm_params)||void 0===sj?void 0:sj.api_base,r.data[e].cleanedLitellmParams=m,sx.push(s.model_name),console.log(r.data[e])}if(t&&"Admin Viewer"==t){let{Title:e,Paragraph:s}=es.default;return(0,c.jsxs)("div",{children:[(0,c.jsx)(e,{level:1,children:"Access Denied"}),(0,c.jsx)(s,{children:"Ask your proxy admin for access to view all models"})]})}let sw=async()=>{try{D.ZP.info("Running health check..."),T("");let e=await (0,v.EY)(s);T(e)}catch(e){console.error("Error running health check:",e),T("Error running health check")}},sS=(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"mb-1",children:"Select API Key Name"}),m?(0,c.jsxs)("div",{children:[(0,c.jsxs)(eS.Z,{defaultValue:"all-keys",children:[(0,c.jsx)(ee.Z,{value:"all-keys",onClick:()=>{ek(null)},children:"All Keys"},"all-keys"),null==i?void 0:i.map((e,s)=>e&&null!==e.key_alias&&e.key_alias.length>0?(0,c.jsx)(ee.Z,{value:String(s),onClick:()=>{ek(e)},children:e.key_alias},s):null)]}),(0,c.jsx)(A.Z,{className:"mt-1",children:"Select Customer Name"}),(0,c.jsxs)(eS.Z,{defaultValue:"all-customers",children:[(0,c.jsx)(ee.Z,{value:"all-customers",onClick:()=>{eL(null)},children:"All Customers"},"all-customers"),null==eM?void 0:eM.map((e,s)=>(0,c.jsx)(ee.Z,{value:e,onClick:()=>{eL(e)},children:e},s))]}),(0,c.jsx)(A.Z,{className:"mt-1",children:"Select Team"}),(0,c.jsxs)(eS.Z,{className:"w-64 relative z-50",defaultValue:"all",value:null!=e5?e5:"all",onValueChange:e=>e3("all"===e?null:e),children:[(0,c.jsx)(ee.Z,{value:"all",children:"All Teams"}),null==u?void 0:u.filter(e=>e.team_id).map(e=>(0,c.jsx)(ee.Z,{value:e.team_id,children:e.team_alias?"".concat(e.team_alias," (").concat(e.team_id.slice(0,8),"...)"):"Team ".concat(e.team_id.slice(0,8),"...")},e.team_id))]})]}):(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"mt-1",children:"Select Team"}),(0,c.jsxs)(eS.Z,{className:"w-64 relative z-50",defaultValue:"all",value:null!=e5?e5:"all",onValueChange:e=>e3("all"===e?null:e),children:[(0,c.jsx)(ee.Z,{value:"all",children:"All Teams"}),null==u?void 0:u.filter(e=>e.team_id).map(e=>(0,c.jsx)(ee.Z,{value:e.team_id,children:e.team_alias?"".concat(e.team_alias," (").concat(e.team_id.slice(0,8),"...)"):"Team ".concat(e.team_id.slice(0,8),"...")},e.team_id))]})]})]}),sC=e=>{var s,l;let{payload:t,active:a}=e;if(!a||!t)return null;let r=null===(l=t[0])||void 0===l?void 0:null===(s=l.payload)||void 0===s?void 0:s.date,n=t.sort((e,s)=>s.value-e.value);if(n.length>5){let e=n.length-5;(n=n.slice(0,5)).push({dataKey:"".concat(e," other deployments"),value:t.slice(5).reduce((e,s)=>e+s.value,0),color:"gray"})}return(0,c.jsxs)("div",{className:"w-150 rounded-tremor-default border border-tremor-border bg-tremor-background p-2 text-tremor-default shadow-tremor-dropdown",children:[r&&(0,c.jsxs)("p",{className:"text-tremor-content-emphasis mb-2",children:["Date: ",r]}),n.map((e,s)=>{let l=parseFloat(e.value.toFixed(5)),t=0===l&&e.value>0?"<0.00001":l.toFixed(5);return(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("div",{className:"w-2 h-2 mt-1 rounded-full bg-".concat(e.color,"-500")}),(0,c.jsx)("p",{className:"text-tremor-content",children:e.dataKey})]}),(0,c.jsx)("p",{className:"font-medium text-tremor-content-emphasis text-righ ml-2",children:t})]},s)})]})};console.log("selectedProvider: ".concat(S)),console.log("providerModels.length: ".concat(y.length));let sI=Object.keys(n).find(e=>n[e]===S);return(sI&&Z&&Z.find(e=>e.name===ss[sI]),eW)?(0,c.jsx)("div",{className:"w-full h-full",children:(0,c.jsx)(sF,{teamId:eW,onClose:()=>eG(null),accessToken:s,is_team_admin:"Admin"===t,is_proxy_admin:"Proxy Admin"===t,userModels:sx,editTeam:!1,onUpdate:sd})}):(0,c.jsx)("div",{style:{width:"100%",height:"100%"},children:eK?(0,c.jsx)(sW,{modelId:eK,editModel:!0,onClose:()=>{eB(null),eJ(!1)},modelData:r.data.find(e=>e.model_info.id===eK),accessToken:s,userID:a,userRole:t,setEditModalVisible:O,setSelectedModel:F,onModelUpdate:e=>{o({...r,data:r.data.map(s=>s.model_info.id===e.model_info.id?e:s)}),sd()}}):(0,c.jsxs)(eA.Z,{className:"gap-2 p-8 h-[75vh] w-full mt-2",children:[(0,c.jsxs)(eE.Z,{className:"flex justify-between mt-2 w-full items-center",children:[(0,c.jsxs)("div",{className:"flex",children:[eg.ZL.includes(t)?(0,c.jsx)(eT.Z,{children:"All Models"}):(0,c.jsx)(eT.Z,{children:"Your Models"}),(0,c.jsx)(eT.Z,{children:"Add Model"}),eg.ZL.includes(t)&&(0,c.jsx)(eT.Z,{children:"LLM Credentials"}),eg.ZL.includes(t)&&(0,c.jsx)(eT.Z,{children:(0,c.jsx)("pre",{children:"/health Models"})}),eg.ZL.includes(t)&&(0,c.jsx)(eT.Z,{children:"Model Analytics"}),eg.ZL.includes(t)&&(0,c.jsx)(eT.Z,{children:"Model Retry Settings"})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[f&&(0,c.jsxs)(A.Z,{children:["Last Refreshed: ",f]}),(0,c.jsx)(sy.Z,{icon:eD.Z,variant:"shadow",size:"xs",className:"self-center",onClick:sd})]})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsx)(w.Z,{children:(0,c.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,c.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,c.jsxs)("div",{children:[(0,c.jsx)(E.Z,{children:"Model Management"}),eg.ZL.includes(t)?(0,c.jsx)(A.Z,{className:"text-tremor-content",children:"Add and manage models for the proxy"}):(0,c.jsx)(A.Z,{className:"text-tremor-content",children:"Add models for teams you are an admin for."})]})}),(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsx)("div",{className:"border-b px-6 py-4",children:(0,c.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,c.jsx)("div",{className:"flex items-center justify-between",children:(0,c.jsxs)("div",{className:"flex items-center gap-3",children:[(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsx)(A.Z,{children:"Filter by Public Model Name:"}),(0,c.jsxs)(eS.Z,{className:"w-64",defaultValue:null!=V?V:"all",onValueChange:e=>K("all"===e?"all":e),value:null!=V?V:"all",children:[(0,c.jsx)(ee.Z,{value:"all",children:"All Models"}),R.map((e,s)=>(0,c.jsx)(ee.Z,{value:e,children:e},s))]})]}),(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsx)(A.Z,{children:"Filter by Team:"}),(0,c.jsxs)(eS.Z,{className:"w-64",defaultValue:"all",value:null!=e5?e5:"all",onValueChange:e=>e3("all"===e?null:e),children:[(0,c.jsx)(ee.Z,{value:"all",children:"All Teams"}),null==u?void 0:u.filter(e=>e.team_id).map(e=>(0,c.jsx)(ee.Z,{value:e.team_id,children:e.team_alias?"".concat(e.team_alias," (").concat(e.team_id.slice(0,8),"...)"):"Team ".concat(e.team_id.slice(0,8),"...")},e.team_id))]})]})]})}),(0,c.jsx)("div",{className:"flex justify-between items-center",children:(0,c.jsxs)(A.Z,{className:"text-sm text-gray-700",children:["Showing ",r&&r.data.length>0?r.data.length:0," results"]})})]})}),(0,c.jsx)(ll,{columns:lt(t,a,m,eB,eG,s_,e=>{F(e),O(!0)},sd,eJ),data:r.data.filter(e=>("all"===V||e.model_name===V||!V)&&("all"===e5||e.model_info.team_id===e5||!e5)),isLoading:!1,table:st})]})]})})}),(0,c.jsx)(eP.Z,{className:"h-full",children:(0,c.jsx)(ls,{form:p,handleOk:()=>{p.validateFields().then(e=>{so(e,s,p,sd)}).catch(e=>{console.error("Validation failed:",e)})},selectedProvider:S,setSelectedProvider:C,providerModels:y,setProviderModelsFn:e=>{let s=sn(e,g);b(s),console.log("providerModels: ".concat(s))},getPlaceholder:sr,uploadProps:sc,showAdvancedSettings:ez,setShowAdvancedSettings:eV,teams:u,credentials:eR,accessToken:s,userRole:t})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(sf,{accessToken:s,uploadProps:sc,credentialList:eR,fetchCredentials:si})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"`/health` will run a very small request through your models configured on litellm"}),(0,c.jsx)(k.Z,{onClick:sw,children:"Run `/health`"}),I&&(0,c.jsx)("pre",{children:JSON.stringify(I,null,2)})]})}),(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)(w.Z,{numItems:4,className:"mt-2 mb-2",children:[(0,c.jsxs)(sm.Z,{children:[(0,c.jsx)(A.Z,{children:"Select Time Range"}),(0,c.jsx)(sv.Z,{enableSelect:!0,value:eu,className:"mr-2",onValueChange:e=>{ex(e),sa(V,e.from,e.to)}})]}),(0,c.jsxs)(sm.Z,{className:"ml-2",children:[(0,c.jsx)(A.Z,{children:"Select Model Group"}),(0,c.jsx)(eS.Z,{defaultValue:V||R[0],value:V||R[0],children:R.map((e,s)=>(0,c.jsx)(ee.Z,{value:e,onClick:()=>sa(e,eu.from,eu.to),children:e},s))})]}),(0,c.jsx)(sm.Z,{children:(0,c.jsx)(sN.Z,{trigger:"click",content:sS,overlayStyle:{width:"20vw"},children:(0,c.jsx)(k.Z,{icon:eU.Z,size:"md",variant:"secondary",className:"mt-4 ml-2",style:{border:"none"},onClick:()=>eN(!0)})})})]}),(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsx)(sm.Z,{children:(0,c.jsx)(eI.Z,{className:"mr-2 max-h-[400px] min-h-[400px]",children:(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{variant:"line",defaultValue:"1",children:[(0,c.jsx)(eT.Z,{value:"1",children:"Avg. Latency per Token"}),(0,c.jsx)(eT.Z,{value:"2",children:"Time to first token"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsxs)(eP.Z,{children:[(0,c.jsx)("p",{className:"text-gray-500 italic",children:" (seconds/token)"}),(0,c.jsx)(A.Z,{className:"text-gray-500 italic mt-1 mb-1",children:"average Latency for successfull requests divided by the total tokens"}),W&&Y&&(0,c.jsx)(sb.Z,{title:"Model Latency",className:"h-72",data:W,showLegend:!1,index:"date",categories:Y,connectNulls:!0,customTooltip:sC})]}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(sk,{modelMetrics:X,modelMetricsCategories:el,customTooltip:sC,premiumUser:m})})]})]})})}),(0,c.jsx)(sm.Z,{children:(0,c.jsx)(eI.Z,{className:"ml-2 max-h-[400px] min-h-[400px]  overflow-y-auto",children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Deployment"}),(0,c.jsx)(e1.Z,{children:"Success Responses"}),(0,c.jsxs)(e1.Z,{children:["Slow Responses ",(0,c.jsx)("p",{children:"Success Responses taking 600+s"})]})]})}),(0,c.jsx)(eX.Z,{children:ed.map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.api_base}),(0,c.jsx)(eQ.Z,{children:e.total_count}),(0,c.jsx)(eQ.Z,{children:e.slow_count})]},s))})]})})})]}),(0,c.jsx)(w.Z,{numItems:1,className:"gap-2 w-full mt-2",children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)(E.Z,{children:["All Exceptions for ",V]}),(0,c.jsx)(sZ.Z,{className:"h-60",data:ea,index:"model",categories:en,stack:!0,yAxisWidth:30})]})}),(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 w-full mt-2",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)(E.Z,{children:["All Up Rate Limit Errors (429) for ",V]}),(0,c.jsxs)(w.Z,{numItems:1,children:[(0,c.jsxs)(sm.Z,{children:[(0,c.jsxs)(se.Z,{style:{fontSize:"15px",fontWeight:"normal",color:"#535452"},children:["Num Rate Limit Errors ",e_.sum_num_rate_limit_exceptions]}),(0,c.jsx)(sZ.Z,{className:"h-40",data:e_.daily_data,index:"date",colors:["rose"],categories:["num_rate_limit_exceptions"],onValueChange:e=>console.log(e)})]}),(0,c.jsx)(sm.Z,{})]})]}),m?(0,c.jsx)(c.Fragment,{children:ey.map((e,s)=>(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:e.api_base?e.api_base:"Unknown API Base"}),(0,c.jsx)(w.Z,{numItems:1,children:(0,c.jsxs)(sm.Z,{children:[(0,c.jsxs)(se.Z,{style:{fontSize:"15px",fontWeight:"normal",color:"#535452"},children:["Num Rate Limit Errors (429) ",e.sum_num_rate_limit_exceptions]}),(0,c.jsx)(sZ.Z,{className:"h-40",data:e.daily_data,index:"date",colors:["rose"],categories:["num_rate_limit_exceptions"],onValueChange:e=>console.log(e)})]})})]},s))}):(0,c.jsx)(c.Fragment,{children:ey&&ey.length>0&&ey.slice(0,1).map((e,s)=>(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"✨ Rate Limit Errors by Deployment"}),(0,c.jsx)("p",{className:"mb-2 text-gray-500 italic text-[12px]",children:"Upgrade to see exceptions for all deployments"}),(0,c.jsx)(k.Z,{variant:"primary",className:"mb-2",children:(0,c.jsx)("a",{href:"https://forms.gle/W3U4PZpJGFHWtHyA9",target:"_blank",children:"Get Free Trial"})}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:e.api_base}),(0,c.jsx)(w.Z,{numItems:1,children:(0,c.jsxs)(sm.Z,{children:[(0,c.jsxs)(se.Z,{style:{fontSize:"15px",fontWeight:"normal",color:"#535452"},children:["Num Rate Limit Errors ",e.sum_num_rate_limit_exceptions]}),(0,c.jsx)(sZ.Z,{className:"h-40",data:e.daily_data,index:"date",colors:["rose"],categories:["num_rate_limit_exceptions"],onValueChange:e=>console.log(e)})]})})]})]},s))})]})]}),(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(A.Z,{children:"Filter by Public Model Name"}),(0,c.jsx)(eS.Z,{className:"mb-4 mt-2 ml-2 w-50",defaultValue:V||R[0],value:V||R[0],onValueChange:e=>K(e),children:R.map((e,s)=>(0,c.jsx)(ee.Z,{value:e,onClick:()=>K(e),children:e},s))})]}),(0,c.jsxs)(E.Z,{children:["Retry Policy for ",V]}),(0,c.jsx)(A.Z,{className:"mb-6",children:"How many retries should be attempted based on the Exception"}),ln&&(0,c.jsx)("table",{children:(0,c.jsx)("tbody",{children:Object.entries(ln).map((e,s)=>{var l;let[t,a]=e,r=null==eh?void 0:null===(l=eh[V])||void 0===l?void 0:l[a];return null==r&&(r=ej),(0,c.jsxs)("tr",{className:"flex justify-between items-center mt-2",children:[(0,c.jsx)("td",{children:(0,c.jsx)(A.Z,{children:t})}),(0,c.jsx)("td",{children:(0,c.jsx)(H.Z,{className:"ml-5",value:r,min:0,step:1,onChange:e=>{ep(s=>{var l;let t=null!==(l=null==s?void 0:s[V])&&void 0!==l?l:{};return{...null!=s?s:{},[V]:{...t,[a]:e}}})}})})]},s)})})}),(0,c.jsx)(k.Z,{className:"mt-6 mr-8",onClick:su,children:"Save"})]})]})]})})},lo=e=>{let{visible:s,possibleUIRoles:l,onCancel:t,user:a,onSubmit:r}=e,[n,i]=(0,d.useState)(a),[o]=L.Z.useForm();(0,d.useEffect)(()=>{o.resetFields()},[a]);let m=async()=>{o.resetFields(),t()},u=async e=>{r(e),o.resetFields(),t()};return a?(0,c.jsx)(M.Z,{visible:s,onCancel:m,footer:null,title:"Edit User "+a.user_id,width:1e3,children:(0,c.jsx)(L.Z,{form:o,onFinish:u,initialValues:a,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{className:"mt-8",label:"User Email",tooltip:"Email of the User",name:"user_email",children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"user_id",name:"user_id",hidden:!0,children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"User Role",name:"user_role",children:(0,c.jsx)(O.default,{children:l&&Object.entries(l).map(e=>{let[s,{ui_label:l,description:t}]=e;return(0,c.jsx)(ee.Z,{value:s,title:l,children:(0,c.jsxs)("div",{className:"flex",children:[l," ",(0,c.jsx)("p",{className:"ml-2",style:{color:"gray",fontSize:"12px"},children:t})]})},s)})})}),(0,c.jsx)(L.Z.Item,{label:"Spend (USD)",name:"spend",tooltip:"(float) - Spend of all LLM calls completed by this user",help:"Across all keys (including keys with team_id).",children:(0,c.jsx)(H.Z,{min:0,step:.01})}),(0,c.jsx)(L.Z.Item,{label:"User Budget (USD)",name:"max_budget",tooltip:"(float) - Maximum budget of this user",help:"Maximum budget of this user.",children:(0,c.jsx)(z,{min:0,step:.01})}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Save"})})]})})}):null},lc=l(15731);let ld=(e,s,l)=>[{header:"User ID",accessorKey:"user_id",cell:e=>{let{row:s}=e;return(0,c.jsx)(W.Z,{title:s.original.user_id,children:(0,c.jsx)("span",{className:"text-xs",children:s.original.user_id?"".concat(s.original.user_id.slice(0,7),"..."):"-"})})}},{header:"User Email",accessorKey:"user_email",cell:e=>{let{row:s}=e;return(0,c.jsx)("span",{className:"text-xs",children:s.original.user_email||"-"})}},{header:"Global Proxy Role",accessorKey:"user_role",cell:s=>{var l;let{row:t}=s;return(0,c.jsx)("span",{className:"text-xs",children:(null==e?void 0:null===(l=e[t.original.user_role])||void 0===l?void 0:l.ui_label)||"-"})}},{header:"User Spend ($ USD)",accessorKey:"spend",cell:e=>{let{row:s}=e;return(0,c.jsx)("span",{className:"text-xs",children:s.original.spend?s.original.spend.toFixed(2):"-"})}},{header:"User Max Budget ($ USD)",accessorKey:"max_budget",cell:e=>{let{row:s}=e;return(0,c.jsx)("span",{className:"text-xs",children:null!==s.original.max_budget?s.original.max_budget:"Unlimited"})}},{header:()=>(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsx)("span",{children:"SSO ID"}),(0,c.jsx)(W.Z,{title:"SSO ID is the ID of the user in the SSO provider. If the user is not using SSO, this will be null.",children:(0,c.jsx)(lc.Z,{className:"w-4 h-4"})})]}),accessorKey:"sso_user_id",cell:e=>{let{row:s}=e;return(0,c.jsx)("span",{className:"text-xs",children:null!==s.original.sso_user_id?s.original.sso_user_id:"-"})}},{header:"API Keys",accessorKey:"key_count",cell:e=>{let{row:s}=e;return(0,c.jsx)(w.Z,{numItems:2,children:s.original.key_count>0?(0,c.jsxs)(eC.Z,{size:"xs",color:"indigo",children:[s.original.key_count," Keys"]}):(0,c.jsx)(eC.Z,{size:"xs",color:"gray",children:"No Keys"})})}},{header:"Created At",accessorKey:"created_at",sortingFn:"datetime",cell:e=>{let{row:s}=e;return(0,c.jsx)("span",{className:"text-xs",children:s.original.created_at?new Date(s.original.created_at).toLocaleDateString():"-"})}},{header:"Updated At",accessorKey:"updated_at",sortingFn:"datetime",cell:e=>{let{row:s}=e;return(0,c.jsx)("span",{className:"text-xs",children:s.original.updated_at?new Date(s.original.updated_at).toLocaleDateString():"-"})}},{id:"actions",header:"",cell:e=>{let{row:t}=e;return(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>s(t.original)}),(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>l(t.original.user_id)})]})}}];var lm=l(16853);function lu(e){let{userData:s,onCancel:l,onSubmit:t,teams:a,accessToken:r,userID:n,userRole:i,userModels:o,possibleUIRoles:m}=e,[u]=L.Z.useForm();return d.useEffect(()=>{var e,l,t,a,r;u.setFieldsValue({user_id:s.user_id,user_email:null===(e=s.user_info)||void 0===e?void 0:e.user_email,user_role:null===(l=s.user_info)||void 0===l?void 0:l.user_role,models:(null===(t=s.user_info)||void 0===t?void 0:t.models)||[],max_budget:null===(a=s.user_info)||void 0===a?void 0:a.max_budget,metadata:(null===(r=s.user_info)||void 0===r?void 0:r.metadata)?JSON.stringify(s.user_info.metadata,null,2):void 0})},[s,u]),(0,c.jsxs)(L.Z,{form:u,onFinish:e=>{if(e.metadata&&"string"==typeof e.metadata)try{e.metadata=JSON.parse(e.metadata)}catch(e){console.error("Error parsing metadata JSON:",e);return}t(e)},layout:"vertical",children:[(0,c.jsx)(L.Z.Item,{label:"User ID",name:"user_id",children:(0,c.jsx)(S.Z,{disabled:!0})}),(0,c.jsx)(L.Z.Item,{label:"Email",name:"user_email",children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Global Proxy Role"," ",(0,c.jsx)(W.Z,{title:"This is the role that the user will globally on the proxy. This role is independent of any team/org specific roles.",children:(0,c.jsx)(J.Z,{})})]}),name:"user_role",children:(0,c.jsx)(O.default,{children:m&&Object.entries(m).map(e=>{let[s,{ui_label:l,description:t}]=e;return(0,c.jsx)(ee.Z,{value:s,title:l,children:(0,c.jsxs)("div",{className:"flex",children:[l," ",(0,c.jsx)("p",{className:"ml-2",style:{color:"gray",fontSize:"12px"},children:t})]})},s)})})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Personal Models"," ",(0,c.jsx)(W.Z,{title:"Select which models this user can access outside of team-scope. Choose 'All Proxy Models' to grant access to all models available on the proxy.",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"models",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",style:{width:"100%"},disabled:!eg.ZL.includes(i||""),children:[(0,c.jsx)(O.default.Option,{value:"all-proxy-models",children:"All Proxy Models"},"all-proxy-models"),o.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:K(e)},e))]})}),(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(H.Z,{step:.01,precision:2,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",children:(0,c.jsx)(lm.Z,{rows:4,placeholder:"Enter metadata as JSON"})}),(0,c.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,c.jsx)(k.Z,{variant:"secondary",onClick:l,children:"Cancel"}),(0,c.jsx)(k.Z,{type:"submit",children:"Save Changes"})]})]})}function lx(e){var s,l,t,a,r,n,i,o,m,u,x,h,p,g,j,f,_,y,b,Z,N,S,C,I,T,P,O;let{userId:L,onClose:M,accessToken:F,userRole:R,onDelete:q,possibleUIRoles:U}=e,[z,V]=(0,d.useState)(null),[K,B]=(0,d.useState)(!1),[H,J]=(0,d.useState)(!0),[W,G]=(0,d.useState)(!1),[Y,$]=(0,d.useState)([]);d.useEffect(()=>{console.log("userId: ".concat(L,", userRole: ").concat(R,", accessToken: ").concat(F)),(async()=>{try{if(!F)return;let e=await (0,v.Br)(F,L,R||"",!1,null,null,!0);V(e);let s=(await (0,v.So)(F,L,R||"")).data.map(e=>e.id);$(s)}catch(e){console.error("Error fetching user data:",e),D.ZP.error("Failed to fetch user data")}finally{J(!1)}})()},[F,L,R]);let X=async()=>{try{if(!F)return;await (0,v.Eb)(F,[L]),D.ZP.success("User deleted successfully"),q&&q(),M()}catch(e){console.error("Error deleting user:",e),D.ZP.error("Failed to delete user")}},Q=async e=>{try{if(!F||!z)return;await (0,v.pf)(F,e,null),V({...z,user_info:{...z.user_info,user_email:e.user_email,models:e.models,max_budget:e.max_budget,metadata:e.metadata}}),D.ZP.success("User updated successfully"),G(!1)}catch(e){console.error("Error updating user:",e),D.ZP.error("Failed to update user")}};return H?(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsx)(k.Z,{icon:eL.Z,variant:"light",onClick:M,className:"mb-4",children:"Back to Users"}),(0,c.jsx)(A.Z,{children:"Loading user data..."})]}):z?(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(k.Z,{icon:eL.Z,variant:"light",onClick:M,className:"mb-4",children:"Back to Users"}),(0,c.jsx)(E.Z,{children:(null===(s=z.user_info)||void 0===s?void 0:s.user_email)||"User"}),(0,c.jsx)(A.Z,{className:"text-gray-500 font-mono",children:z.user_id})]}),R&&eg.LQ.includes(R)&&(0,c.jsx)(k.Z,{icon:eM.Z,variant:"secondary",onClick:()=>B(!0),className:"flex items-center",children:"Delete User"})]}),K&&(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete User"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user?"})})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:X,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:()=>B(!1),children:"Cancel"})]})]})]})}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{className:"mb-4",children:[(0,c.jsx)(eT.Z,{children:"Overview"}),(0,c.jsx)(eT.Z,{children:"Details"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:1,numItemsSm:2,numItemsLg:3,className:"gap-6",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Spend"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(E.Z,{children:["$",Number((null===(l=z.user_info)||void 0===l?void 0:l.spend)||0).toFixed(4)]}),(0,c.jsxs)(A.Z,{children:["of ",(null===(t=z.user_info)||void 0===t?void 0:t.max_budget)!==null?"$".concat(z.user_info.max_budget):"Unlimited"]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Teams"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsxs)(A.Z,{children:[(null===(a=z.teams)||void 0===a?void 0:a.length)||0," teams"]})})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"API Keys"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsxs)(A.Z,{children:[(null===(r=z.keys)||void 0===r?void 0:r.length)||0," keys"]})})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Personal Models"}),(0,c.jsx)("div",{className:"mt-2",children:(null===(i=z.user_info)||void 0===i?void 0:null===(n=i.models)||void 0===n?void 0:n.length)&&(null===(m=z.user_info)||void 0===m?void 0:null===(o=m.models)||void 0===o?void 0:o.length)>0?null===(x=z.user_info)||void 0===x?void 0:null===(u=x.models)||void 0===u?void 0:u.map((e,s)=>(0,c.jsx)(A.Z,{children:e},s)):(0,c.jsx)(A.Z,{children:"All proxy models"})})]})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,c.jsx)(E.Z,{children:"User Settings"}),!W&&R&&eg.LQ.includes(R)&&(0,c.jsx)(k.Z,{variant:"light",onClick:()=>G(!0),children:"Edit Settings"})]}),W&&z?(0,c.jsx)(lu,{userData:z,onCancel:()=>G(!1),onSubmit:Q,teams:z.teams,accessToken:F,userID:L,userRole:R,userModels:Y,possibleUIRoles:U}):(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"User ID"}),(0,c.jsx)(A.Z,{className:"font-mono",children:z.user_id})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Email"}),(0,c.jsx)(A.Z,{children:(null===(h=z.user_info)||void 0===h?void 0:h.user_email)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Role"}),(0,c.jsx)(A.Z,{children:(null===(p=z.user_info)||void 0===p?void 0:p.user_role)||"Not Set"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Created"}),(0,c.jsx)(A.Z,{children:(null===(g=z.user_info)||void 0===g?void 0:g.created_at)?new Date(z.user_info.created_at).toLocaleString():"Unknown"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Last Updated"}),(0,c.jsx)(A.Z,{children:(null===(j=z.user_info)||void 0===j?void 0:j.updated_at)?new Date(z.user_info.updated_at).toLocaleString():"Unknown"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Teams"}),(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:(null===(f=z.teams)||void 0===f?void 0:f.length)&&(null===(_=z.teams)||void 0===_?void 0:_.length)>0?null===(y=z.teams)||void 0===y?void 0:y.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:e.team_alias||e.team_id},s)):(0,c.jsx)(A.Z,{children:"No teams"})})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Models"}),(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:(null===(Z=z.user_info)||void 0===Z?void 0:null===(b=Z.models)||void 0===b?void 0:b.length)&&(null===(S=z.user_info)||void 0===S?void 0:null===(N=S.models)||void 0===N?void 0:N.length)>0?null===(I=z.user_info)||void 0===I?void 0:null===(C=I.models)||void 0===C?void 0:C.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:e},s)):(0,c.jsx)(A.Z,{children:"All proxy models"})})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"API Keys"}),(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:(null===(T=z.keys)||void 0===T?void 0:T.length)&&(null===(P=z.keys)||void 0===P?void 0:P.length)>0?z.keys.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-green-100 rounded text-xs",children:e.key_alias||e.token},s)):(0,c.jsx)(A.Z,{children:"No API keys"})})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Metadata"}),(0,c.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto mt-1",children:JSON.stringify((null===(O=z.user_info)||void 0===O?void 0:O.metadata)||{},null,2)})]})]})]})})]})]})]}):(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsx)(k.Z,{icon:eL.Z,variant:"light",onClick:M,className:"mb-4",children:"Back to Users"}),(0,c.jsx)(A.Z,{children:"User not found"})]})}function lh(e){let{data:s=[],columns:l,isLoading:t=!1,onSortChange:a,currentSort:r,accessToken:n,userRole:i,possibleUIRoles:o}=e,[m,u]=d.useState([{id:(null==r?void 0:r.sortBy)||"created_at",desc:(null==r?void 0:r.sortOrder)==="desc"}]),[x,h]=d.useState(null),p=(0,eG.b7)({data:s,columns:l,state:{sorting:m},onSortingChange:e=>{if(u(e),e.length>0){let s=e[0],l=s.id,t=s.desc?"desc":"asc";null==a||a(l,t)}},getCoreRowModel:(0,eY.sC)(),getSortedRowModel:(0,eY.tj)(),enableSorting:!0}),g=e=>{h(e)};return(d.useEffect(()=>{r&&u([{id:r.sortBy,desc:"desc"===r.sortOrder}])},[r]),x)?(0,c.jsx)(lx,{userId:x,onClose:()=>{h(null)},accessToken:n,userRole:i,possibleUIRoles:o}):(0,c.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,c.jsx)("div",{className:"overflow-x-auto",children:(0,c.jsxs)(e$.Z,{className:"[&_td]:py-0.5 [&_th]:py-1",children:[(0,c.jsx)(e0.Z,{children:p.getHeaderGroups().map(e=>(0,c.jsx)(e2.Z,{children:e.headers.map(e=>(0,c.jsx)(e1.Z,{className:"py-1 h-8 ".concat("actions"===e.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),onClick:e.column.getToggleSortingHandler(),children:(0,c.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,c.jsx)("div",{className:"flex items-center",children:e.isPlaceholder?null:(0,eG.ie)(e.column.columnDef.header,e.getContext())}),"actions"!==e.id&&(0,c.jsx)("div",{className:"w-4",children:e.column.getIsSorted()?({asc:(0,c.jsx)(e4.Z,{className:"h-4 w-4 text-blue-500"}),desc:(0,c.jsx)(e5.Z,{className:"h-4 w-4 text-blue-500"})})[e.column.getIsSorted()]:(0,c.jsx)(e3.Z,{className:"h-4 w-4 text-gray-400"})})]})},e.id))},e.id))}),(0,c.jsx)(eX.Z,{children:t?(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:l.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"\uD83D\uDE85 Loading users..."})})})}):s.length>0?p.getRowModel().rows.map(e=>(0,c.jsx)(e2.Z,{className:"h-8",children:e.getVisibleCells().map(e=>(0,c.jsx)(eQ.Z,{className:"py-0.5 max-h-8 overflow-hidden text-ellipsis whitespace-nowrap ".concat("actions"===e.column.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),onClick:()=>{"user_id"===e.column.id&&g(e.getValue())},style:{cursor:"user_id"===e.column.id?"pointer":"default",color:"user_id"===e.column.id?"#3b82f6":"inherit"},children:(0,eG.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:l.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"No users found"})})})})})]})})})}var lp=l(67982),lg=l(87908),lj=e=>{var s;let{accessToken:l,possibleUIRoles:t,userID:a,userRole:r}=e,[n,i]=(0,d.useState)(!0),[o,m]=(0,d.useState)(null),[u,x]=(0,d.useState)(!1),[h,p]=(0,d.useState)({}),[g,j]=(0,d.useState)(!1),[f,_]=(0,d.useState)([]),{Paragraph:y}=es.default,{Option:b}=O.default;(0,d.useEffect)(()=>{(async()=>{if(!l){i(!1);return}try{let e=await (0,v.NL)(l);if(m(e),p(e.values||{}),l)try{let e=await (0,v.So)(l,a,r);if(e&&e.data){let s=e.data.map(e=>e.id);_(s)}}catch(e){console.error("Error fetching available models:",e)}}catch(e){console.error("Error fetching SSO settings:",e),D.ZP.error("Failed to fetch SSO settings")}finally{i(!1)}})()},[l]);let Z=async()=>{if(l){j(!0);try{let e=await (0,v.nd)(l,h);m({...o,values:e.settings}),x(!1)}catch(e){console.error("Error updating SSO settings:",e),D.ZP.error("Failed to update settings")}finally{j(!1)}}},N=(e,s)=>{p(l=>({...l,[e]:s}))},w=(e,s,l)=>{var a;let r=s.type;return"user_role"===e&&t?(0,c.jsx)(O.default,{style:{width:"100%"},value:h[e]||"",onChange:s=>N(e,s),className:"mt-2",children:Object.entries(t).filter(e=>{let[s]=e;return s.includes("internal_user")}).map(e=>{let[s,{ui_label:l,description:t}]=e;return(0,c.jsx)(b,{value:s,children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("span",{children:l}),(0,c.jsx)("span",{className:"ml-2 text-xs text-gray-500",children:t})]})},s)})}):"budget_duration"===e?(0,c.jsx)(e_,{value:h[e]||null,onChange:s=>N(e,s),className:"mt-2"}):"boolean"===r?(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)(sV.Z,{checked:!!h[e],onChange:s=>N(e,s)})}):"array"===r&&(null===(a=s.items)||void 0===a?void 0:a.enum)?(0,c.jsx)(O.default,{mode:"multiple",style:{width:"100%"},value:h[e]||[],onChange:s=>N(e,s),className:"mt-2",children:s.items.enum.map(e=>(0,c.jsx)(b,{value:e,children:e},e))}):"models"===e?(0,c.jsx)(O.default,{mode:"multiple",style:{width:"100%"},value:h[e]||[],onChange:s=>N(e,s),className:"mt-2",children:f.map(e=>(0,c.jsx)(b,{value:e,children:K(e)},e))}):"string"===r&&s.enum?(0,c.jsx)(O.default,{style:{width:"100%"},value:h[e]||"",onChange:s=>N(e,s),className:"mt-2",children:s.enum.map(e=>(0,c.jsx)(b,{value:e,children:e},e))}):(0,c.jsx)(S.Z,{value:void 0!==h[e]?String(h[e]):"",onChange:s=>N(e,s.target.value),placeholder:s.description||"",className:"mt-2"})},C=(e,s)=>{if(null==s)return(0,c.jsx)("span",{className:"text-gray-400",children:"Not set"});if("user_role"===e&&t&&t[s]){let{ui_label:e,description:l}=t[s];return(0,c.jsxs)("div",{children:[(0,c.jsx)("span",{className:"font-medium",children:e}),l&&(0,c.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:l})]})}return"budget_duration"===e?(0,c.jsx)("span",{children:ef(s)}):"boolean"==typeof s?(0,c.jsx)("span",{children:s?"Enabled":"Disabled"}):"models"===e&&Array.isArray(s)?0===s.length?(0,c.jsx)("span",{className:"text-gray-400",children:"None"}):(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:s.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:K(e)},s))}):"object"==typeof s?Array.isArray(s)?0===s.length?(0,c.jsx)("span",{className:"text-gray-400",children:"None"}):(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:s.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:"object"==typeof e?JSON.stringify(e):String(e)},s))}):(0,c.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto mt-1",children:JSON.stringify(s,null,2)}):(0,c.jsx)("span",{children:String(s)})};return n?(0,c.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,c.jsx)(lg.Z,{size:"large"})}):o?(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)("div",{className:"flex justify-end items-center mb-4",children:!n&&o&&(u?(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(k.Z,{variant:"secondary",onClick:()=>{x(!1),p(o.values||{})},disabled:g,children:"Cancel"}),(0,c.jsx)(k.Z,{onClick:Z,loading:g,children:"Save Changes"})]}):(0,c.jsx)(k.Z,{onClick:()=>x(!0),children:"Edit Settings"}))}),(null==o?void 0:null===(s=o.schema)||void 0===s?void 0:s.description)&&(0,c.jsx)(y,{className:"mb-4",children:o.schema.description}),(0,c.jsx)(lp.Z,{}),(0,c.jsx)("div",{className:"mt-4 space-y-4",children:(()=>{let{values:e,schema:s}=o;return s&&s.properties?Object.entries(s.properties).map(s=>{let[l,t]=s,a=e[l],r=l.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase());return(0,c.jsxs)("div",{className:"mb-6 pb-6 border-b border-gray-200 last:border-0",children:[(0,c.jsx)(A.Z,{className:"font-medium text-lg",children:r}),(0,c.jsx)(y,{className:"text-sm text-gray-500 mt-1",children:t.description||"No description available"}),u?(0,c.jsx)("div",{className:"mt-2",children:w(l,t,a)}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:C(l,a)})]},l)}):(0,c.jsx)(A.Z,{children:"No schema information available"})})()})]}):(0,c.jsx)(eI.Z,{children:(0,c.jsx)(A.Z,{children:"No settings available or you do not have permission to view them."})})},lf=l(19616);console.log=function(){};let l_={email:"",user_id:"",user_role:"",sso_user_id:"",team:"",model:"",min_spend:null,max_spend:null,sort_by:"created_at",sort_order:"desc"};var lv=e=>{var s;let{accessToken:l,token:t,userRole:a,userID:r,teams:n}=e,i=(0,h.NL)(),[o,m]=(0,d.useState)(1),[u,x]=(0,d.useState)(!1),[p,g]=(0,d.useState)(null),[j,f]=(0,d.useState)(!1),[_,y]=(0,d.useState)(null),[b,Z]=(0,d.useState)("users"),[N,w]=(0,d.useState)(l_),[S,C,I]=(0,lf.G)(N,{wait:300}),[T,A]=(0,d.useState)(!1);(0,d.useEffect)(()=>()=>{I.cancel()},[I]);let E=e=>{w(s=>{let l={...s,...e};return C(l),l})},P=async()=>{if(_&&l)try{await (0,v.Eb)(l,[_]),i.setQueriesData({queryKey:["userList"]},e=>{if(void 0===e)return e;let s=e.users.filter(e=>e.user_id!==_);return{...e,users:s}}),D.ZP.success("User deleted successfully")}catch(e){console.error("Error deleting user:",e),D.ZP.error("Failed to delete user")}f(!1),y(null)},O=async()=>{g(null),x(!1)},L=async e=>{if(console.log("inside handleEditSubmit:",e),l&&t&&a&&r){try{let s=await (0,v.pf)(l,e,null);i.setQueriesData({queryKey:["userList"]},e=>{if(void 0===e)return e;let l=e.users.map(e=>e.user_id===s.data.user_id?eW(e,s.data):e);return{...e,users:l}}),D.ZP.success("User ".concat(e.user_id," updated successfully"))}catch(e){console.error("There was an error updating the user",e)}g(null),x(!1)}},M=async e=>{m(e)},F=(0,eV.a)({queryKey:["userList",{debouncedFilter:S,currentPage:o}],queryFn:async()=>{if(!l)throw Error("Access token required");return await (0,v.Of)(l,S.user_id?[S.user_id]:null,o,25,S.email||null,S.user_role||null,S.team||null,S.sso_user_id||null,S.sort_by,S.sort_order)},enabled:!!(l&&t&&a&&r),placeholderData:e=>e}),R=F.data,q=(0,eV.a)({queryKey:["userRoles"],initialData:()=>({}),queryFn:async()=>{if(!l)throw Error("Access token required");return await (0,v.lg)(l)},enabled:!!(l&&t&&a&&r)}).data;if(F.isLoading||!l||!t||!a||!r)return(0,c.jsx)("div",{children:"Loading..."});let U=ld(q,e=>{g(e),x(!0)},e=>{y(e),f(!0)});return(0,c.jsxs)("div",{className:"w-full p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("h1",{className:"text-xl font-semibold",children:"Users"}),(0,c.jsx)("div",{className:"flex space-x-3",children:(0,c.jsx)(ex,{userID:r,accessToken:l,teams:n,possibleUIRoles:q})})]}),(0,c.jsxs)(eA.Z,{defaultIndex:0,onIndexChange:e=>Z(0===e?"users":"settings"),children:[(0,c.jsxs)(eE.Z,{className:"mb-4",children:[(0,c.jsx)(eT.Z,{children:"Users"}),(0,c.jsx)(eT.Z,{children:"Default User Settings"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsx)("div",{className:"border-b px-6 py-4",children:(0,c.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,c.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,c.jsxs)("div",{className:"relative w-64",children:[(0,c.jsx)("input",{type:"text",placeholder:"Search by email...",className:"w-full px-3 py-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:N.email,onChange:e=>E({email:e.target.value})}),(0,c.jsx)("svg",{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),(0,c.jsxs)("button",{className:"px-3 py-2 text-sm border rounded-md hover:bg-gray-50 flex items-center gap-2 ".concat(T?"bg-gray-100":""),onClick:()=>A(!T),children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})}),"Filters",(N.user_id||N.user_role||N.team)&&(0,c.jsx)("span",{className:"w-2 h-2 rounded-full bg-blue-500"})]}),(0,c.jsxs)("button",{className:"px-3 py-2 text-sm border rounded-md hover:bg-gray-50 flex items-center gap-2",onClick:()=>{E(l_)},children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Reset Filters"]})]}),T&&(0,c.jsxs)("div",{className:"flex flex-wrap items-center gap-3 mt-3",children:[(0,c.jsxs)("div",{className:"relative w-64",children:[(0,c.jsx)("input",{type:"text",placeholder:"Filter by User ID",className:"w-full px-3 py-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:N.user_id,onChange:e=>E({user_id:e.target.value})}),(0,c.jsx)("svg",{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),(0,c.jsx)("div",{className:"w-64",children:(0,c.jsx)(eS.Z,{value:N.user_role,onValueChange:e=>E({user_role:e}),placeholder:"Select Role",children:Object.entries(q).map(e=>{let[s,l]=e;return(0,c.jsx)(ee.Z,{value:s,children:l.ui_label},s)})})}),(0,c.jsx)("div",{className:"w-64",children:(0,c.jsx)(eS.Z,{value:N.team,onValueChange:e=>E({team:e}),placeholder:"Select Team",children:null==n?void 0:n.map(e=>(0,c.jsx)(ee.Z,{value:e.team_id,children:e.team_alias||e.team_id},e.team_id))})}),(0,c.jsx)("div",{className:"relative w-64",children:(0,c.jsx)("input",{type:"text",placeholder:"Filter by SSO ID",className:"w-full px-3 py-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:N.sso_user_id,onChange:e=>E({sso_user_id:e.target.value})})})]}),(0,c.jsxs)("div",{className:"flex justify-between items-center",children:[(0,c.jsxs)("span",{className:"text-sm text-gray-700",children:["Showing"," ",R&&R.users&&R.users.length>0?(R.page-1)*R.page_size+1:0," ","-"," ",R&&R.users?Math.min(R.page*R.page_size,R.total):0," ","of ",R?R.total:0," results"]}),(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsx)("button",{onClick:()=>M(o-1),disabled:1===o,className:"px-3 py-1 text-sm border rounded-md ".concat(1===o?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-gray-50"),children:"Previous"}),(0,c.jsx)("button",{onClick:()=>M(o+1),disabled:!R||o>=R.total_pages,className:"px-3 py-1 text-sm border rounded-md ".concat(!R||o>=R.total_pages?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-gray-50"),children:"Next"})]})]})]})}),(0,c.jsx)(lh,{data:(null===(s=F.data)||void 0===s?void 0:s.users)||[],columns:U,isLoading:F.isLoading,accessToken:l,userRole:a,onSortChange:(e,s)=>{E({sort_by:e,sort_order:s})},currentSort:{sortBy:N.sort_by,sortOrder:N.sort_order},possibleUIRoles:q})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(lj,{accessToken:l,possibleUIRoles:q,userID:r,userRole:a})})]})]}),(0,c.jsx)(lo,{visible:u,possibleUIRoles:q,onCancel:O,user:p,onSubmit:L}),j&&(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete User"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user?"}),(0,c.jsxs)("p",{className:"text-sm font-medium text-gray-900 mt-2",children:["User ID: ",_]})]})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:P,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:()=>{f(!1),y(null)},children:"Cancel"})]})]})]})})]})},ly=e=>{var s;let{accessToken:l,userID:t,userRole:a}=e,[r,n]=(0,d.useState)(!0),[i,o]=(0,d.useState)(null),[m,u]=(0,d.useState)(!1),[x,h]=(0,d.useState)({}),[p,g]=(0,d.useState)(!1),[j,f]=(0,d.useState)([]),{Paragraph:_}=es.default,{Option:y}=O.default;(0,d.useEffect)(()=>{(async()=>{if(!l){n(!1);return}try{let e=await (0,v.EB)(l);if(o(e),h(e.values||{}),l)try{let e=await (0,v.So)(l,t,a);if(e&&e.data){let s=e.data.map(e=>e.id);f(s)}}catch(e){console.error("Error fetching available models:",e)}}catch(e){console.error("Error fetching team SSO settings:",e),D.ZP.error("Failed to fetch team settings")}finally{n(!1)}})()},[l]);let b=async()=>{if(l){g(!0);try{let e=await (0,v.r1)(l,x);o({...i,values:e.settings}),u(!1),D.ZP.success("Default team settings updated successfully")}catch(e){console.error("Error updating team settings:",e),D.ZP.error("Failed to update team settings")}finally{g(!1)}}},Z=(e,s)=>{h(l=>({...l,[e]:s}))},N=(e,s,l)=>{var t;let a=s.type;return"budget_duration"===e?(0,c.jsx)(e_,{value:x[e]||null,onChange:s=>Z(e,s),className:"mt-2"}):"boolean"===a?(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)(sV.Z,{checked:!!x[e],onChange:s=>Z(e,s)})}):"array"===a&&(null===(t=s.items)||void 0===t?void 0:t.enum)?(0,c.jsx)(O.default,{mode:"multiple",style:{width:"100%"},value:x[e]||[],onChange:s=>Z(e,s),className:"mt-2",children:s.items.enum.map(e=>(0,c.jsx)(y,{value:e,children:e},e))}):"models"===e?(0,c.jsx)(O.default,{mode:"multiple",style:{width:"100%"},value:x[e]||[],onChange:s=>Z(e,s),className:"mt-2",children:j.map(e=>(0,c.jsx)(y,{value:e,children:K(e)},e))}):"string"===a&&s.enum?(0,c.jsx)(O.default,{style:{width:"100%"},value:x[e]||"",onChange:s=>Z(e,s),className:"mt-2",children:s.enum.map(e=>(0,c.jsx)(y,{value:e,children:e},e))}):(0,c.jsx)(S.Z,{value:void 0!==x[e]?String(x[e]):"",onChange:s=>Z(e,s.target.value),placeholder:s.description||"",className:"mt-2"})},w=(e,s)=>null==s?(0,c.jsx)("span",{className:"text-gray-400",children:"Not set"}):"budget_duration"===e?(0,c.jsx)("span",{children:ef(s)}):"boolean"==typeof s?(0,c.jsx)("span",{children:s?"Enabled":"Disabled"}):"models"===e&&Array.isArray(s)?0===s.length?(0,c.jsx)("span",{className:"text-gray-400",children:"None"}):(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:s.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:K(e)},s))}):"object"==typeof s?Array.isArray(s)?0===s.length?(0,c.jsx)("span",{className:"text-gray-400",children:"None"}):(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:s.map((e,s)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-blue-100 rounded text-xs",children:"object"==typeof e?JSON.stringify(e):String(e)},s))}):(0,c.jsx)("pre",{className:"bg-gray-100 p-2 rounded text-xs overflow-auto mt-1",children:JSON.stringify(s,null,2)}):(0,c.jsx)("span",{children:String(s)});return r?(0,c.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,c.jsx)(lg.Z,{size:"large"})}):i?(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,c.jsx)(E.Z,{className:"text-xl",children:"Default Team Settings"}),!r&&i&&(m?(0,c.jsxs)("div",{className:"flex gap-2",children:[(0,c.jsx)(k.Z,{variant:"secondary",onClick:()=>{u(!1),h(i.values||{})},disabled:p,children:"Cancel"}),(0,c.jsx)(k.Z,{onClick:b,loading:p,children:"Save Changes"})]}):(0,c.jsx)(k.Z,{onClick:()=>u(!0),children:"Edit Settings"}))]}),(0,c.jsx)(A.Z,{children:"These settings will be applied by default when creating new teams."}),(null==i?void 0:null===(s=i.schema)||void 0===s?void 0:s.description)&&(0,c.jsx)(_,{className:"mb-4 mt-2",children:i.schema.description}),(0,c.jsx)(lp.Z,{}),(0,c.jsx)("div",{className:"mt-4 space-y-4",children:(()=>{let{values:e,schema:s}=i;return s&&s.properties?Object.entries(s.properties).map(s=>{let[l,t]=s,a=e[l],r=l.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase());return(0,c.jsxs)("div",{className:"mb-6 pb-6 border-b border-gray-200 last:border-0",children:[(0,c.jsx)(A.Z,{className:"font-medium text-lg",children:r}),(0,c.jsx)(_,{className:"text-sm text-gray-500 mt-1",children:t.description||"No description available"}),m?(0,c.jsx)("div",{className:"mt-2",children:N(l,t,a)}):(0,c.jsx)("div",{className:"mt-1 p-2 bg-gray-50 rounded",children:w(l,a)})]},l)}):(0,c.jsx)(A.Z,{children:"No schema information available"})})()})]}):(0,c.jsx)(eI.Z,{children:(0,c.jsx)(A.Z,{children:"No team settings available or you do not have permission to view them."})})},lb=e=>{let{accessToken:s,userID:l}=e,[t,a]=(0,d.useState)([]);(0,d.useEffect)(()=>{(async()=>{if(s&&l)try{let e=await (0,v.a6)(s);a(e)}catch(e){console.error("Error fetching available teams:",e)}})()},[s,l]);let r=async e=>{if(s&&l)try{await (0,v.cu)(s,e,{user_id:l,role:"user"}),D.ZP.success("Successfully joined team"),a(s=>s.filter(s=>s.team_id!==e))}catch(e){console.error("Error joining team:",e),D.ZP.error("Failed to join team")}};return(0,c.jsx)(eI.Z,{className:"w-full mx-auto flex-auto overflow-y-auto max-h-[50vh]",children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Team Name"}),(0,c.jsx)(e1.Z,{children:"Description"}),(0,c.jsx)(e1.Z,{children:"Members"}),(0,c.jsx)(e1.Z,{children:"Models"}),(0,c.jsx)(e1.Z,{children:"Actions"})]})}),(0,c.jsxs)(eX.Z,{children:[t.map(e=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{children:e.team_alias})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{children:e.description||"No description available"})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsxs)(A.Z,{children:[e.members_with_roles.length," members"]})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)("div",{className:"flex flex-col",children:e.models&&0!==e.models.length?e.models.map((e,s)=>(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"blue",children:(0,c.jsx)(A.Z,{children:e.length>30?"".concat(e.slice(0,30),"..."):e})},s)):(0,c.jsx)(eC.Z,{size:"xs",color:"red",children:(0,c.jsx)(A.Z,{children:"All Proxy Models"})})})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(k.Z,{size:"xs",variant:"secondary",onClick:()=>r(e.team_id),children:"Join Team"})})]},e.team_id)),0===t.length&&(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:5,className:"text-center",children:(0,c.jsx)(A.Z,{children:"No available teams to join"})})})]})]})})};console.log=function(){};let lZ=(e,s)=>{let l=[];return e&&e.models.length>0?(console.log("organization.models: ".concat(e.models)),l=e.models):l=s,B(l,s)};var lN=e=>{let{teams:s,searchParams:l,accessToken:t,setTeams:a,userID:r,userRole:n,organizations:i}=e,[o,m]=(0,d.useState)(""),[u,x]=(0,d.useState)(null),[h,p]=(0,d.useState)(null),[g,j]=(0,d.useState)(!1),[f,_]=(0,d.useState)({team_id:"",team_alias:"",organization_id:"",sort_by:"created_at",sort_order:"desc"});(0,d.useEffect)(()=>{console.log("inside useeffect - ".concat(o)),t&&Z(t,r,n,u,a),ew()},[o]);let[y]=L.Z.useForm(),[b]=L.Z.useForm(),{Title:E,Paragraph:P}=es.default,[F,U]=(0,d.useState)(""),[B,H]=(0,d.useState)(!1),[G,Y]=(0,d.useState)(null),[$,X]=(0,d.useState)(null),[Q,el]=(0,d.useState)(!1),[et,ea]=(0,d.useState)(!1),[er,en]=(0,d.useState)(!1),[ei,eo]=(0,d.useState)(!1),[ec,ed]=(0,d.useState)([]),[em,eu]=(0,d.useState)(!1),[ex,eh]=(0,d.useState)(null),[ep,ej]=(0,d.useState)([]),[ef,e_]=(0,d.useState)({}),[ev,ey]=(0,d.useState)([]);(0,d.useEffect)(()=>{console.log("currentOrgForCreateTeam: ".concat(h));let e=lZ(h,ec);console.log("models: ".concat(e)),ej(e),y.setFieldValue("models",[])},[h,ec]),(0,d.useEffect)(()=>{(async()=>{try{if(null==t)return;let e=(await (0,v.t3)(t)).guardrails.map(e=>e.guardrail_name);ey(e)}catch(e){console.error("Failed to fetch guardrails:",e)}})()},[t]);let eb=async e=>{eh(e),eu(!0)},eZ=async()=>{if(null!=ex&&null!=s&&null!=t){try{await (0,v.rs)(t,ex),Z(t,r,n,u,a)}catch(e){console.error("Error deleting the team:",e)}eu(!1),eh(null)}};(0,d.useEffect)(()=>{(async()=>{try{if(null===r||null===n||null===t)return;let e=await V(r,n,t);e&&ed(e)}catch(e){console.error("Error fetching user models:",e)}})()},[t,r,n,s]);let eN=async e=>{try{if(console.log("formValues: ".concat(JSON.stringify(e))),null!=t){var l;let r=null==e?void 0:e.team_alias,n=null!==(l=null==s?void 0:s.map(e=>e.team_alias))&&void 0!==l?l:[],i=(null==e?void 0:e.organization_id)||(null==u?void 0:u.organization_id);if(""===i||"string"!=typeof i?e.organization_id=null:e.organization_id=i.trim(),n.includes(r))throw Error("Team alias ".concat(r," already exists, please pick another alias"));D.ZP.info("Creating Team");let o=await (0,v.hT)(t,e);null!==s?a([...s,o]):a([o]),console.log("response for team create call: ".concat(o)),D.ZP.success("Team created"),y.resetFields(),ea(!1)}}catch(e){console.error("Error creating the team:",e),D.ZP.error("Error creating the team: "+e,20)}},ew=()=>{m(new Date().toLocaleString())},ek=(e,s)=>{let l={...f,[e]:s};_(l),t&&(0,v.JO)(t,l.organization_id||null,null,l.team_id||null,l.team_alias||null).then(e=>{e&&e.teams&&a(e.teams)}).catch(e=>{console.error("Error fetching teams:",e)})};return(0,c.jsx)("div",{className:"w-full mx-4 h-[75vh]",children:$?(0,c.jsx)(sF,{teamId:$,onUpdate:e=>{a(s=>null==s?s:s.map(s=>e.team_id===s.team_id?eW(s,e):s))},onClose:()=>{X(null),el(!1)},accessToken:t,is_team_admin:(e=>{if(null==e||null==e.members_with_roles)return!1;for(let s=0;s<e.members_with_roles.length;s++){let l=e.members_with_roles[s];if(l.user_id==r&&"admin"==l.role)return!0}return!1})(null==s?void 0:s.find(e=>e.team_id===$)),is_proxy_admin:"Admin"==n,userModels:ec,editTeam:Q}):(0,c.jsxs)(eA.Z,{className:"gap-2 p-8 h-[75vh] w-full mt-2",children:[(0,c.jsxs)(eE.Z,{className:"flex justify-between mt-2 w-full items-center",children:[(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)(eT.Z,{children:"Your Teams"}),(0,c.jsx)(eT.Z,{children:"Available Teams"}),(0,eg.tY)(n||"")&&(0,c.jsx)(eT.Z,{children:"Default Team Settings"})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[o&&(0,c.jsxs)(A.Z,{children:["Last Refreshed: ",o]}),(0,c.jsx)(sy.Z,{icon:eD.Z,variant:"shadow",size:"xs",className:"self-center",onClick:ew})]})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)(A.Z,{children:["Click on “Team ID” to view team details ",(0,c.jsx)("b",{children:"and"})," manage team members."]}),(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 pt-2 pb-2 h-[75vh] w-full mt-2",children:[(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(eI.Z,{className:"w-full mx-auto flex-auto overflow-hidden overflow-y-auto max-h-[50vh]",children:[(0,c.jsx)("div",{className:"border-b px-6 py-4",children:(0,c.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,c.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,c.jsxs)("div",{className:"relative w-64",children:[(0,c.jsx)("input",{type:"text",placeholder:"Search by Team Name...",className:"w-full px-3 py-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:f.team_alias,onChange:e=>ek("team_alias",e.target.value)}),(0,c.jsx)("svg",{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),(0,c.jsxs)("button",{className:"px-3 py-2 text-sm border rounded-md hover:bg-gray-50 flex items-center gap-2 ".concat(g?"bg-gray-100":""),onClick:()=>j(!g),children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})}),"Filters",(f.team_id||f.team_alias||f.organization_id)&&(0,c.jsx)("span",{className:"w-2 h-2 rounded-full bg-blue-500"})]}),(0,c.jsxs)("button",{className:"px-3 py-2 text-sm border rounded-md hover:bg-gray-50 flex items-center gap-2",onClick:()=>{_({team_id:"",team_alias:"",organization_id:"",sort_by:"created_at",sort_order:"desc"}),t&&(0,v.JO)(t,null,r||null,null,null).then(e=>{e&&e.teams&&a(e.teams)}).catch(e=>{console.error("Error fetching teams:",e)})},children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Reset Filters"]})]}),g&&(0,c.jsxs)("div",{className:"flex flex-wrap items-center gap-3 mt-3",children:[(0,c.jsxs)("div",{className:"relative w-64",children:[(0,c.jsx)("input",{type:"text",placeholder:"Enter Team ID",className:"w-full px-3 py-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:f.team_id,onChange:e=>ek("team_id",e.target.value)}),(0,c.jsx)("svg",{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),(0,c.jsx)("div",{className:"w-64",children:(0,c.jsx)(eS.Z,{value:f.organization_id||"",onValueChange:e=>ek("organization_id",e),placeholder:"Select Organization",children:null==i?void 0:i.map(e=>(0,c.jsx)(ee.Z,{value:e.organization_id||"",children:e.organization_alias||e.organization_id},e.organization_id))})})]})]})}),(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Team Name"}),(0,c.jsx)(e1.Z,{children:"Team ID"}),(0,c.jsx)(e1.Z,{children:"Created"}),(0,c.jsx)(e1.Z,{children:"Spend (USD)"}),(0,c.jsx)(e1.Z,{children:"Budget (USD)"}),(0,c.jsx)(e1.Z,{children:"Models"}),(0,c.jsx)(e1.Z,{children:"Organization"}),(0,c.jsx)(e1.Z,{children:"Info"})]})}),(0,c.jsx)(eX.Z,{children:s&&s.length>0?s.filter(e=>!u||e.organization_id===u.organization_id).sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()).map(e=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{style:{maxWidth:"4px",whiteSpace:"pre-wrap",overflow:"hidden"},children:e.team_alias}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsx)(W.Z,{title:e.team_id,children:(0,c.jsxs)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left overflow-hidden truncate max-w-[200px]",onClick:()=>{X(e.team_id)},children:[e.team_id.slice(0,7),"..."]})})})}),(0,c.jsx)(eQ.Z,{style:{maxWidth:"4px",whiteSpace:"pre-wrap",overflow:"hidden"},children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,c.jsx)(eQ.Z,{style:{maxWidth:"4px",whiteSpace:"pre-wrap",overflow:"hidden"},children:e.spend}),(0,c.jsx)(eQ.Z,{style:{maxWidth:"4px",whiteSpace:"pre-wrap",overflow:"hidden"},children:null!==e.max_budget&&void 0!==e.max_budget?e.max_budget:"No limit"}),(0,c.jsx)(eQ.Z,{style:{maxWidth:"8-x",whiteSpace:"pre-wrap",overflow:"hidden"},children:Array.isArray(e.models)?(0,c.jsx)("div",{style:{display:"flex",flexDirection:"column"},children:0===e.models.length?(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"red",children:(0,c.jsx)(A.Z,{children:"All Proxy Models"})}):e.models.map((e,s)=>"all-proxy-models"===e?(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"red",children:(0,c.jsx)(A.Z,{children:"All Proxy Models"})},s):(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"blue",children:(0,c.jsx)(A.Z,{children:e.length>30?"".concat(K(e).slice(0,30),"..."):K(e)})},s))}):null}),(0,c.jsx)(eQ.Z,{children:e.organization_id}),(0,c.jsxs)(eQ.Z,{children:[(0,c.jsxs)(A.Z,{children:[ef&&e.team_id&&ef[e.team_id]&&ef[e.team_id].keys&&ef[e.team_id].keys.length," ","Keys"]}),(0,c.jsxs)(A.Z,{children:[ef&&e.team_id&&ef[e.team_id]&&ef[e.team_id].members_with_roles&&ef[e.team_id].members_with_roles.length," ","Members"]})]}),(0,c.jsx)(eQ.Z,{children:"Admin"==n?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>{X(e.team_id),el(!0)}}),(0,c.jsx)(sy.Z,{onClick:()=>eb(e.team_id),icon:eM.Z,size:"sm"})]}):null})]},e.team_id)):null})]}),em&&(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Team"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this team ?"})})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:eZ,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:()=>{eu(!1),eh(null)},children:"Cancel"})]})]})]})})]})}),"Admin"==n||"Org Admin"==n?(0,c.jsxs)(N.Z,{numColSpan:1,children:[(0,c.jsx)(k.Z,{className:"mx-auto",onClick:()=>ea(!0),children:"+ Create New Team"}),(0,c.jsx)(M.Z,{title:"Create Team",visible:et,width:800,footer:null,onOk:()=>{ea(!1),y.resetFields()},onCancel:()=>{ea(!1),y.resetFields()},children:(0,c.jsxs)(L.Z,{form:y,onFinish:eN,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"Team Name",name:"team_alias",rules:[{required:!0,message:"Please input a team name"}],children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Organization"," ",(0,c.jsx)(W.Z,{title:(0,c.jsxs)("span",{children:["Organizations can have multiple teams. Learn more about"," ",(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/user_management_heirarchy",target:"_blank",rel:"noopener noreferrer",style:{color:"#1890ff",textDecoration:"underline"},onClick:e=>e.stopPropagation(),children:"user management hierarchy"})]}),children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"organization_id",initialValue:u?u.organization_id:null,className:"mt-8",children:(0,c.jsx)(O.default,{showSearch:!0,allowClear:!0,placeholder:"Search or select an Organization",onChange:e=>{y.setFieldValue("organization_id",e),p((null==i?void 0:i.find(s=>s.organization_id===e))||null)},filterOption:(e,s)=>{var l;return!!s&&((null===(l=s.children)||void 0===l?void 0:l.toString())||"").toLowerCase().includes(e.toLowerCase())},optionFilterProp:"children",children:null==i?void 0:i.map(e=>(0,c.jsxs)(O.default.Option,{value:e.organization_id,children:[(0,c.jsx)("span",{className:"font-medium",children:e.organization_alias})," ",(0,c.jsxs)("span",{className:"text-gray-500",children:["(",e.organization_id,")"]})]},e.organization_id))})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Models"," ",(0,c.jsx)(W.Z,{title:"These are the models that your selected team has access to",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"models",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",style:{width:"100%"},children:[(0,c.jsx)(O.default.Option,{value:"all-proxy-models",children:"All Proxy Models"},"all-proxy-models"),ep.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:K(e)},e))]})}),(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(z,{step:.01,precision:2,width:200})}),(0,c.jsx)(L.Z.Item,{className:"mt-8",label:"Reset Budget",name:"budget_duration",children:(0,c.jsxs)(O.default,{defaultValue:null,placeholder:"n/a",children:[(0,c.jsx)(O.default.Option,{value:"24h",children:"daily"}),(0,c.jsx)(O.default.Option,{value:"7d",children:"weekly"}),(0,c.jsx)(O.default.Option,{value:"30d",children:"monthly"})]})}),(0,c.jsx)(L.Z.Item,{label:"Tokens per minute Limit (TPM)",name:"tpm_limit",children:(0,c.jsx)(z,{step:1,width:400})}),(0,c.jsx)(L.Z.Item,{label:"Requests per minute Limit (RPM)",name:"rpm_limit",children:(0,c.jsx)(z,{step:1,width:400})}),(0,c.jsxs)(C.Z,{className:"mt-20 mb-8",children:[(0,c.jsx)(T.Z,{children:(0,c.jsx)("b",{children:"Additional Settings"})}),(0,c.jsxs)(I.Z,{children:[(0,c.jsx)(L.Z.Item,{label:"Team ID",name:"team_id",help:"ID of the team you want to create. If not provided, it will be generated automatically.",children:(0,c.jsx)(S.Z,{onChange:e=>{e.target.value=e.target.value.trim()}})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",help:"Additional team metadata. Enter metadata as JSON object.",children:(0,c.jsx)(q.default.TextArea,{rows:4})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Guardrails"," ",(0,c.jsx)(W.Z,{title:"Setup your first guardrail",children:(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/guardrails/quick_start",target:"_blank",rel:"noopener noreferrer",onClick:e=>e.stopPropagation(),children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})})]}),name:"guardrails",className:"mt-8",help:"Select existing guardrails or enter new ones",children:(0,c.jsx)(O.default,{mode:"tags",style:{width:"100%"},placeholder:"Select or enter guardrails",options:ev.map(e=>({value:e,label:e}))})})]})]})]}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Create Team"})})]})})]}):null]})]}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(lb,{accessToken:t,userID:r})}),(0,eg.tY)(n||"")&&(0,c.jsx)(eP.Z,{children:(0,c.jsx)(ly,{accessToken:t,userID:r||"",userRole:n||""})})]})]})})},lw=e=>{var s,l;let{organizationId:t,onClose:a,accessToken:r,is_org_admin:n,is_proxy_admin:i,userModels:o,editOrg:m}=e,[u,x]=(0,d.useState)(null),[h,p]=(0,d.useState)(!0),[g]=L.Z.useForm(),[j,f]=(0,d.useState)(!1),[_,y]=(0,d.useState)(!1),[b,Z]=(0,d.useState)(!1),[N,C]=(0,d.useState)(null),I=n||i,T=async()=>{try{if(p(!0),!r)return;let e=await (0,v.t$)(r,t);x(e)}catch(e){D.ZP.error("Failed to load organization information"),console.error("Error fetching organization info:",e)}finally{p(!1)}};(0,d.useEffect)(()=>{T()},[t,r]);let P=async e=>{try{if(null==r)return;let s={user_email:e.user_email,user_id:e.user_id,role:e.role};await (0,v.vh)(r,t,s),D.ZP.success("Organization member added successfully"),y(!1),g.resetFields(),T()}catch(e){D.ZP.error("Failed to add organization member"),console.error("Error adding organization member:",e)}},M=async e=>{try{if(!r)return;let s={user_email:e.user_email,user_id:e.user_id,role:e.role};await (0,v.LY)(r,t,s),D.ZP.success("Organization member updated successfully"),Z(!1),g.resetFields(),T()}catch(e){D.ZP.error("Failed to update organization member"),console.error("Error updating organization member:",e)}},F=async e=>{try{if(!r)return;await (0,v.Sb)(r,t,e.user_id),D.ZP.success("Organization member deleted successfully"),Z(!1),g.resetFields(),T()}catch(e){D.ZP.error("Failed to delete organization member"),console.error("Error deleting organization member:",e)}},U=async e=>{try{if(!r)return;let s={organization_id:t,organization_alias:e.organization_alias,models:e.models,litellm_budget_table:{tpm_limit:e.tpm_limit,rpm_limit:e.rpm_limit,max_budget:e.max_budget,budget_duration:e.budget_duration},metadata:e.metadata?JSON.parse(e.metadata):null};await (0,v.VA)(r,s),D.ZP.success("Organization settings updated successfully"),f(!1),T()}catch(e){D.ZP.error("Failed to update organization settings"),console.error("Error updating organization:",e)}};return h?(0,c.jsx)("div",{className:"p-4",children:"Loading..."}):u?(0,c.jsxs)("div",{className:"w-full h-screen p-4 bg-white",children:[(0,c.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,c.jsxs)("div",{children:[(0,c.jsx)(R.ZP,{onClick:a,className:"mb-4",children:"← Back"}),(0,c.jsx)(E.Z,{children:u.organization_alias}),(0,c.jsx)(A.Z,{className:"text-gray-500 font-mono",children:u.organization_id})]})}),(0,c.jsxs)(eA.Z,{defaultIndex:m?2:0,children:[(0,c.jsxs)(eE.Z,{className:"mb-4",children:[(0,c.jsx)(eT.Z,{children:"Overview"}),(0,c.jsx)(eT.Z,{children:"Members"}),(0,c.jsx)(eT.Z,{children:"Settings"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:1,numItemsSm:2,numItemsLg:3,className:"gap-6",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Organization Details"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(A.Z,{children:["Created: ",new Date(u.created_at).toLocaleDateString()]}),(0,c.jsxs)(A.Z,{children:["Updated: ",new Date(u.updated_at).toLocaleDateString()]}),(0,c.jsxs)(A.Z,{children:["Created By: ",u.created_by]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Budget Status"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(E.Z,{children:["$",u.spend.toFixed(6)]}),(0,c.jsxs)(A.Z,{children:["of ",null===u.litellm_budget_table.max_budget?"Unlimited":"$".concat(u.litellm_budget_table.max_budget)]}),u.litellm_budget_table.budget_duration&&(0,c.jsxs)(A.Z,{className:"text-gray-500",children:["Reset: ",u.litellm_budget_table.budget_duration]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Rate Limits"}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsxs)(A.Z,{children:["TPM: ",u.litellm_budget_table.tpm_limit||"Unlimited"]}),(0,c.jsxs)(A.Z,{children:["RPM: ",u.litellm_budget_table.rpm_limit||"Unlimited"]}),u.litellm_budget_table.max_parallel_requests&&(0,c.jsxs)(A.Z,{children:["Max Parallel Requests: ",u.litellm_budget_table.max_parallel_requests]})]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Models"}),(0,c.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:u.models.map((e,s)=>(0,c.jsx)(eC.Z,{color:"red",children:e},s))})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Teams"}),(0,c.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:null===(s=u.teams)||void 0===s?void 0:s.map((e,s)=>(0,c.jsx)(eC.Z,{color:"red",children:e.team_id},s))})]})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsx)(eI.Z,{className:"w-full mx-auto flex-auto overflow-y-auto max-h-[75vh]",children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"User ID"}),(0,c.jsx)(e1.Z,{children:"Role"}),(0,c.jsx)(e1.Z,{children:"Spend"}),(0,c.jsx)(e1.Z,{children:"Created At"}),(0,c.jsx)(e1.Z,{})]})}),(0,c.jsx)(eX.Z,{children:null===(l=u.members)||void 0===l?void 0:l.map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{className:"font-mono",children:e.user_id})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{className:"font-mono",children:e.user_role})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsxs)(A.Z,{children:["$",e.spend.toFixed(6)]})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{children:new Date(e.created_at).toLocaleString()})}),(0,c.jsx)(eQ.Z,{children:I&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>{C({role:e.user_role,user_email:e.user_email,user_id:e.user_id}),Z(!0)}}),(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>{F(e)}})]})})]},s))})]})}),I&&(0,c.jsx)(k.Z,{onClick:()=>{y(!0)},children:"Add Member"})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,c.jsx)(E.Z,{children:"Organization Settings"}),I&&!j&&(0,c.jsx)(k.Z,{onClick:()=>f(!0),children:"Edit Settings"})]}),j?(0,c.jsxs)(L.Z,{form:g,onFinish:U,initialValues:{organization_alias:u.organization_alias,models:u.models,tpm_limit:u.litellm_budget_table.tpm_limit,rpm_limit:u.litellm_budget_table.rpm_limit,max_budget:u.litellm_budget_table.max_budget,budget_duration:u.litellm_budget_table.budget_duration,metadata:u.metadata?JSON.stringify(u.metadata,null,2):""},layout:"vertical",children:[(0,c.jsx)(L.Z.Item,{label:"Organization Name",name:"organization_alias",rules:[{required:!0,message:"Please input an organization name"}],children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"Models",name:"models",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",children:[(0,c.jsx)(O.default.Option,{value:"all-proxy-models",children:"All Proxy Models"},"all-proxy-models"),o.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:K(e)},e))]})}),(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(z,{step:.01,precision:2,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{label:"Reset Budget",name:"budget_duration",children:(0,c.jsxs)(O.default,{placeholder:"n/a",children:[(0,c.jsx)(O.default.Option,{value:"24h",children:"daily"}),(0,c.jsx)(O.default.Option,{value:"7d",children:"weekly"}),(0,c.jsx)(O.default.Option,{value:"30d",children:"monthly"})]})}),(0,c.jsx)(L.Z.Item,{label:"Tokens per minute Limit (TPM)",name:"tpm_limit",children:(0,c.jsx)(z,{step:1,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{label:"Requests per minute Limit (RPM)",name:"rpm_limit",children:(0,c.jsx)(z,{step:1,style:{width:"100%"}})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",children:(0,c.jsx)(q.default.TextArea,{rows:4})}),(0,c.jsxs)("div",{className:"flex justify-end gap-2 mt-6",children:[(0,c.jsx)(R.ZP,{onClick:()=>f(!1),children:"Cancel"}),(0,c.jsx)(k.Z,{type:"submit",children:"Save Changes"})]})]}):(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Organization Name"}),(0,c.jsx)("div",{children:u.organization_alias})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Organization ID"}),(0,c.jsx)("div",{className:"font-mono",children:u.organization_id})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Created At"}),(0,c.jsx)("div",{children:new Date(u.created_at).toLocaleString()})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Models"}),(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:u.models.map((e,s)=>(0,c.jsx)(eC.Z,{color:"red",children:e},s))})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Rate Limits"}),(0,c.jsxs)("div",{children:["TPM: ",u.litellm_budget_table.tpm_limit||"Unlimited"]}),(0,c.jsxs)("div",{children:["RPM: ",u.litellm_budget_table.rpm_limit||"Unlimited"]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Budget"}),(0,c.jsxs)("div",{children:["Max: ",null!==u.litellm_budget_table.max_budget?"$".concat(u.litellm_budget_table.max_budget):"No Limit"]}),(0,c.jsxs)("div",{children:["Reset: ",u.litellm_budget_table.budget_duration||"Never"]})]})]})]})})]})]}),(0,c.jsx)(sM,{isVisible:_,onCancel:()=>y(!1),onSubmit:P,accessToken:r,title:"Add Organization Member",roles:[{label:"org_admin",value:"org_admin",description:"Can add and remove members, and change their roles."},{label:"internal_user",value:"internal_user",description:"Can view/create keys for themselves within organization."},{label:"internal_user_viewer",value:"internal_user_viewer",description:"Can only view their keys within organization."}],defaultRole:"internal_user"}),(0,c.jsx)(sD,{visible:b,onCancel:()=>Z(!1),onSubmit:M,initialData:N,mode:"edit",config:{title:"Edit Member",showEmail:!0,showUserId:!0,roleOptions:[{label:"Org Admin",value:"org_admin"},{label:"Internal User",value:"internal_user"},{label:"Internal User Viewer",value:"internal_user_viewer"}]}})]}):(0,c.jsx)("div",{className:"p-4",children:"Organization not found"})};let lk=async(e,s)=>{s(await (0,v.r6)(e))};var lS=e=>{let{organizations:s,userRole:l,userModels:t,accessToken:a,lastRefreshed:r,handleRefreshClick:n,currentOrg:i,guardrailsList:o=[],setOrganizations:m,premiumUser:u}=e,[x,h]=(0,d.useState)(null),[p,g]=(0,d.useState)(!1),[j,f]=(0,d.useState)(!1),[_,y]=(0,d.useState)(null),[b,Z]=(0,d.useState)(!1),[C]=L.Z.useForm();(0,d.useEffect)(()=>{0===s.length&&a&&lk(a,m)},[s,a]);let I=e=>{e&&(y(e),f(!0))},T=async()=>{if(_&&a)try{await (0,v.cq)(a,_),D.ZP.success("Organization deleted successfully"),f(!1),y(null),lk(a,m)}catch(e){console.error("Error deleting organization:",e)}},E=async e=>{try{if(!a)return;console.log("values in organizations new create call: ".concat(JSON.stringify(e))),await (0,v.H1)(a,e),Z(!1),C.resetFields(),lk(a,m)}catch(e){console.error("Error creating organization:",e)}};return u?x?(0,c.jsx)(lw,{organizationId:x,onClose:()=>{h(null),g(!1)},accessToken:a,is_org_admin:!0,is_proxy_admin:"Admin"===l,userModels:t,editOrg:p}):(0,c.jsxs)(eA.Z,{className:"gap-2 p-8 h-[75vh] w-full mt-2",children:[(0,c.jsxs)(eE.Z,{className:"flex justify-between mt-2 w-full items-center",children:[(0,c.jsx)("div",{className:"flex",children:(0,c.jsx)(eT.Z,{children:"Your Organizations"})}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[r&&(0,c.jsxs)(A.Z,{children:["Last Refreshed: ",r]}),(0,c.jsx)(sy.Z,{icon:eD.Z,variant:"shadow",size:"xs",className:"self-center",onClick:n})]})]}),(0,c.jsx)(eO.Z,{children:(0,c.jsxs)(eP.Z,{children:[(0,c.jsx)(A.Z,{children:"Click on “Organization ID” to view organization details."}),(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 pt-2 pb-2 h-[75vh] w-full mt-2",children:[(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(eI.Z,{className:"w-full mx-auto flex-auto overflow-y-auto max-h-[50vh]",children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Organization ID"}),(0,c.jsx)(e1.Z,{children:"Organization Name"}),(0,c.jsx)(e1.Z,{children:"Created"}),(0,c.jsx)(e1.Z,{children:"Spend (USD)"}),(0,c.jsx)(e1.Z,{children:"Budget (USD)"}),(0,c.jsx)(e1.Z,{children:"Models"}),(0,c.jsx)(e1.Z,{children:"TPM / RPM Limits"}),(0,c.jsx)(e1.Z,{children:"Info"}),(0,c.jsx)(e1.Z,{children:"Actions"})]})}),(0,c.jsx)(eX.Z,{children:s&&s.length>0?s.sort((e,s)=>new Date(s.created_at).getTime()-new Date(e.created_at).getTime()).map(e=>{var s,t,a,r,n,i,o,d,m;return(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsx)(W.Z,{title:e.organization_id,children:(0,c.jsxs)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left overflow-hidden truncate max-w-[200px]",onClick:()=>h(e.organization_id),children:[null===(s=e.organization_id)||void 0===s?void 0:s.slice(0,7),"..."]})})})}),(0,c.jsx)(eQ.Z,{children:e.organization_alias}),(0,c.jsx)(eQ.Z,{children:e.created_at?new Date(e.created_at).toLocaleDateString():"N/A"}),(0,c.jsx)(eQ.Z,{children:e.spend}),(0,c.jsx)(eQ.Z,{children:(null===(t=e.litellm_budget_table)||void 0===t?void 0:t.max_budget)!==null&&(null===(a=e.litellm_budget_table)||void 0===a?void 0:a.max_budget)!==void 0?null===(r=e.litellm_budget_table)||void 0===r?void 0:r.max_budget:"No limit"}),(0,c.jsx)(eQ.Z,{children:Array.isArray(e.models)&&(0,c.jsx)("div",{className:"flex flex-col",children:0===e.models.length?(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"red",children:"All Proxy Models"}):e.models.map((e,s)=>"all-proxy-models"===e?(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"red",children:"All Proxy Models"},s):(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"blue",children:e.length>30?"".concat(K(e).slice(0,30),"..."):K(e)},s))})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsxs)(A.Z,{children:["TPM: ",(null===(n=e.litellm_budget_table)||void 0===n?void 0:n.tpm_limit)?null===(i=e.litellm_budget_table)||void 0===i?void 0:i.tpm_limit:"Unlimited",(0,c.jsx)("br",{}),"RPM: ",(null===(o=e.litellm_budget_table)||void 0===o?void 0:o.rpm_limit)?null===(d=e.litellm_budget_table)||void 0===d?void 0:d.rpm_limit:"Unlimited"]})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsxs)(A.Z,{children:[(null===(m=e.members)||void 0===m?void 0:m.length)||0," Members"]})}),(0,c.jsx)(eQ.Z,{children:"Admin"===l&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>{h(e.organization_id),g(!0)}}),(0,c.jsx)(sy.Z,{onClick:()=>I(e.organization_id),icon:eM.Z,size:"sm"})]})})]},e.organization_id)}):null})]})})}),("Admin"===l||"Org Admin"===l)&&(0,c.jsxs)(N.Z,{numColSpan:1,children:[(0,c.jsx)(k.Z,{className:"mx-auto",onClick:()=>Z(!0),children:"+ Create New Organization"}),(0,c.jsx)(M.Z,{title:"Create Organization",visible:b,width:800,footer:null,onCancel:()=>{Z(!1),C.resetFields()},children:(0,c.jsxs)(L.Z,{form:C,onFinish:E,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsx)(L.Z.Item,{label:"Organization Name",name:"organization_alias",rules:[{required:!0,message:"Please input an organization name"}],children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsx)(L.Z.Item,{label:"Models",name:"models",children:(0,c.jsxs)(O.default,{mode:"multiple",placeholder:"Select models",style:{width:"100%"},children:[(0,c.jsx)(O.default.Option,{value:"all-proxy-models",children:"All Proxy Models"},"all-proxy-models"),t&&t.length>0&&t.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:K(e)},e))]})}),(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(z,{step:.01,precision:2,width:200})}),(0,c.jsx)(L.Z.Item,{label:"Reset Budget",name:"budget_duration",children:(0,c.jsxs)(O.default,{defaultValue:null,placeholder:"n/a",children:[(0,c.jsx)(O.default.Option,{value:"24h",children:"daily"}),(0,c.jsx)(O.default.Option,{value:"7d",children:"weekly"}),(0,c.jsx)(O.default.Option,{value:"30d",children:"monthly"})]})}),(0,c.jsx)(L.Z.Item,{label:"Tokens per minute Limit (TPM)",name:"tpm_limit",children:(0,c.jsx)(z,{step:1,width:400})}),(0,c.jsx)(L.Z.Item,{label:"Requests per minute Limit (RPM)",name:"rpm_limit",children:(0,c.jsx)(z,{step:1,width:400})}),(0,c.jsx)(L.Z.Item,{label:"Metadata",name:"metadata",children:(0,c.jsx)(q.default.TextArea,{rows:4})}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(k.Z,{type:"submit",children:"Create Organization"})})]})})]})]})]})}),j?(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Organization"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this organization?"})})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:T,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:()=>{f(!1),y(null)},children:"Cancel"})]})]})]})}):(0,c.jsx)(c.Fragment,{})]}):(0,c.jsx)("div",{children:(0,c.jsxs)(A.Z,{children:["This is a LiteLLM Enterprise feature, and requires a valid key to use. Get a trial key ",(0,c.jsx)("a",{href:"https://litellm.ai/pricing",target:"_blank",rel:"noopener noreferrer",children:"here"}),"."]})})},lC=l(94789);let lI={google:"https://artificialanalysis.ai/img/logos/google_small.svg",microsoft:"https://upload.wikimedia.org/wikipedia/commons/a/a8/Microsoft_Azure_Logo.svg",okta:"https://www.okta.com/sites/default/files/Okta_Logo_BrightBlue_Medium.png",generic:""},lT={google:{envVarMap:{google_client_id:"GOOGLE_CLIENT_ID",google_client_secret:"GOOGLE_CLIENT_SECRET"},fields:[{label:"GOOGLE CLIENT ID",name:"google_client_id"},{label:"GOOGLE CLIENT SECRET",name:"google_client_secret"}]},microsoft:{envVarMap:{microsoft_client_id:"MICROSOFT_CLIENT_ID",microsoft_client_secret:"MICROSOFT_CLIENT_SECRET",microsoft_tenant:"MICROSOFT_TENANT"},fields:[{label:"MICROSOFT CLIENT ID",name:"microsoft_client_id"},{label:"MICROSOFT CLIENT SECRET",name:"microsoft_client_secret"},{label:"MICROSOFT TENANT",name:"microsoft_tenant"}]},okta:{envVarMap:{generic_client_id:"GENERIC_CLIENT_ID",generic_client_secret:"GENERIC_CLIENT_SECRET",generic_authorization_endpoint:"GENERIC_AUTHORIZATION_ENDPOINT",generic_token_endpoint:"GENERIC_TOKEN_ENDPOINT",generic_userinfo_endpoint:"GENERIC_USERINFO_ENDPOINT"},fields:[{label:"GENERIC CLIENT ID",name:"generic_client_id"},{label:"GENERIC CLIENT SECRET",name:"generic_client_secret"},{label:"AUTHORIZATION ENDPOINT",name:"generic_authorization_endpoint",placeholder:"https://your-okta-domain/authorize"},{label:"TOKEN ENDPOINT",name:"generic_token_endpoint",placeholder:"https://your-okta-domain/token"},{label:"USERINFO ENDPOINT",name:"generic_userinfo_endpoint",placeholder:"https://your-okta-domain/userinfo"}]},generic:{envVarMap:{generic_client_id:"GENERIC_CLIENT_ID",generic_client_secret:"GENERIC_CLIENT_SECRET",generic_authorization_endpoint:"GENERIC_AUTHORIZATION_ENDPOINT",generic_token_endpoint:"GENERIC_TOKEN_ENDPOINT",generic_userinfo_endpoint:"GENERIC_USERINFO_ENDPOINT"},fields:[{label:"GENERIC CLIENT ID",name:"generic_client_id"},{label:"GENERIC CLIENT SECRET",name:"generic_client_secret"},{label:"AUTHORIZATION ENDPOINT",name:"generic_authorization_endpoint"},{label:"TOKEN ENDPOINT",name:"generic_token_endpoint"},{label:"USERINFO ENDPOINT",name:"generic_userinfo_endpoint"}]}};var lA=e=>{let{isAddSSOModalVisible:s,isInstructionsModalVisible:l,handleAddSSOOk:t,handleAddSSOCancel:a,handleShowInstructions:r,handleInstructionsOk:n,handleInstructionsCancel:i,form:o}=e,d=e=>{let s=lT[e];return s?s.fields.map(e=>(0,c.jsx)(L.Z.Item,{label:e.label,name:e.name,rules:[{required:!0,message:"Please enter the ".concat(e.label.toLowerCase())}],children:e.name.includes("client")?(0,c.jsx)(q.default.Password,{}):(0,c.jsx)(S.Z,{placeholder:e.placeholder})},e.name)):null};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(M.Z,{title:"Add SSO",visible:s,width:800,footer:null,onOk:t,onCancel:a,children:(0,c.jsxs)(L.Z,{form:o,onFinish:r,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"SSO Provider",name:"sso_provider",rules:[{required:!0,message:"Please select an SSO provider"}],children:(0,c.jsx)(O.default,{children:Object.entries(lI).map(e=>{let[s,l]=e;return(0,c.jsx)(O.default.Option,{value:s,children:(0,c.jsxs)("div",{style:{display:"flex",alignItems:"center",padding:"4px 0"},children:[l&&(0,c.jsx)("img",{src:l,alt:s,style:{height:24,width:24,marginRight:12,objectFit:"contain"}}),(0,c.jsxs)("span",{children:[s.charAt(0).toUpperCase()+s.slice(1)," SSO"]})]})},s)})})}),(0,c.jsx)(L.Z.Item,{noStyle:!0,shouldUpdate:(e,s)=>e.sso_provider!==s.sso_provider,children:e=>{let{getFieldValue:s}=e,l=s("sso_provider");return l?d(l):null}}),(0,c.jsx)(L.Z.Item,{label:"Proxy Admin Email",name:"user_email",rules:[{required:!0,message:"Please enter the email of the proxy admin"}],children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"PROXY BASE URL",name:"proxy_base_url",rules:[{required:!0,message:"Please enter the proxy base url"}],children:(0,c.jsx)(S.Z,{})})]}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Save"})})]})}),(0,c.jsxs)(M.Z,{title:"SSO Setup Instructions",visible:l,width:800,footer:null,onOk:n,onCancel:i,children:[(0,c.jsx)("p",{children:"Follow these steps to complete the SSO setup:"}),(0,c.jsx)(A.Z,{className:"mt-2",children:"1. DO NOT Exit this TAB"}),(0,c.jsx)(A.Z,{className:"mt-2",children:"2. Open a new tab, visit your proxy base url"}),(0,c.jsx)(A.Z,{className:"mt-2",children:"3. Confirm your SSO is configured correctly and you can login on the new Tab"}),(0,c.jsx)(A.Z,{className:"mt-2",children:"4. If Step 3 is successful, you can close this tab"}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{onClick:n,children:"Done"})})]})]})},lE=l(62272),lP=l(92403),lO=l(29271),lL=l(34419),lD=e=>{let{accessToken:s,userID:l,proxySettings:t}=e,[a]=L.Z.useForm(),[r,n]=(0,d.useState)(!1),[i,o]=(0,d.useState)(null),[m,u]=(0,d.useState)("<your_proxy_base_url>");(0,d.useEffect)(()=>{let e="<your_proxy_base_url>";u(t&&t.PROXY_BASE_URL&&void 0!==t.PROXY_BASE_URL?t.PROXY_BASE_URL:window.location.origin)},[t]);let x="".concat(m,"/scim/v2"),h=async e=>{if(!s||!l){D.ZP.error("You need to be logged in to create a SCIM token");return}try{n(!0);let t={key_alias:e.key_alias||"SCIM Access Token",team_id:null,models:[],allowed_routes:["/scim/*"]},a=await (0,v.wX)(s,l,t);o(a),D.ZP.success("SCIM token created successfully")}catch(e){console.error("Error creating SCIM token:",e),D.ZP.error("Failed to create SCIM token")}finally{n(!1)}};return(0,c.jsx)(w.Z,{numItems:1,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)("div",{className:"flex items-center mb-4",children:(0,c.jsx)(E.Z,{children:"SCIM Configuration"})}),(0,c.jsx)(A.Z,{className:"text-gray-600",children:"System for Cross-domain Identity Management (SCIM) allows you to automatically provision and manage users and groups in LiteLLM."}),(0,c.jsx)(lp.Z,{}),(0,c.jsxs)("div",{className:"space-y-8",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex items-center mb-2",children:[(0,c.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-700 mr-2",children:"1"}),(0,c.jsxs)(E.Z,{className:"text-lg flex items-center",children:[(0,c.jsx)(lE.Z,{className:"h-5 w-5 mr-2"}),"SCIM Tenant URL"]})]}),(0,c.jsx)(A.Z,{className:"text-gray-600 mb-3",children:"Use this URL in your identity provider SCIM integration settings."}),(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(S.Z,{value:x,disabled:!0,className:"flex-grow"}),(0,c.jsx)(P.CopyToClipboard,{text:x,onCopy:()=>D.ZP.success("URL copied to clipboard"),children:(0,c.jsxs)(k.Z,{variant:"primary",className:"ml-2 flex items-center",children:[(0,c.jsx)(s3.Z,{className:"h-4 w-4 mr-1"}),"Copy"]})})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex items-center mb-2",children:[(0,c.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-700 mr-2",children:"2"}),(0,c.jsxs)(E.Z,{className:"text-lg flex items-center",children:[(0,c.jsx)(lP.Z,{className:"h-5 w-5 mr-2"}),"Authentication Token"]})]}),(0,c.jsx)(lC.Z,{title:"Using SCIM",color:"blue",className:"mb-4",children:"You need a SCIM token to authenticate with the SCIM API. Create one below and use it in your SCIM provider configuration."}),i?(0,c.jsxs)(eI.Z,{className:"border border-yellow-300 bg-yellow-50",children:[(0,c.jsxs)("div",{className:"flex items-center mb-2 text-yellow-800",children:[(0,c.jsx)(lO.Z,{className:"h-5 w-5 mr-2"}),(0,c.jsx)(E.Z,{className:"text-lg text-yellow-800",children:"Your SCIM Token"})]}),(0,c.jsx)(A.Z,{className:"text-yellow-800 mb-4 font-medium",children:"Make sure to copy this token now. You will not be able to see it again."}),(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(S.Z,{value:i.token,className:"flex-grow mr-2 bg-white",type:"password",disabled:!0}),(0,c.jsx)(P.CopyToClipboard,{text:i.token,onCopy:()=>D.ZP.success("Token copied to clipboard"),children:(0,c.jsxs)(k.Z,{variant:"primary",className:"flex items-center",children:[(0,c.jsx)(s3.Z,{className:"h-4 w-4 mr-1"}),"Copy"]})})]}),(0,c.jsxs)(k.Z,{className:"mt-4 flex items-center",variant:"secondary",onClick:()=>o(null),children:[(0,c.jsx)(lL.Z,{className:"h-4 w-4 mr-1"}),"Create Another Token"]})]}):(0,c.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,c.jsxs)(L.Z,{form:a,onFinish:h,layout:"vertical",children:[(0,c.jsx)(L.Z.Item,{name:"key_alias",label:"Token Name",rules:[{required:!0,message:"Please enter a name for your token"}],children:(0,c.jsx)(S.Z,{placeholder:"SCIM Access Token"})}),(0,c.jsx)(L.Z.Item,{children:(0,c.jsxs)(k.Z,{variant:"primary",type:"submit",loading:r,className:"flex items-center",children:[(0,c.jsx)(lP.Z,{className:"h-4 w-4 mr-1"}),"Create SCIM Token"]})})]})})]})]})]})})},lM=e=>{let{searchParams:s,accessToken:l,showSSOBanner:t,premiumUser:a,proxySettings:r}=e,[n]=L.Z.useForm(),[i]=L.Z.useForm(),{Title:o,Paragraph:u}=es.default,[x,h]=(0,d.useState)(""),[p,g]=(0,d.useState)(null),[j,f]=(0,d.useState)(null),[_,y]=(0,d.useState)(!1),[b,Z]=(0,d.useState)(!1),[N,w]=(0,d.useState)(!1),[S,C]=(0,d.useState)(!1),[I,T]=(0,d.useState)(!1),[A,E]=(0,d.useState)(!1),[P,O]=(0,d.useState)(!1),[F,U]=(0,d.useState)(!1),[z,V]=(0,d.useState)(!1),[K,B]=(0,d.useState)([]),[H,J]=(0,d.useState)(null);(0,m.useRouter)();let[W,G]=(0,d.useState)(null);console.log=function(){};let Y=eJ(),$="All IP Addresses Allowed",X=Y;X+="/fallback/login";let Q=async()=>{try{if(!0!==a){D.ZP.error("This feature is only available for premium users. Please upgrade your account.");return}if(l){let e=await (0,v.PT)(l);B(e&&e.length>0?e:[$])}else B([$])}catch(e){console.error("Error fetching allowed IPs:",e),D.ZP.error("Failed to fetch allowed IPs ".concat(e)),B([$])}finally{!0===a&&O(!0)}},ee=async e=>{try{if(l){await (0,v.eH)(l,e.ip);let s=await (0,v.PT)(l);B(s),D.ZP.success("IP address added successfully")}}catch(e){console.error("Error adding IP:",e),D.ZP.error("Failed to add IP address ".concat(e))}finally{U(!1)}},el=async e=>{J(e),V(!0)},et=async()=>{if(H&&l)try{await (0,v.$I)(l,H);let e=await (0,v.PT)(l);B(e.length>0?e:[$]),D.ZP.success("IP address deleted successfully")}catch(e){console.error("Error deleting IP:",e),D.ZP.error("Failed to delete IP address ".concat(e))}finally{V(!1),J(null)}};(0,d.useEffect)(()=>{(async()=>{if(null!=l){let e=[],s=await (0,v.Xd)(l,"proxy_admin_viewer");console.log("proxy admin viewer response: ",s);let t=s.users;console.log("proxy viewers response: ".concat(t)),t.forEach(s=>{e.push({user_role:s.user_role,user_id:s.user_id,user_email:s.user_email})}),console.log("proxy viewers: ".concat(t));let a=(await (0,v.Xd)(l,"proxy_admin")).users;a.forEach(s=>{e.push({user_role:s.user_role,user_id:s.user_id,user_email:s.user_email})}),console.log("proxy admins: ".concat(a)),console.log("combinedList: ".concat(e)),g(e),G(await (0,v.lg)(l))}})()},[l]);let ea=async e=>{try{if(null!=l&&null!=p){var s;D.ZP.info("Making API Call"),e.user_email,e.user_id;let t=await (0,v.pf)(l,e,"proxy_admin"),a=(null===(s=t.data)||void 0===s?void 0:s.user_id)||t.user_id;(0,v.XO)(l,a).then(e=>{f(e),y(!0)}),console.log("response for team create call: ".concat(t));let r=p.findIndex(e=>(console.log("user.user_id=".concat(e.user_id,"; response.user_id=").concat(a)),e.user_id===t.user_id));console.log("foundIndex: ".concat(r)),-1==r&&(console.log("updates admin with new user"),p.push(t),g(p)),n.resetFields(),w(!1)}}catch(e){console.error("Error creating the key:",e)}},er=async e=>{if(null==l)return;let s=lT[e.sso_provider],t={PROXY_BASE_URL:e.proxy_base_url};s&&Object.entries(s.envVarMap).forEach(s=>{let[l,a]=s;e[l]&&(t[a]=e[l])}),(0,v.K_)(l,{environment_variables:t})};return console.log("admins: ".concat(null==p?void 0:p.length)),(0,c.jsxs)("div",{className:"w-full m-2 mt-2 p-8",children:[(0,c.jsx)(o,{level:4,children:"Admin Access "}),(0,c.jsx)(u,{children:"Go to 'Internal Users' page to add other admins."}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{children:[(0,c.jsx)(eT.Z,{children:"Security Settings"}),(0,c.jsx)(eT.Z,{children:"SCIM"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(o,{level:4,children:" ✨ Security Settings"}),(0,c.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"1rem",marginTop:"1rem"},children:[(0,c.jsx)("div",{children:(0,c.jsx)(k.Z,{onClick:()=>!0===a?T(!0):D.ZP.error("Only premium users can add SSO"),children:"Add SSO"})}),(0,c.jsx)("div",{children:(0,c.jsx)(k.Z,{onClick:Q,children:"Allowed IPs"})})]})]}),(0,c.jsxs)("div",{className:"flex justify-start mb-4",children:[(0,c.jsx)(lA,{isAddSSOModalVisible:I,isInstructionsModalVisible:A,handleAddSSOOk:()=>{T(!1),n.resetFields()},handleAddSSOCancel:()=>{T(!1),n.resetFields()},handleShowInstructions:e=>{ea(e),er(e),T(!1),E(!0)},handleInstructionsOk:()=>{E(!1)},handleInstructionsCancel:()=>{E(!1)},form:n}),(0,c.jsx)(M.Z,{title:"Manage Allowed IP Addresses",width:800,visible:P,onCancel:()=>O(!1),footer:[(0,c.jsx)(k.Z,{className:"mx-1",onClick:()=>U(!0),children:"Add IP Address"},"add"),(0,c.jsx)(k.Z,{onClick:()=>O(!1),children:"Close"},"close")],children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"IP Address"}),(0,c.jsx)(e1.Z,{className:"text-right",children:"Action"})]})}),(0,c.jsx)(eX.Z,{children:K.map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e}),(0,c.jsx)(eQ.Z,{className:"text-right",children:e!==$&&(0,c.jsx)(k.Z,{onClick:()=>el(e),color:"red",size:"xs",children:"Delete"})})]},s))})]})}),(0,c.jsx)(M.Z,{title:"Add Allowed IP Address",visible:F,onCancel:()=>U(!1),footer:null,children:(0,c.jsxs)(L.Z,{onFinish:ee,children:[(0,c.jsx)(L.Z.Item,{name:"ip",rules:[{required:!0,message:"Please enter an IP address"}],children:(0,c.jsx)(q.default,{placeholder:"Enter IP address"})}),(0,c.jsx)(L.Z.Item,{children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Add IP Address"})})]})}),(0,c.jsx)(M.Z,{title:"Confirm Delete",visible:z,onCancel:()=>V(!1),onOk:et,footer:[(0,c.jsx)(k.Z,{className:"mx-1",onClick:()=>et(),children:"Yes"},"delete"),(0,c.jsx)(k.Z,{onClick:()=>V(!1),children:"Close"},"close")],children:(0,c.jsxs)("p",{children:["Are you sure you want to delete the IP address: ",H,"?"]})})]}),(0,c.jsxs)(lC.Z,{title:"Login without SSO",color:"teal",children:["If you need to login without sso, you can access"," ",(0,c.jsxs)("a",{href:X,target:"_blank",children:[(0,c.jsx)("b",{children:X})," "]})]})]}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(lD,{accessToken:l,userID:p&&p.length>0?p[0].user_id:null,proxySettings:r})})]})]})]})},lF=l(92858),lR=e=>{let{alertingSettings:s,handleInputChange:l,handleResetField:t,handleSubmit:a,premiumUser:r}=e,[n]=L.Z.useForm();return(0,c.jsxs)(L.Z,{form:n,onFinish:()=>{console.log("INSIDE ONFINISH");let e=n.getFieldsValue(),s=Object.entries(e).every(e=>{let[s,l]=e;return"boolean"!=typeof l&&(""===l||null==l)});console.log("formData: ".concat(JSON.stringify(e),", isEmpty: ").concat(s)),s?console.log("Some form fields are empty."):a(e)},labelAlign:"left",children:[s.map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsxs)(eQ.Z,{align:"center",children:[(0,c.jsx)(A.Z,{children:e.field_name}),(0,c.jsx)("p",{style:{fontSize:"0.65rem",color:"#808080",fontStyle:"italic"},className:"mt-1",children:e.field_description})]}),e.premium_field?r?(0,c.jsx)(L.Z.Item,{name:e.field_name,children:(0,c.jsx)(eQ.Z,{children:"Integer"===e.field_type?(0,c.jsx)(H.Z,{step:1,value:e.field_value,onChange:s=>l(e.field_name,s)}):"Boolean"===e.field_type?(0,c.jsx)(lF.Z,{checked:e.field_value,onChange:s=>l(e.field_name,s)}):(0,c.jsx)(q.default,{value:e.field_value,onChange:s=>l(e.field_name,s)})})}):(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(k.Z,{className:"flex items-center justify-center",children:(0,c.jsx)("a",{href:"https://forms.gle/W3U4PZpJGFHWtHyA9",target:"_blank",children:"✨ Enterprise Feature"})})}):(0,c.jsx)(L.Z.Item,{name:e.field_name,className:"mb-0",valuePropName:"Boolean"===e.field_type?"checked":"value",children:(0,c.jsx)(eQ.Z,{children:"Integer"===e.field_type?(0,c.jsx)(H.Z,{step:1,value:e.field_value,onChange:s=>l(e.field_name,s),className:"p-0"}):"Boolean"===e.field_type?(0,c.jsx)(lF.Z,{checked:e.field_value,onChange:s=>{l(e.field_name,s),n.setFieldsValue({[e.field_name]:s})}}):(0,c.jsx)(q.default,{value:e.field_value,onChange:s=>l(e.field_name,s)})})}),(0,c.jsx)(eQ.Z,{children:!0==e.stored_in_db?(0,c.jsx)(eC.Z,{icon:ed.Z,className:"text-white",children:"In DB"}):!1==e.stored_in_db?(0,c.jsx)(eC.Z,{className:"text-gray bg-white outline",children:"In Config"}):(0,c.jsx)(eC.Z,{className:"text-gray bg-white outline",children:"Not Set"})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(sy.Z,{icon:eM.Z,color:"red",onClick:()=>t(e.field_name,s),children:"Reset"})})]},s)),(0,c.jsx)("div",{children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Update Settings"})})]})},lq=e=>{let{accessToken:s,premiumUser:l}=e,[t,a]=(0,d.useState)([]);return(0,d.useEffect)(()=>{s&&(0,v.RQ)(s).then(e=>{a(e)})},[s]),(0,c.jsx)(lR,{alertingSettings:t,handleInputChange:(e,s)=>{let l=t.map(l=>l.field_name===e?{...l,field_value:s}:l);console.log("updatedSettings: ".concat(JSON.stringify(l))),a(l)},handleResetField:(e,l)=>{if(s)try{let s=t.map(s=>s.field_name===e?{...s,stored_in_db:null,field_value:s.field_default_value}:s);a(s)}catch(e){console.log("ERROR OCCURRED!")}},handleSubmit:e=>{if(!s||(console.log("formValues: ".concat(e)),null==e||void 0==e))return;let l={};t.forEach(e=>{l[e.field_name]=e.field_value});let a={...e,...l};console.log("mergedFormValues: ".concat(JSON.stringify(a)));let{slack_alerting:r,...n}=a;console.log("slack_alerting: ".concat(r,", alertingArgs: ").concat(JSON.stringify(n)));try{(0,v.jA)(s,"alerting_args",n),"boolean"==typeof r&&(!0==r?(0,v.jA)(s,"alerting",["slack"]):(0,v.jA)(s,"alerting",[])),D.ZP.success("Wait 10s for proxy to update.")}catch(e){}},premiumUser:l})},lU=l(86582);let{Title:lz,Paragraph:lV}=es.default;console.log=function(){};var lK=e=>{let{accessToken:s,userRole:l,userID:t,premiumUser:a}=e,[r,n]=(0,d.useState)([]),[i,o]=(0,d.useState)([]),[m,u]=(0,d.useState)(!1),[x]=L.Z.useForm(),[h,p]=(0,d.useState)(null),[g,j]=(0,d.useState)([]),[f,_]=(0,d.useState)(""),[y,b]=(0,d.useState)({}),[Z,N]=(0,d.useState)([]),[C,I]=(0,d.useState)(!1),[T,E]=(0,d.useState)([]),[P,F]=(0,d.useState)(null),[q,U]=(0,d.useState)([]),[z,V]=(0,d.useState)(!1),[K,B]=(0,d.useState)(null),H=e=>{Z.includes(e)?N(Z.filter(s=>s!==e)):N([...Z,e])},J={llm_exceptions:"LLM Exceptions",llm_too_slow:"LLM Responses Too Slow",llm_requests_hanging:"LLM Requests Hanging",budget_alerts:"Budget Alerts (API Keys, Users)",db_exceptions:"Database Exceptions (Read/Write)",daily_reports:"Weekly/Monthly Spend Reports",outage_alerts:"Outage Alerts",region_outage_alerts:"Region Outage Alerts"};(0,d.useEffect)(()=>{s&&l&&t&&(0,v.BL)(s,t,l).then(e=>{console.log("callbacks",e),n(e.callbacks),E(e.available_callbacks);let s=e.alerts;if(console.log("alerts_data",s),s&&s.length>0){let e=s[0];console.log("_alert_info",e);let l=e.variables.SLACK_WEBHOOK_URL;console.log("catch_all_webhook",l),N(e.active_alerts),_(l),b(e.alerts_to_webhook)}o(s)})},[s,l,t]);let W=e=>Z&&Z.includes(e),G=()=>{if(!s)return;let e={};i.filter(e=>"email"===e.name).forEach(s=>{var l;Object.entries(null!==(l=s.variables)&&void 0!==l?l:{}).forEach(s=>{let[l,t]=s,a=document.querySelector('input[name="'.concat(l,'"]'));a&&a.value&&(e[l]=null==a?void 0:a.value)})}),console.log("updatedVariables",e);try{(0,v.K_)(s,{general_settings:{alerting:["email"]},environment_variables:e})}catch(e){D.ZP.error("Failed to update alerts: "+e,20)}D.ZP.success("Email settings updated successfully")},Y=async e=>{if(!s)return;let l={};Object.entries(e).forEach(e=>{let[s,t]=e;"callback"!==s&&(l[s]=t)});try{await (0,v.K_)(s,{environment_variables:l}),D.ZP.success("Callback added successfully"),u(!1),x.resetFields(),p(null)}catch(e){D.ZP.error("Failed to add callback: "+e,20)}},$=async e=>{if(!s)return;let l=null==e?void 0:e.callback,t={};Object.entries(e).forEach(e=>{let[s,l]=e;"callback"!==s&&(t[s]=l)});try{await (0,v.K_)(s,{environment_variables:t,litellm_settings:{success_callback:[l]}}),D.ZP.success("Callback ".concat(l," added successfully")),u(!1),x.resetFields(),p(null)}catch(e){D.ZP.error("Failed to add callback: "+e,20)}},X=e=>{console.log("inside handleSelectedCallbackChange",e),p(e.litellm_callback_name),console.log("all callbacks",T),e&&e.litellm_callback_params?(U(e.litellm_callback_params),console.log("selectedCallbackParams",q)):U([])};return s?(console.log("callbacks: ".concat(r)),(0,c.jsxs)("div",{className:"w-full mx-4",children:[(0,c.jsx)(w.Z,{numItems:1,className:"gap-2 p-8 w-full mt-2",children:(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{variant:"line",defaultValue:"1",children:[(0,c.jsx)(eT.Z,{value:"1",children:"Logging Callbacks"}),(0,c.jsx)(eT.Z,{value:"2",children:"Alerting Types"}),(0,c.jsx)(eT.Z,{value:"3",children:"Alerting Settings"}),(0,c.jsx)(eT.Z,{value:"4",children:"Email Alerts"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsxs)(eP.Z,{children:[(0,c.jsx)(lz,{level:4,children:"Active Logging Callbacks"}),(0,c.jsx)(w.Z,{numItems:2,children:(0,c.jsx)(eI.Z,{className:"max-h-[50vh]",children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsx)(e2.Z,{children:(0,c.jsx)(e1.Z,{children:"Callback Name"})})}),(0,c.jsx)(eX.Z,{children:r.map((e,l)=>(0,c.jsxs)(e2.Z,{className:"flex justify-between",children:[(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{children:e.name})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsxs)(w.Z,{numItems:2,className:"flex justify-between",children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>{B(e),V(!0)}}),(0,c.jsx)(k.Z,{onClick:()=>(0,v.jE)(s,e.name),className:"ml-2",variant:"secondary",children:"Test Callback"})]})})]},l))})]})})}),(0,c.jsx)(k.Z,{className:"mt-2",onClick:()=>I(!0),children:"Add Callback"})]}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)(A.Z,{className:"my-2",children:["Alerts are only supported for Slack Webhook URLs. Get your webhook urls from"," ",(0,c.jsx)("a",{href:"https://api.slack.com/messaging/webhooks",target:"_blank",style:{color:"blue"},children:"here"})]}),(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{}),(0,c.jsx)(e1.Z,{}),(0,c.jsx)(e1.Z,{children:"Slack Webhook URL"})]})}),(0,c.jsx)(eX.Z,{children:Object.entries(J).map((e,s)=>{let[l,t]=e;return(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:"region_outage_alerts"==l?a?(0,c.jsx)(lF.Z,{id:"switch",name:"switch",checked:W(l),onChange:()=>H(l)}):(0,c.jsx)(k.Z,{className:"flex items-center justify-center",children:(0,c.jsx)("a",{href:"https://forms.gle/W3U4PZpJGFHWtHyA9",target:"_blank",children:"✨ Enterprise Feature"})}):(0,c.jsx)(lF.Z,{id:"switch",name:"switch",checked:W(l),onChange:()=>H(l)})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{children:t})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(S.Z,{name:l,type:"password",defaultValue:y&&y[l]?y[l]:f})})]},s)})})]}),(0,c.jsx)(k.Z,{size:"xs",className:"mt-2",onClick:()=>{if(!s)return;let e={};Object.entries(J).forEach(s=>{let[l,t]=s,a=document.querySelector('input[name="'.concat(l,'"]'));console.log("key",l),console.log("webhookInput",a);let r=(null==a?void 0:a.value)||"";console.log("newWebhookValue",r),e[l]=r}),console.log("updatedAlertToWebhooks",e);let l={general_settings:{alert_to_webhook_url:e,alert_types:Z}};console.log("payload",l);try{(0,v.K_)(s,l)}catch(e){D.ZP.error("Failed to update alerts: "+e,20)}D.ZP.success("Alerts updated successfully")},children:"Save Changes"}),(0,c.jsx)(k.Z,{onClick:()=>(0,v.jE)(s,"slack"),className:"mx-2",children:"Test Alerts"})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(lq,{accessToken:s,premiumUser:a})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(lz,{level:4,children:"Email Settings"}),(0,c.jsxs)(A.Z,{children:[(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/email",target:"_blank",style:{color:"blue"},children:" LiteLLM Docs: email alerts"})," ",(0,c.jsx)("br",{})]}),(0,c.jsx)("div",{className:"flex w-full",children:i.filter(e=>"email"===e.name).map((e,s)=>{var l;return(0,c.jsx)(eQ.Z,{children:(0,c.jsx)("ul",{children:(0,c.jsx)(w.Z,{numItems:2,children:Object.entries(null!==(l=e.variables)&&void 0!==l?l:{}).map(e=>{let[s,l]=e;return(0,c.jsxs)("li",{className:"mx-2 my-2",children:[!0!=a&&("EMAIL_LOGO_URL"===s||"EMAIL_SUPPORT_CONTACT"===s)?(0,c.jsxs)("div",{children:[(0,c.jsx)("a",{href:"https://forms.gle/W3U4PZpJGFHWtHyA9",target:"_blank",children:(0,c.jsxs)(A.Z,{className:"mt-2",children:[" ","✨ ",s]})}),(0,c.jsx)(S.Z,{name:s,defaultValue:l,type:"password",disabled:!0,style:{width:"400px"}})]}):(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"mt-2",children:s}),(0,c.jsx)(S.Z,{name:s,defaultValue:l,type:"password",style:{width:"400px"}})]}),(0,c.jsxs)("p",{style:{fontSize:"small",fontStyle:"italic"},children:["SMTP_HOST"===s&&(0,c.jsxs)("div",{style:{color:"gray"},children:["Enter the SMTP host address, e.g. `smtp.resend.com`",(0,c.jsx)("span",{style:{color:"red"},children:" Required * "})]}),"SMTP_PORT"===s&&(0,c.jsxs)("div",{style:{color:"gray"},children:["Enter the SMTP port number, e.g. `587`",(0,c.jsx)("span",{style:{color:"red"},children:" Required * "})]}),"SMTP_USERNAME"===s&&(0,c.jsxs)("div",{style:{color:"gray"},children:["Enter the SMTP username, e.g. `username`",(0,c.jsx)("span",{style:{color:"red"},children:" Required * "})]}),"SMTP_PASSWORD"===s&&(0,c.jsx)("span",{style:{color:"red"},children:" Required * "}),"SMTP_SENDER_EMAIL"===s&&(0,c.jsxs)("div",{style:{color:"gray"},children:["Enter the sender email address, e.g. `<EMAIL>`",(0,c.jsx)("span",{style:{color:"red"},children:" Required * "})]}),"TEST_EMAIL_ADDRESS"===s&&(0,c.jsxs)("div",{style:{color:"gray"},children:["Email Address to send `Test Email Alert` to. example: `<EMAIL>`",(0,c.jsx)("span",{style:{color:"red"},children:" Required * "})]}),"EMAIL_LOGO_URL"===s&&(0,c.jsx)("div",{style:{color:"gray"},children:"(Optional) Customize the Logo that appears in the email, pass a url to your logo"}),"EMAIL_SUPPORT_CONTACT"===s&&(0,c.jsx)("div",{style:{color:"gray"},children:"(Optional) Customize the support email address that appears in the email. <NAME_EMAIL>"})]})]},s)})})})},s)})}),(0,c.jsx)(k.Z,{className:"mt-2",onClick:()=>G(),children:"Save Changes"}),(0,c.jsx)(k.Z,{onClick:()=>(0,v.jE)(s,"email"),className:"mx-2",children:"Test Email Alerts"})]})})]})]})}),(0,c.jsxs)(M.Z,{title:"Add Logging Callback",visible:C,width:800,onCancel:()=>I(!1),footer:null,children:[(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/logging",className:"mb-8 mt-4",target:"_blank",style:{color:"blue"},children:" LiteLLM Docs: Logging"}),(0,c.jsx)(L.Z,{form:x,onFinish:$,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(lU.Z,{label:"Callback",name:"callback",rules:[{required:!0,message:"Please select a callback"}],children:(0,c.jsx)(O.default,{onChange:e=>{let s=T[e];s&&(console.log(s.ui_callback_name),X(s))},children:T&&Object.values(T).map(e=>(0,c.jsx)(ee.Z,{value:e.litellm_callback_name,children:e.ui_callback_name},e.litellm_callback_name))})}),q&&q.map(e=>(0,c.jsx)(lU.Z,{label:e,name:e,rules:[{required:!0,message:"Please enter the value for "+e}],children:(0,c.jsx)(S.Z,{type:"password"})},e)),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Save"})})]})})]}),(0,c.jsx)(M.Z,{visible:z,width:800,title:"Edit ".concat(null==K?void 0:K.name," Settings"),onCancel:()=>V(!1),footer:null,children:(0,c.jsxs)(L.Z,{form:x,onFinish:Y,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsx)(c.Fragment,{children:K&&K.variables&&Object.entries(K.variables).map(e=>{let[s,l]=e;return(0,c.jsx)(lU.Z,{label:s,name:s,children:(0,c.jsx)(S.Z,{type:"password",defaultValue:l})},s)})}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Save"})})]})})]})):null},lB=l(92414),lH=l(46030);let{Option:lJ}=O.default;var lW=e=>{let{models:s,accessToken:l,routerSettings:t,setRouterSettings:a}=e,[r]=L.Z.useForm(),[n,i]=(0,d.useState)(!1),[o,m]=(0,d.useState)("");return(0,c.jsxs)("div",{children:[(0,c.jsx)(k.Z,{className:"mx-auto",onClick:()=>i(!0),children:"+ Add Fallbacks"}),(0,c.jsx)(M.Z,{title:"Add Fallbacks",visible:n,width:800,footer:null,onOk:()=>{i(!1),r.resetFields()},onCancel:()=>{i(!1),r.resetFields()},children:(0,c.jsxs)(L.Z,{form:r,onFinish:e=>{console.log(e);let{model_name:s,models:n}=e,o=[...t.fallbacks||[],{[s]:n}],c={...t,fallbacks:o};console.log(c);try{(0,v.K_)(l,{router_settings:c}),a(c)}catch(e){D.ZP.error("Failed to update router settings: "+e,20)}D.ZP.success("router settings updated successfully"),i(!1),r.resetFields()},labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"Public Model Name",name:"model_name",rules:[{required:!0,message:"Set the model to fallback for"}],help:"required",children:(0,c.jsx)(eS.Z,{defaultValue:o,children:s&&s.map((e,s)=>(0,c.jsx)(ee.Z,{value:e,onClick:()=>m(e),children:e},s))})}),(0,c.jsx)(L.Z.Item,{label:"Fallback Models",name:"models",rules:[{required:!0,message:"Please select a model"}],help:"required",children:(0,c.jsx)(lB.Z,{value:s,children:s&&s.filter(e=>e!=o).map(e=>(0,c.jsx)(lH.Z,{value:e,children:e},e))})})]}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Add Fallbacks"})})]})})]})},lG=l(26433);async function lY(e,s){console.log=function(){},console.log("isLocal:",!1);let l=window.location.origin,t=new lG.ZP.OpenAI({apiKey:s,baseURL:l,dangerouslyAllowBrowser:!0});try{let s=await t.chat.completions.create({model:e,messages:[{role:"user",content:"Hi, this is a test message"}],mock_testing_fallbacks:!0});D.ZP.success((0,c.jsxs)("span",{children:["Test model=",(0,c.jsx)("strong",{children:e}),", received model=",(0,c.jsx)("strong",{children:s.model}),". See"," ",(0,c.jsx)("a",{href:"#",onClick:()=>window.open("https://docs.litellm.ai/docs/proxy/reliability","_blank"),style:{textDecoration:"underline",color:"blue"},children:"curl"})]}))}catch(e){D.ZP.error("Error occurred while generating model response. Please try again. Error: ".concat(e),20)}}let l$={ttl:3600,lowest_latency_buffer:0},lX=e=>{let{selectedStrategy:s,strategyArgs:l,paramExplanation:t}=e;return(0,c.jsxs)(C.Z,{children:[(0,c.jsx)(T.Z,{className:"text-sm font-medium text-tremor-content-strong dark:text-dark-tremor-content-strong",children:"Routing Strategy Specific Args"}),(0,c.jsx)(I.Z,{children:"latency-based-routing"==s?(0,c.jsx)(eI.Z,{children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Setting"}),(0,c.jsx)(e1.Z,{children:"Value"})]})}),(0,c.jsx)(eX.Z,{children:Object.entries(l).map(e=>{let[s,l]=e;return(0,c.jsxs)(e2.Z,{children:[(0,c.jsxs)(eQ.Z,{children:[(0,c.jsx)(A.Z,{children:s}),(0,c.jsx)("p",{style:{fontSize:"0.65rem",color:"#808080",fontStyle:"italic"},className:"mt-1",children:t[s]})]}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(S.Z,{name:s,defaultValue:"object"==typeof l?JSON.stringify(l,null,2):l.toString()})})]},s)})})]})}):(0,c.jsx)(A.Z,{children:"No specific settings"})})]})};var lQ=e=>{let{accessToken:s,userRole:l,userID:t,modelData:a}=e,[r,n]=(0,d.useState)({}),[i,o]=(0,d.useState)({}),[m,u]=(0,d.useState)([]),[x,h]=(0,d.useState)(!1),[p]=L.Z.useForm(),[g,j]=(0,d.useState)(null),[f,_]=(0,d.useState)(null),[y,b]=(0,d.useState)(null),Z={routing_strategy_args:"(dict) Arguments to pass to the routing strategy",routing_strategy:"(string) Routing strategy to use",allowed_fails:"(int) Number of times a deployment can fail before being added to cooldown",cooldown_time:"(int) time in seconds to cooldown a deployment after failure",num_retries:"(int) Number of retries for failed requests. Defaults to 0.",timeout:"(float) Timeout for requests. Defaults to None.",retry_after:"(int) Minimum time to wait before retrying a failed request",ttl:"(int) Sliding window to look back over when calculating the average latency of a deployment. Default - 1 hour (in seconds).",lowest_latency_buffer:"(float) Shuffle between deployments within this % of the lowest latency. Default - 0 (i.e. always pick lowest latency)."};(0,d.useEffect)(()=>{s&&l&&t&&((0,v.BL)(s,t,l).then(e=>{console.log("callbacks",e);let s=e.router_settings;"model_group_retry_policy"in s&&delete s.model_group_retry_policy,n(s)}),(0,v.YU)(s).then(e=>{u(e)}))},[s,l,t]);let C=async e=>{if(!s)return;console.log("received key: ".concat(e)),console.log("routerSettings['fallbacks']: ".concat(r.fallbacks));let l=r.fallbacks.map(s=>(e in s&&delete s[e],s)).filter(e=>Object.keys(e).length>0),t={...r,fallbacks:l};try{await (0,v.K_)(s,{router_settings:t}),n(t),D.ZP.success("Router settings updated successfully")}catch(e){D.ZP.error("Failed to update router settings: "+e,20)}},I=(e,s)=>{u(m.map(l=>l.field_name===e?{...l,field_value:s}:l))},T=(e,l)=>{if(!s)return;let t=m[l].field_value;if(null!=t&&void 0!=t)try{(0,v.jA)(s,e,t);let l=m.map(s=>s.field_name===e?{...s,stored_in_db:!0}:s);u(l)}catch(e){}},P=(e,l)=>{if(s)try{(0,v.ao)(s,e);let l=m.map(s=>s.field_name===e?{...s,stored_in_db:null,field_value:null}:s);u(l)}catch(e){}},O=e=>{if(!s)return;console.log("router_settings",e);let l=Object.fromEntries(Object.entries(e).map(e=>{let[s,l]=e;if("routing_strategy_args"!==s&&"routing_strategy"!==s){var t;return[s,(null===(t=document.querySelector('input[name="'.concat(s,'"]')))||void 0===t?void 0:t.value)||l]}if("routing_strategy"==s)return[s,f];if("routing_strategy_args"==s&&"latency-based-routing"==f){let e={},s=document.querySelector('input[name="lowest_latency_buffer"]'),l=document.querySelector('input[name="ttl"]');return(null==s?void 0:s.value)&&(e.lowest_latency_buffer=Number(s.value)),(null==l?void 0:l.value)&&(e.ttl=Number(l.value)),console.log("setRoutingStrategyArgs: ".concat(e)),["routing_strategy_args",e]}return null}).filter(e=>null!=e));console.log("updatedVariables",l);try{(0,v.K_)(s,{router_settings:l})}catch(e){D.ZP.error("Failed to update router settings: "+e,20)}D.ZP.success("router settings updated successfully")};return s?(0,c.jsx)("div",{className:"w-full mx-4",children:(0,c.jsxs)(eA.Z,{className:"gap-2 p-8 h-[75vh] w-full mt-2",children:[(0,c.jsxs)(eE.Z,{variant:"line",defaultValue:"1",children:[(0,c.jsx)(eT.Z,{value:"1",children:"Loadbalancing"}),(0,c.jsx)(eT.Z,{value:"2",children:"Fallbacks"}),(0,c.jsx)(eT.Z,{value:"3",children:"General"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 p-8 w-full mt-2",children:[(0,c.jsx)(E.Z,{children:"Router Settings"}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Setting"}),(0,c.jsx)(e1.Z,{children:"Value"})]})}),(0,c.jsx)(eX.Z,{children:Object.entries(r).filter(e=>{let[s,l]=e;return"fallbacks"!=s&&"context_window_fallbacks"!=s&&"routing_strategy_args"!=s}).map(e=>{let[s,l]=e;return(0,c.jsxs)(e2.Z,{children:[(0,c.jsxs)(eQ.Z,{children:[(0,c.jsx)(A.Z,{children:s}),(0,c.jsx)("p",{style:{fontSize:"0.65rem",color:"#808080",fontStyle:"italic"},className:"mt-1",children:Z[s]})]}),(0,c.jsx)(eQ.Z,{children:"routing_strategy"==s?(0,c.jsxs)(eS.Z,{defaultValue:l,className:"w-full max-w-md",onValueChange:_,children:[(0,c.jsx)(ee.Z,{value:"usage-based-routing",children:"usage-based-routing"}),(0,c.jsx)(ee.Z,{value:"latency-based-routing",children:"latency-based-routing"}),(0,c.jsx)(ee.Z,{value:"simple-shuffle",children:"simple-shuffle"})]}):(0,c.jsx)(S.Z,{name:s,defaultValue:"object"==typeof l?JSON.stringify(l,null,2):l.toString()})})]},s)})})]}),(0,c.jsx)(lX,{selectedStrategy:f,strategyArgs:r&&r.routing_strategy_args&&Object.keys(r.routing_strategy_args).length>0?r.routing_strategy_args:l$,paramExplanation:Z})]}),(0,c.jsx)(N.Z,{children:(0,c.jsx)(k.Z,{className:"mt-2",onClick:()=>O(r),children:"Save Changes"})})]})}),(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Model Name"}),(0,c.jsx)(e1.Z,{children:"Fallbacks"})]})}),(0,c.jsx)(eX.Z,{children:r.fallbacks&&r.fallbacks.map((e,l)=>Object.entries(e).map(e=>{let[t,a]=e;return(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:t}),(0,c.jsx)(eQ.Z,{children:Array.isArray(a)?a.join(", "):a}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(k.Z,{onClick:()=>lY(t,s),children:"Test Fallback"})}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>C(t)})})]},l.toString()+t)}))})]}),(0,c.jsx)(lW,{models:(null==a?void 0:a.data)?a.data.map(e=>e.model_name):[],accessToken:s,routerSettings:r,setRouterSettings:n})]}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(eI.Z,{children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Setting"}),(0,c.jsx)(e1.Z,{children:"Value"}),(0,c.jsx)(e1.Z,{children:"Status"}),(0,c.jsx)(e1.Z,{children:"Action"})]})}),(0,c.jsx)(eX.Z,{children:m.filter(e=>"TypedDictionary"!==e.field_type).map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsxs)(eQ.Z,{children:[(0,c.jsx)(A.Z,{children:e.field_name}),(0,c.jsx)("p",{style:{fontSize:"0.65rem",color:"#808080",fontStyle:"italic"},className:"mt-1",children:e.field_description})]}),(0,c.jsx)(eQ.Z,{children:"Integer"==e.field_type?(0,c.jsx)(H.Z,{step:1,value:e.field_value,onChange:s=>I(e.field_name,s)}):null}),(0,c.jsx)(eQ.Z,{children:!0==e.stored_in_db?(0,c.jsx)(eC.Z,{icon:ed.Z,className:"text-white",children:"In DB"}):!1==e.stored_in_db?(0,c.jsx)(eC.Z,{className:"text-gray bg-white outline",children:"In Config"}):(0,c.jsx)(eC.Z,{className:"text-gray bg-white outline",children:"Not Set"})}),(0,c.jsxs)(eQ.Z,{children:[(0,c.jsx)(k.Z,{onClick:()=>T(e.field_name,s),children:"Update"}),(0,c.jsx)(sy.Z,{icon:eM.Z,color:"red",onClick:()=>P(e.field_name,s),children:"Reset"})]})]},s))})]})})})]})]})}):null},l0=l(93142),l1=e=>{let{value:s={},onChange:l}=e,[t,a]=(0,d.useState)(Object.entries(s)),r=e=>{let s=t.filter((s,l)=>l!==e);a(s),null==l||l(Object.fromEntries(s))},n=(e,s,r)=>{let n=[...t];n[e]=[s,r],a(n),null==l||l(Object.fromEntries(n))};return(0,c.jsxs)("div",{children:[t.map((e,s)=>{let[l,t]=e;return(0,c.jsxs)(l0.Z,{style:{display:"flex",marginBottom:8},align:"start",children:[(0,c.jsx)(S.Z,{placeholder:"Header Name",value:l,onChange:e=>n(s,e.target.value,t)}),(0,c.jsx)(S.Z,{placeholder:"Header Value",value:t,onChange:e=>n(s,l,e.target.value)}),(0,c.jsx)(sK.Z,{onClick:()=>r(s)})]},s)}),(0,c.jsx)(R.ZP,{type:"dashed",onClick:()=>{a([...t,["",""]])},icon:(0,c.jsx)(sB.Z,{}),children:"Add Header"})]})};let{Option:l2}=O.default;var l4=e=>{let{accessToken:s,setPassThroughItems:l,passThroughItems:t}=e,[a]=L.Z.useForm(),[r,n]=(0,d.useState)(!1),[i,o]=(0,d.useState)("");return(0,c.jsxs)("div",{children:[(0,c.jsx)(k.Z,{className:"mx-auto",onClick:()=>n(!0),children:"+ Add Pass-Through Endpoint"}),(0,c.jsx)(M.Z,{title:"Add Pass-Through Endpoint",visible:r,width:800,footer:null,onOk:()=>{n(!1),a.resetFields()},onCancel:()=>{n(!1),a.resetFields()},children:(0,c.jsxs)(L.Z,{form:a,onFinish:e=>{console.log(e);let r=[...t,{headers:e.headers,path:e.path,target:e.target}];try{(0,v.Vt)(s,e),l(r)}catch(e){D.ZP.error("Failed to update router settings: "+e,20)}D.ZP.success("Pass through endpoint successfully added"),n(!1),a.resetFields()},labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"Path",name:"path",rules:[{required:!0,message:"The route to be added to the LiteLLM Proxy Server."}],help:"required",children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"Target",name:"target",rules:[{required:!0,message:"The URL to which requests for this path should be forwarded."}],help:"required",children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"Headers",name:"headers",rules:[{required:!0,message:"Key-value pairs of headers to be forwarded with the request. You can set any key value pair here and it will be forwarded to your target endpoint"}],help:"required",children:(0,c.jsx)(l1,{})})]}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Add Pass-Through Endpoint"})})]})})]})},l5=e=>{let{accessToken:s,userRole:l,userID:t,modelData:a}=e,[r,n]=(0,d.useState)([]);(0,d.useEffect)(()=>{s&&l&&t&&(0,v.mp)(s).then(e=>{n(e.endpoints)})},[s,l,t]);let i=(e,l)=>{if(s)try{(0,v.EG)(s,e);let l=r.filter(s=>s.path!==e);n(l),D.ZP.success("Endpoint deleted successfully.")}catch(e){}};return s?(0,c.jsx)("div",{className:"w-full mx-4",children:(0,c.jsx)(eA.Z,{className:"gap-2 p-8 h-[75vh] w-full mt-2",children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Path"}),(0,c.jsx)(e1.Z,{children:"Target"}),(0,c.jsx)(e1.Z,{children:"Headers"}),(0,c.jsx)(e1.Z,{children:"Action"})]})}),(0,c.jsx)(eX.Z,{children:r.map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(A.Z,{children:e.path})}),(0,c.jsx)(eQ.Z,{children:e.target}),(0,c.jsx)(eQ.Z,{children:JSON.stringify(e.headers)}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)(sy.Z,{icon:eM.Z,color:"red",onClick:()=>i(e.path,s),children:"Reset"})})]},s))})]}),(0,c.jsx)(l4,{accessToken:s,setPassThroughItems:n,passThroughItems:r})]})})}):null},l3=e=>{let{isModalVisible:s,accessToken:l,setIsModalVisible:t,setBudgetList:a}=e,[r]=L.Z.useForm(),n=async e=>{if(null!=l&&void 0!=l)try{D.ZP.info("Making API Call");let s=await (0,v.Zr)(l,e);console.log("key create Response:",s),a(e=>e?[...e,s]:[s]),D.ZP.success("API Key Created"),r.resetFields()}catch(e){console.error("Error creating the key:",e),D.ZP.error("Error creating the key: ".concat(e),20)}};return(0,c.jsx)(M.Z,{title:"Create Budget",visible:s,width:800,footer:null,onOk:()=>{t(!1),r.resetFields()},onCancel:()=>{t(!1),r.resetFields()},children:(0,c.jsxs)(L.Z,{form:r,onFinish:n,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"Budget ID",name:"budget_id",rules:[{required:!0,message:"Please input a human-friendly name for the budget"}],help:"A human-friendly name for the budget",children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsx)(L.Z.Item,{label:"Max Tokens per minute",name:"tpm_limit",help:"Default is model limit.",children:(0,c.jsx)(H.Z,{step:1,precision:2,width:200})}),(0,c.jsx)(L.Z.Item,{label:"Max Requests per minute",name:"rpm_limit",help:"Default is model limit.",children:(0,c.jsx)(H.Z,{step:1,precision:2,width:200})}),(0,c.jsxs)(C.Z,{className:"mt-20 mb-8",children:[(0,c.jsx)(T.Z,{children:(0,c.jsx)("b",{children:"Optional Settings"})}),(0,c.jsxs)(I.Z,{children:[(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(H.Z,{step:.01,precision:2,width:200})}),(0,c.jsx)(L.Z.Item,{className:"mt-8",label:"Reset Budget",name:"budget_duration",children:(0,c.jsxs)(O.default,{defaultValue:null,placeholder:"n/a",children:[(0,c.jsx)(O.default.Option,{value:"24h",children:"daily"}),(0,c.jsx)(O.default.Option,{value:"7d",children:"weekly"}),(0,c.jsx)(O.default.Option,{value:"30d",children:"monthly"})]})})]})]})]}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Create Budget"})})]})})},l6=e=>{let{isModalVisible:s,accessToken:l,setIsModalVisible:t,setBudgetList:a,existingBudget:r,handleUpdateCall:n}=e;console.log("existingBudget",r);let[i]=L.Z.useForm();(0,d.useEffect)(()=>{i.setFieldsValue(r)},[r,i]);let o=async e=>{if(null!=l&&void 0!=l)try{D.ZP.info("Making API Call"),t(!0);let s=await (0,v.qI)(l,e);a(e=>e?[...e,s]:[s]),D.ZP.success("Budget Updated"),i.resetFields(),n()}catch(e){console.error("Error creating the key:",e),D.ZP.error("Error creating the key: ".concat(e),20)}};return(0,c.jsx)(M.Z,{title:"Edit Budget",visible:s,width:800,footer:null,onOk:()=>{t(!1),i.resetFields()},onCancel:()=>{t(!1),i.resetFields()},children:(0,c.jsxs)(L.Z,{form:i,onFinish:o,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",initialValues:r,children:[(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(L.Z.Item,{label:"Budget ID",name:"budget_id",rules:[{required:!0,message:"Please input a human-friendly name for the budget"}],help:"A human-friendly name for the budget",children:(0,c.jsx)(S.Z,{placeholder:""})}),(0,c.jsx)(L.Z.Item,{label:"Max Tokens per minute",name:"tpm_limit",help:"Default is model limit.",children:(0,c.jsx)(H.Z,{step:1,precision:2,width:200})}),(0,c.jsx)(L.Z.Item,{label:"Max Requests per minute",name:"rpm_limit",help:"Default is model limit.",children:(0,c.jsx)(H.Z,{step:1,precision:2,width:200})}),(0,c.jsxs)(C.Z,{className:"mt-20 mb-8",children:[(0,c.jsx)(T.Z,{children:(0,c.jsx)("b",{children:"Optional Settings"})}),(0,c.jsxs)(I.Z,{children:[(0,c.jsx)(L.Z.Item,{label:"Max Budget (USD)",name:"max_budget",children:(0,c.jsx)(H.Z,{step:.01,precision:2,width:200})}),(0,c.jsx)(L.Z.Item,{className:"mt-8",label:"Reset Budget",name:"budget_duration",children:(0,c.jsxs)(O.default,{defaultValue:null,placeholder:"n/a",children:[(0,c.jsx)(O.default.Option,{value:"24h",children:"daily"}),(0,c.jsx)(O.default.Option,{value:"7d",children:"weekly"}),(0,c.jsx)(O.default.Option,{value:"30d",children:"monthly"})]})})]})]})]}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(R.ZP,{htmlType:"submit",children:"Save"})})]})})},l8=l(17906),l7=e=>{let{accessToken:s}=e,[l,t]=(0,d.useState)(!1),[a,r]=(0,d.useState)(!1),[n,i]=(0,d.useState)(null),[o,m]=(0,d.useState)([]);(0,d.useEffect)(()=>{s&&(0,v.O3)(s).then(e=>{m(e)})},[s]);let u=async(e,l)=>{console.log("budget_id",e),null!=s&&(i(o.find(s=>s.budget_id===e)||null),r(!0))},x=async(e,l)=>{if(null==s)return;D.ZP.info("Request made"),await (0,v.NV)(s,e);let t=[...o];t.splice(l,1),m(t),D.ZP.success("Budget Deleted.")},h=async()=>{null!=s&&(0,v.O3)(s).then(e=>{m(e)})};return(0,c.jsxs)("div",{className:"w-full mx-auto flex-auto overflow-y-auto m-8 p-2",children:[(0,c.jsx)(k.Z,{size:"sm",variant:"primary",className:"mb-2",onClick:()=>t(!0),children:"+ Create Budget"}),(0,c.jsx)(l3,{accessToken:s,isModalVisible:l,setIsModalVisible:t,setBudgetList:m}),n&&(0,c.jsx)(l6,{accessToken:s,isModalVisible:a,setIsModalVisible:r,setBudgetList:m,existingBudget:n,handleUpdateCall:h}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Create a budget to assign to customers."}),(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Budget ID"}),(0,c.jsx)(e1.Z,{children:"Max Budget"}),(0,c.jsx)(e1.Z,{children:"TPM"}),(0,c.jsx)(e1.Z,{children:"RPM"})]})}),(0,c.jsx)(eX.Z,{children:o.slice().sort((e,s)=>new Date(s.updated_at).getTime()-new Date(e.updated_at).getTime()).map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.budget_id}),(0,c.jsx)(eQ.Z,{children:e.max_budget?e.max_budget:"n/a"}),(0,c.jsx)(eQ.Z,{children:e.tpm_limit?e.tpm_limit:"n/a"}),(0,c.jsx)(eQ.Z,{children:e.rpm_limit?e.rpm_limit:"n/a"}),(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>u(e.budget_id,s)}),(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>x(e.budget_id,s)})]},s))})]})]}),(0,c.jsxs)("div",{className:"mt-5",children:[(0,c.jsx)(A.Z,{className:"text-base",children:"How to use budget id"}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{children:[(0,c.jsx)(eT.Z,{children:"Assign Budget to Customer"}),(0,c.jsx)(eT.Z,{children:"Test it (Curl)"}),(0,c.jsx)(eT.Z,{children:"Test it (OpenAI SDK)"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsx)(l8.Z,{language:"bash",children:"\ncurl -X POST --location '<your_proxy_base_url>/end_user/new' \n-H 'Authorization: Bearer <your-master-key>' \n-H 'Content-Type: application/json' \n-d '{\"user_id\": \"my-customer-id', \"budget_id\": \"<BUDGET_ID>\"}' # \uD83D\uDC48 KEY CHANGE\n\n            "})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(l8.Z,{language:"bash",children:'\ncurl -X POST --location \'<your_proxy_base_url>/chat/completions\' \n-H \'Authorization: Bearer <your-master-key>\' \n-H \'Content-Type: application/json\' \n-d \'{\n  "model": "gpt-3.5-turbo\', \n  "messages":[{"role": "user", "content": "Hey, how\'s it going?"}],\n  "user": "my-customer-id"\n}\' # \uD83D\uDC48 KEY CHANGE\n\n            '})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(l8.Z,{language:"python",children:'from openai import OpenAI\nclient = OpenAI(\n  base_url="<your_proxy_base_url>",\n  api_key="<your_proxy_key>"\n)\n\ncompletion = client.chat.completions.create(\n  model="gpt-3.5-turbo",\n  messages=[\n    {"role": "system", "content": "You are a helpful assistant."},\n    {"role": "user", "content": "Hello!"}\n  ],\n  user="my-customer-id"\n)\n\nprint(completion.choices[0].message)'})})]})]})]})]})},l9=l(77398),te=l.n(l9);function ts(e){let{data:s=[],columns:l,getRowCanExpand:t,renderSubComponent:a,isLoading:r=!1,loadingMessage:n="\uD83D\uDE85 Loading logs...",noDataMessage:i="No logs found"}=e,o=(0,eG.b7)({data:s,columns:l,getRowCanExpand:t,getCoreRowModel:(0,eY.sC)(),getExpandedRowModel:(0,eY.rV)()});return(0,c.jsx)("div",{className:"rounded-lg custom-border table-wrapper",children:(0,c.jsxs)(e$.Z,{className:"[&_td]:py-0.5 [&_th]:py-1",children:[(0,c.jsx)(e0.Z,{children:o.getHeaderGroups().map(e=>(0,c.jsx)(e2.Z,{children:e.headers.map(e=>(0,c.jsx)(e1.Z,{className:"py-1 h-8",children:e.isPlaceholder?null:(0,eG.ie)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,c.jsx)(eX.Z,{children:r?(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:l.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:n})})})}):o.getRowModel().rows.length>0?o.getRowModel().rows.map(e=>(0,c.jsxs)(d.Fragment,{children:[(0,c.jsx)(e2.Z,{className:"h-8",children:e.getVisibleCells().map(e=>(0,c.jsx)(eQ.Z,{className:"py-0.5 max-h-8 overflow-hidden text-ellipsis whitespace-nowrap",children:(0,eG.ie)(e.column.columnDef.cell,e.getContext())},e.id))}),e.getIsExpanded()&&(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:e.getVisibleCells().length,children:a({row:e})})})]},e.id)):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:l.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:i})})})})})]})})}async function tl(e){try{let s=await fetch("http://ip-api.com/json/".concat(e)),l=await s.json();console.log("ip lookup data",l);let t=l.countryCode?l.countryCode.toUpperCase().split("").map(e=>String.fromCodePoint(e.charCodeAt(0)+127397)).join(""):"";return l.country?"".concat(t," ").concat(l.country):"Unknown"}catch(e){return console.error("Error looking up IP:",e),"Unknown"}}let tt=e=>{let{ipAddress:s}=e,[l,t]=d.useState("-");return d.useEffect(()=>{if(!s)return;let e=!0;return tl(s).then(s=>{e&&t(s)}).catch(()=>{e&&t("-")}),()=>{e=!1}},[s]),(0,c.jsx)("span",{children:l})},ta=e=>{try{return new Date(e).toLocaleString("en-US",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!0}).replace(",","")}catch(e){return"Error converting time"}},tr=e=>{let{utcTime:s}=e;return(0,c.jsx)("span",{style:{fontFamily:"monospace",width:"180px",display:"inline-block"},children:ta(s)})},tn=(e,s)=>{var l,t;return(null===(t=e.metadata)||void 0===t?void 0:null===(l=t.mcp_tool_call_metadata)||void 0===l?void 0:l.mcp_server_logo_url)?e.metadata.mcp_tool_call_metadata.mcp_server_logo_url:s?sa(s).logo:""},ti=[{id:"expander",header:()=>null,cell:e=>{let{row:s}=e;return(0,c.jsx)(()=>{let[e,l]=d.useState(s.getIsExpanded()),t=d.useCallback(()=>{l(e=>!e),s.getToggleExpandedHandler()()},[s]);return s.getCanExpand()?(0,c.jsx)("button",{onClick:t,style:{cursor:"pointer"},"aria-label":e?"Collapse row":"Expand row",className:"w-6 h-6 flex items-center justify-center focus:outline-none",children:(0,c.jsx)("svg",{className:"w-4 h-4 transform transition-transform duration-75 ".concat(e?"rotate-90":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})}):(0,c.jsx)("span",{className:"w-6 h-6 flex items-center justify-center",children:"●"})},{})}},{header:"Time",accessorKey:"startTime",cell:e=>(0,c.jsx)(tr,{utcTime:e.getValue()})},{header:"Status",accessorKey:"metadata.status",cell:e=>{let s="failure"!==(e.getValue()||"Success").toLowerCase();return(0,c.jsx)("span",{className:"px-2 py-1 rounded-md text-xs font-medium inline-block text-center w-16 ".concat(s?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:s?"Success":"Failure"})}},{header:"Session ID",accessorKey:"session_id",cell:e=>{let s=String(e.getValue()||""),l=e.row.original.onSessionClick;return(0,c.jsx)(W.Z,{title:String(e.getValue()||""),children:(0,c.jsx)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal text-xs max-w-[15ch] truncate block",onClick:()=>null==l?void 0:l(s),children:String(e.getValue()||"")})})}},{header:"Request ID",accessorKey:"request_id",cell:e=>(0,c.jsx)(W.Z,{title:String(e.getValue()||""),children:(0,c.jsx)("span",{className:"font-mono text-xs max-w-[15ch] truncate block",children:String(e.getValue()||"")})})},{header:"Cost",accessorKey:"spend",cell:e=>(0,c.jsxs)("span",{children:["$",Number(e.getValue()||0).toFixed(6)]})},{header:"Country",accessorKey:"requester_ip_address",cell:e=>(0,c.jsx)(tt,{ipAddress:e.getValue()})},{header:"Team Name",accessorKey:"metadata.user_api_key_team_alias",cell:e=>(0,c.jsx)(W.Z,{title:String(e.getValue()||"-"),children:(0,c.jsx)("span",{className:"max-w-[15ch] truncate block",children:String(e.getValue()||"-")})})},{header:"Key Hash",accessorKey:"metadata.user_api_key",cell:e=>{let s=String(e.getValue()||"-"),l=e.row.original.onKeyHashClick;return(0,c.jsx)(W.Z,{title:s,children:(0,c.jsx)("span",{className:"font-mono max-w-[15ch] truncate block cursor-pointer hover:text-blue-600",onClick:()=>null==l?void 0:l(s),children:s})})}},{header:"Key Name",accessorKey:"metadata.user_api_key_alias",cell:e=>(0,c.jsx)(W.Z,{title:String(e.getValue()||"-"),children:(0,c.jsx)("span",{className:"max-w-[15ch] truncate block",children:String(e.getValue()||"-")})})},{header:"Model",accessorKey:"model",cell:e=>{let s=e.row.original,l=s.custom_llm_provider,t=String(e.getValue()||"");return(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[l&&(0,c.jsx)("img",{src:tn(s,l),alt:"",className:"w-4 h-4",onError:e=>{e.target.style.display="none"}}),(0,c.jsx)(W.Z,{title:t,children:(0,c.jsx)("span",{className:"max-w-[15ch] truncate block",children:t})})]})}},{header:"Tokens",accessorKey:"total_tokens",cell:e=>{let s=e.row.original;return(0,c.jsxs)("span",{className:"text-sm",children:[String(s.total_tokens||"0"),(0,c.jsxs)("span",{className:"text-gray-400 text-xs ml-1",children:["(",String(s.prompt_tokens||"0"),"+",String(s.completion_tokens||"0"),")"]})]})}},{header:"Internal User",accessorKey:"user",cell:e=>(0,c.jsx)(W.Z,{title:String(e.getValue()||"-"),children:(0,c.jsx)("span",{className:"max-w-[15ch] truncate block",children:String(e.getValue()||"-")})})},{header:"End User",accessorKey:"end_user",cell:e=>(0,c.jsx)(W.Z,{title:String(e.getValue()||"-"),children:(0,c.jsx)("span",{className:"max-w-[15ch] truncate block",children:String(e.getValue()||"-")})})},{header:"Tags",accessorKey:"request_tags",cell:e=>{let s=e.getValue();if(!s||0===Object.keys(s).length)return"-";let l=Object.entries(s),t=l[0],a=l.slice(1);return(0,c.jsx)("div",{className:"flex flex-wrap gap-1",children:(0,c.jsx)(W.Z,{title:(0,c.jsx)("div",{className:"flex flex-col gap-1",children:l.map(e=>{let[s,l]=e;return(0,c.jsxs)("span",{children:[s,": ",String(l)]},s)})}),children:(0,c.jsxs)("span",{className:"px-2 py-1 bg-gray-100 rounded-full text-xs",children:[t[0],": ",String(t[1]),a.length>0&&" +".concat(a.length)]})})})}}],to=async(e,s,l,t)=>{console.log("prefetchLogDetails called with",e.length,"logs");let a=e.map(e=>{if(e.request_id)return console.log("Prefetching details for request_id:",e.request_id),t.prefetchQuery({queryKey:["logDetails",e.request_id,s],queryFn:async()=>{console.log("Fetching details for",e.request_id);let t=await (0,v.qk)(l,e.request_id,s);return console.log("Received details for",e.request_id,":",t?"success":"failed"),t},staleTime:6e5,gcTime:6e5})});try{let e=await Promise.all(a);return console.log("All prefetch promises completed:",e.length),e}catch(e){throw console.error("Error in prefetchLogDetails:",e),e}},tc=e=>{var s;let{errorInfo:l}=e,[t,a]=d.useState({}),[r,n]=d.useState(!1),i=e=>{a(s=>({...s,[e]:!s[e]}))},o=l.traceback&&(s=l.traceback)?Array.from(s.matchAll(/File "([^"]+)", line (\d+)/g)).map(e=>{let l=e[1],t=e[2],a=l.split("/").pop()||l,r=e.index||0,n=s.indexOf('File "',r+1),i=n>-1?s.substring(r,n).trim():s.substring(r).trim(),o=i.split("\n"),c="";return o.length>1&&(c=o[o.length-1].trim()),{filePath:l,fileName:a,lineNumber:t,code:c,inFunction:i.includes(" in ")?i.split(" in ")[1].split("\n")[0]:""}}):[];return(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsx)("div",{className:"p-4 border-b",children:(0,c.jsxs)("h3",{className:"text-lg font-medium flex items-center text-red-600",children:[(0,c.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),"Error Details"]})}),(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsxs)("div",{className:"bg-red-50 rounded-md p-4 mb-4",children:[(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"text-red-800 font-medium w-20",children:"Type:"}),(0,c.jsx)("span",{className:"text-red-700",children:l.error_class||"Unknown Error"})]}),(0,c.jsxs)("div",{className:"flex mt-2",children:[(0,c.jsx)("span",{className:"text-red-800 font-medium w-20",children:"Message:"}),(0,c.jsx)("span",{className:"text-red-700",children:l.error_message||"Unknown error occurred"})]})]}),l.traceback&&(0,c.jsxs)("div",{className:"mt-4",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,c.jsx)("h4",{className:"font-medium",children:"Traceback"}),(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsx)("button",{onClick:()=>{let e=!r;if(n(e),o.length>0){let s={};o.forEach((l,t)=>{s[t]=e}),a(s)}},className:"text-gray-500 hover:text-gray-700 flex items-center text-sm",children:r?"Collapse All":"Expand All"}),(0,c.jsxs)("button",{onClick:()=>navigator.clipboard.writeText(l.traceback||""),className:"text-gray-500 hover:text-gray-700 flex items-center",title:"Copy traceback",children:[(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,c.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,c.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]}),(0,c.jsx)("span",{className:"ml-1",children:"Copy"})]})]})]}),(0,c.jsx)("div",{className:"bg-white rounded-md border border-gray-200 overflow-hidden shadow-sm",children:o.map((e,s)=>(0,c.jsxs)("div",{className:"border-b border-gray-200 last:border-b-0",children:[(0,c.jsxs)("div",{className:"px-4 py-2 flex items-center justify-between cursor-pointer hover:bg-gray-50",onClick:()=>i(s),children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("span",{className:"text-gray-400 mr-2 w-12 text-right",children:e.lineNumber}),(0,c.jsx)("span",{className:"text-gray-600 font-medium",children:e.fileName}),(0,c.jsx)("span",{className:"text-gray-500 mx-1",children:"in"}),(0,c.jsx)("span",{className:"text-indigo-600 font-medium",children:e.inFunction||e.fileName})]}),(0,c.jsx)("svg",{className:"w-5 h-5 text-gray-500 transition-transform ".concat(t[s]?"transform rotate-180":""),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),(t[s]||!1)&&e.code&&(0,c.jsx)("div",{className:"px-12 py-2 font-mono text-sm text-gray-800 bg-gray-50 overflow-x-auto border-t border-gray-100",children:e.code})]},s))})]})]})]})},td=e=>{let{show:s}=e;return s?(0,c.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start",children:[(0,c.jsx)("div",{className:"text-blue-500 mr-3 flex-shrink-0 mt-0.5",children:(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,c.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,c.jsx)("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),(0,c.jsx)("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})]})}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h4",{className:"text-sm font-medium text-blue-800",children:"Request/Response Data Not Available"}),(0,c.jsxs)("p",{className:"text-sm text-blue-700 mt-1",children:["To view request and response details, enable prompt storage in your LiteLLM configuration by adding the following to your ",(0,c.jsx)("code",{className:"bg-blue-100 px-1 py-0.5 rounded",children:"proxy_config.yaml"})," file:"]}),(0,c.jsx)("pre",{className:"mt-2 bg-white p-3 rounded border border-blue-200 text-xs font-mono overflow-auto",children:"general_settings:\n  store_model_in_db: true\n  store_prompts_in_spend_logs: true"}),(0,c.jsx)("p",{className:"text-xs text-blue-700 mt-2",children:"Note: This will only affect new requests after the configuration change."})]})]}):null};var tm=l(35829);let tu=e=>{let{sessionId:s,logs:l,onBack:t}=e,[a,r]=(0,d.useState)(null),n=l.reduce((e,s)=>e+(s.spend||0),0),i=l.reduce((e,s)=>e+(s.total_tokens||0),0),o=l.length>0?new Date(l[0].startTime):new Date;return(((l.length>0?new Date(l[l.length-1].endTime):new Date).getTime()-o.getTime())/1e3).toFixed(2),l.map(e=>({time:new Date(e.startTime).toISOString(),tokens:e.total_tokens||0,cost:e.spend||0})),(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"mb-8",children:[(0,c.jsx)("div",{className:"flex items-center space-x-4",children:(0,c.jsxs)("button",{onClick:t,className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,c.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to All Logs"]})}),(0,c.jsxs)("div",{className:"mt-4",children:[(0,c.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Session Details"}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsx)("p",{className:"text-sm text-gray-500 font-mono",children:s}),(0,c.jsxs)("a",{href:"https://docs.litellm.ai/docs/proxy/ui_logs_sessions",target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1",children:["Get started with session management here",(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})})]})]})]})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Requests"}),(0,c.jsx)(tm.Z,{children:l.length})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Cost"}),(0,c.jsxs)(tm.Z,{children:["$",n.toFixed(4)]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Tokens"}),(0,c.jsx)(tm.Z,{children:i})]})]}),(0,c.jsx)(E.Z,{children:"Session Logs"}),(0,c.jsx)("div",{className:"mt-4",children:(0,c.jsx)(ts,{columns:ti,data:l,renderSubComponent:tp,getRowCanExpand:()=>!0,loadingMessage:"Loading logs...",noDataMessage:"No logs found"})})]})};function tx(e){let{data:s}=e,[l,t]=(0,d.useState)(!0),[a,r]=(0,d.useState)({});if(!s||0===s.length)return null;let n=e=>new Date(1e3*e).toLocaleString(),i=(e,s)=>"".concat(((s-e)*1e3).toFixed(2),"ms"),o=(e,s)=>{let l="".concat(e,"-").concat(s);r(e=>({...e,[l]:!e[l]}))};return(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow mb-6",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center p-4 border-b cursor-pointer hover:bg-gray-50",onClick:()=>t(!l),children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("svg",{className:"w-5 h-5 mr-2 text-gray-600 transition-transform ".concat(l?"transform rotate-90":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,c.jsx)("h3",{className:"text-lg font-medium",children:"Vector Store Requests"})]}),(0,c.jsx)("span",{className:"text-sm text-gray-500",children:l?"Click to collapse":"Click to expand"})]}),l&&(0,c.jsx)("div",{className:"p-4",children:s.map((e,s)=>(0,c.jsxs)("div",{className:"mb-6 last:mb-0",children:[(0,c.jsx)("div",{className:"bg-white rounded-lg border p-4 mb-4",children:(0,c.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Query:"}),(0,c.jsx)("span",{className:"font-mono",children:e.query})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Vector Store ID:"}),(0,c.jsx)("span",{className:"font-mono",children:e.vector_store_id})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Provider:"}),(0,c.jsx)("span",{className:"flex items-center",children:(()=>{let{logo:s,displayName:l}=sa(e.custom_llm_provider);return(0,c.jsxs)(c.Fragment,{children:[s&&(0,c.jsx)("img",{src:s,alt:"".concat(l," logo"),className:"h-5 w-5 mr-2"}),l]})})()})]})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Start Time:"}),(0,c.jsx)("span",{children:n(e.start_time)})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"End Time:"}),(0,c.jsx)("span",{children:n(e.end_time)})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Duration:"}),(0,c.jsx)("span",{children:i(e.start_time,e.end_time)})]})]})]})}),(0,c.jsx)("h4",{className:"font-medium mb-2",children:"Search Results"}),(0,c.jsx)("div",{className:"space-y-2",children:e.vector_store_search_response.data.map((e,l)=>{let t=a["".concat(s,"-").concat(l)]||!1;return(0,c.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[(0,c.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 cursor-pointer",onClick:()=>o(s,l),children:[(0,c.jsx)("svg",{className:"w-5 h-5 mr-2 transition-transform ".concat(t?"transform rotate-90":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsxs)("span",{className:"font-medium mr-2",children:["Result ",l+1]}),(0,c.jsxs)("span",{className:"text-gray-500 text-sm",children:["Score: ",(0,c.jsx)("span",{className:"font-mono",children:e.score.toFixed(4)})]})]})]}),t&&(0,c.jsx)("div",{className:"p-3 border-t bg-white",children:e.content.map((e,s)=>(0,c.jsxs)("div",{className:"mb-2 last:mb-0",children:[(0,c.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:e.type}),(0,c.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap break-all bg-gray-50 p-2 rounded",children:e.text})]},s))})]},l)})})]},s))})]})}function th(e){var s,l,t,a,r,n;let{accessToken:i,token:o,userRole:m,userID:u,allTeams:x}=e,[p,g]=(0,d.useState)(""),[j,f]=(0,d.useState)(!1),[_,y]=(0,d.useState)(!1),[b,Z]=(0,d.useState)(1),[N]=(0,d.useState)(50),w=(0,d.useRef)(null),k=(0,d.useRef)(null),S=(0,d.useRef)(null),[C,I]=(0,d.useState)(te()().subtract(24,"hours").format("YYYY-MM-DDTHH:mm")),[T,A]=(0,d.useState)(te()().format("YYYY-MM-DDTHH:mm")),[E,P]=(0,d.useState)(!1),[O,L]=(0,d.useState)(!1),[D,M]=(0,d.useState)(""),[F,R]=(0,d.useState)(""),[q,U]=(0,d.useState)(""),[z,V]=(0,d.useState)(""),[K,B]=(0,d.useState)(null),[H,J]=(0,d.useState)(null),[W,G]=(0,d.useState)("Team ID"),[Y,$]=(0,d.useState)(m&&eg.lo.includes(m)),[X,Q]=(0,d.useState)(null),[ee,es]=(0,d.useState)(null),el=(0,h.NL)();(0,d.useEffect)(()=>{(async()=>{if(H&&i){let e=await (0,v.t0)(i,H);console.log("keyData",e),B({...e.info,token:H,api_key:H})}})()},[H,i]),(0,d.useEffect)(()=>{function e(e){w.current&&!w.current.contains(e.target)&&y(!1),k.current&&!k.current.contains(e.target)&&f(!1),S.current&&!S.current.contains(e.target)&&L(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,d.useEffect)(()=>{m&&eg.lo.includes(m)&&$(!0)},[m]);let et=(0,eV.a)({queryKey:["logs","table",b,N,C,T,q,z,Y?u:null],queryFn:async()=>{if(!i||!o||!m||!u)return console.log("Missing required auth parameters"),{data:[],total:0,page:1,page_size:N,total_pages:0};let e=te()(C).utc().format("YYYY-MM-DD HH:mm:ss"),s=E?te()(T).utc().format("YYYY-MM-DD HH:mm:ss"):te()().utc().format("YYYY-MM-DD HH:mm:ss"),l=await (0,v.h3)(i,z||void 0,q||void 0,void 0,e,s,b,N,Y?u:void 0);return await to(l.data,e,i,el),l.data=l.data.map(s=>{let l=el.getQueryData(["logDetails",s.request_id,e]);return(null==l?void 0:l.messages)&&(null==l?void 0:l.response)&&(s.messages=l.messages,s.response=l.response),s}),l},enabled:!!i&&!!o&&!!m&&!!u,refetchInterval:5e3,refetchIntervalInBackground:!0}),ea=(0,eV.a)({queryKey:["sessionLogs",ee],queryFn:async()=>{if(!i||!ee)return{data:[],total:0,page:1,page_size:50,total_pages:1};let e=await (0,v.XB)(i,ee);return{data:e.data||e||[],total:(e.data||e||[]).length,page:1,page_size:1e3,total_pages:1}},enabled:!!i&&!!ee});if((0,d.useEffect)(()=>{var e;(null===(e=et.data)||void 0===e?void 0:e.data)&&X&&!et.data.data.some(e=>e.request_id===X)&&Q(null)},[null===(s=et.data)||void 0===s?void 0:s.data,X]),!i||!o||!m||!u)return console.log("got None values for one of accessToken, token, userRole, userID"),null;let er=(null===(t=et.data)||void 0===t?void 0:null===(l=t.data)||void 0===l?void 0:l.filter(e=>!p||e.request_id.includes(p)||e.model.includes(p)||e.user&&e.user.includes(p)).map(e=>({...e,onKeyHashClick:e=>J(e),onSessionClick:e=>{e&&es(e)}})))||[],en=(null===(r=ea.data)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.map(e=>({...e,onKeyHashClick:e=>J(e),onSessionClick:e=>{}})))||[],ei=()=>{if(E)return"".concat(te()(C).format("MMM D, h:mm A")," - ").concat(te()(T).format("MMM D, h:mm A"));let e=te()(),s=te()(C),l=e.diff(s,"minutes");if(l<=15)return"Last 15 Minutes";if(l<=60)return"Last Hour";let t=e.diff(s,"hours");return t<=4?"Last 4 Hours":t<=24?"Last 24 Hours":t<=168?"Last 7 Days":"".concat(s.format("MMM D")," - ").concat(e.format("MMM D"))};return ee&&ea.data?(0,c.jsx)("div",{className:"w-full p-6",children:(0,c.jsx)(tu,{sessionId:ee,logs:ea.data.data,onBack:()=>es(null)})}):(0,c.jsxs)("div",{className:"w-full p-6",children:[(0,c.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,c.jsx)("h1",{className:"text-xl font-semibold",children:ee?(0,c.jsxs)(c.Fragment,{children:["Session: ",(0,c.jsx)("span",{className:"font-mono",children:ee}),(0,c.jsx)("button",{className:"ml-4 px-3 py-1 text-sm border rounded hover:bg-gray-50",onClick:()=>es(null),children:"← Back to All Logs"})]}):"Request Logs"})}),K&&H&&K.api_key===H?(0,c.jsx)(eq,{keyId:H,keyData:K,accessToken:i,userID:u,userRole:m,teams:x,onClose:()=>J(null)}):ee?(0,c.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,c.jsx)(ts,{columns:ti,data:en,renderSubComponent:tp,getRowCanExpand:()=>!0})}):(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsx)("div",{className:"border-b px-6 py-4",children:(0,c.jsxs)("div",{className:"flex flex-col md:flex-row items-start md:items-center justify-between space-y-4 md:space-y-0",children:[(0,c.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,c.jsxs)("div",{className:"relative w-64",children:[(0,c.jsx)("input",{type:"text",placeholder:"Search by Request ID",className:"w-full px-3 py-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:p,onChange:e=>g(e.target.value)}),(0,c.jsx)("svg",{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),(0,c.jsxs)("div",{className:"relative",ref:k,children:[(0,c.jsxs)("button",{className:"px-3 py-2 text-sm border rounded-md hover:bg-gray-50 flex items-center gap-2",onClick:()=>f(!j),children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})}),"Filter"]}),j&&(0,c.jsx)("div",{className:"absolute left-0 mt-2 w-[500px] bg-white rounded-lg shadow-lg border p-4 z-50",children:(0,c.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsx)("span",{className:"text-sm font-medium",children:"Where"}),(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsxs)("button",{onClick:()=>y(!_),className:"px-3 py-1.5 border rounded-md bg-white text-sm min-w-[160px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-left flex justify-between items-center",children:[W,(0,c.jsx)("svg",{className:"h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),_&&(0,c.jsx)("div",{className:"absolute left-0 mt-1 w-[160px] bg-white border rounded-md shadow-lg z-50",children:["Team ID","Key Hash"].map(e=>(0,c.jsxs)("button",{className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 ".concat(W===e?"bg-blue-50 text-blue-600":""),onClick:()=>{G(e),y(!1),"Team ID"===e?R(""):M("")},children:[W===e&&(0,c.jsx)("svg",{className:"h-4 w-4 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),e]},e))})]}),(0,c.jsx)("input",{type:"text",placeholder:"Enter value...",className:"px-3 py-1.5 border rounded-md text-sm flex-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:"Team ID"===W?D:F,onChange:e=>{"Team ID"===W?M(e.target.value):R(e.target.value)}}),(0,c.jsx)("button",{className:"p-1 hover:bg-gray-100 rounded-md",onClick:()=>{M(""),R("")},children:(0,c.jsx)("span",{className:"text-gray-500",children:"\xd7"})})]}),(0,c.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,c.jsx)("button",{className:"px-3 py-1.5 text-sm border rounded-md hover:bg-gray-50",onClick:()=>{M(""),R(""),f(!1)},children:"Cancel"}),(0,c.jsx)("button",{className:"px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700",onClick:()=>{U(D),V(F),Z(1),f(!1)},children:"Apply Filters"})]})]})})]}),(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsxs)("div",{className:"relative",ref:S,children:[(0,c.jsxs)("button",{onClick:()=>L(!O),className:"px-3 py-2 text-sm border rounded-md hover:bg-gray-50 flex items-center gap-2",children:[(0,c.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),ei()]}),O&&(0,c.jsx)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border p-2 z-50",children:(0,c.jsxs)("div",{className:"space-y-1",children:[[{label:"Last 15 Minutes",value:15,unit:"minutes"},{label:"Last Hour",value:1,unit:"hours"},{label:"Last 4 Hours",value:4,unit:"hours"},{label:"Last 24 Hours",value:24,unit:"hours"},{label:"Last 7 Days",value:7,unit:"days"}].map(e=>(0,c.jsx)("button",{className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 rounded-md ".concat(ei()===e.label?"bg-blue-50 text-blue-600":""),onClick:()=>{A(te()().format("YYYY-MM-DDTHH:mm")),I(te()().subtract(e.value,e.unit).format("YYYY-MM-DDTHH:mm")),L(!1),P(!1)},children:e.label},e.label)),(0,c.jsx)("div",{className:"border-t my-2"}),(0,c.jsx)("button",{className:"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 rounded-md ".concat(E?"bg-blue-50 text-blue-600":""),onClick:()=>P(!E),children:"Custom Range"})]})})]}),(0,c.jsxs)("button",{onClick:()=>{et.refetch()},className:"px-3 py-2 text-sm border rounded-md hover:bg-gray-50 flex items-center gap-2",title:"Refresh data",children:[(0,c.jsx)("svg",{className:"w-4 h-4 ".concat(et.isFetching?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,c.jsx)("span",{children:"Refresh"})]})]}),E&&(0,c.jsxs)("div",{className:"flex items-center gap-2",children:[(0,c.jsx)("div",{children:(0,c.jsx)("input",{type:"datetime-local",value:C,onChange:e=>{I(e.target.value),Z(1)},className:"px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})}),(0,c.jsx)("span",{className:"text-gray-500",children:"to"}),(0,c.jsx)("div",{children:(0,c.jsx)("input",{type:"datetime-local",value:T,onChange:e=>{A(e.target.value),Z(1)},className:"px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})})]})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsxs)("span",{className:"text-sm text-gray-700",children:["Showing"," ",et.isLoading?"...":et.data?(b-1)*N+1:0," ","-"," ",et.isLoading?"...":et.data?Math.min(b*N,et.data.total):0," ","of"," ",et.isLoading?"...":et.data?et.data.total:0," ","results"]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsxs)("span",{className:"text-sm text-gray-700",children:["Page ",et.isLoading?"...":b," of"," ",et.isLoading?"...":et.data?et.data.total_pages:1]}),(0,c.jsx)("button",{onClick:()=>Z(e=>Math.max(1,e-1)),disabled:et.isLoading||1===b,className:"px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,c.jsx)("button",{onClick:()=>Z(e=>{var s;return Math.min((null===(s=et.data)||void 0===s?void 0:s.total_pages)||1,e+1)}),disabled:et.isLoading||b===((null===(n=et.data)||void 0===n?void 0:n.total_pages)||1),className:"px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})]})}),(0,c.jsx)(ts,{columns:ti,data:er,renderSubComponent:tp,getRowCanExpand:()=>!0})]})})]})}function tp(e){var s,l,t,a;let{row:r}=e,n=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},i=()=>{var e;return(null===(e=r.original)||void 0===e?void 0:e.proxy_server_request)?n(r.original.proxy_server_request):n(r.original.messages)},o=r.original.metadata||{},d="failure"===o.status,m=d?o.error_information:null,u=r.original.messages&&(Array.isArray(r.original.messages)?r.original.messages.length>0:Object.keys(r.original.messages).length>0),x=r.original.response&&Object.keys(n(r.original.response)).length>0,h=()=>d&&m?{error:{message:m.error_message||"An error occurred",type:m.error_class||"error",code:m.error_code||"unknown",param:null}}:n(r.original.response),p=o.vector_store_request_metadata&&Array.isArray(o.vector_store_request_metadata)&&o.vector_store_request_metadata.length>0;return(0,c.jsxs)("div",{className:"p-6 bg-gray-50 space-y-6",children:[(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsx)("div",{className:"p-4 border-b",children:(0,c.jsx)("h3",{className:"text-lg font-medium",children:"Request Details"})}),(0,c.jsxs)("div",{className:"grid grid-cols-2 gap-4 p-4",children:[(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Request ID:"}),(0,c.jsx)("span",{className:"font-mono text-sm",children:r.original.request_id})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Model:"}),(0,c.jsx)("span",{children:r.original.model})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Model ID:"}),(0,c.jsx)("span",{children:r.original.model_id})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Provider:"}),(0,c.jsx)("span",{children:r.original.custom_llm_provider||"-"})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"API Base:"}),(0,c.jsx)(W.Z,{title:r.original.api_base||"-",children:(0,c.jsx)("span",{className:"max-w-[15ch] truncate block",children:r.original.api_base||"-"})})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Start Time:"}),(0,c.jsx)("span",{children:r.original.startTime})]})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Tokens:"}),(0,c.jsxs)("span",{children:[r.original.total_tokens," (",r.original.prompt_tokens,"+",r.original.completion_tokens,")"]})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Cost:"}),(0,c.jsxs)("span",{children:["$",Number(r.original.spend||0).toFixed(6)]})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Cache Hit:"}),(0,c.jsx)("span",{children:r.original.cache_hit})]}),(null==r?void 0:null===(s=r.original)||void 0===s?void 0:s.requester_ip_address)&&(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"IP Address:"}),(0,c.jsx)("span",{children:null==r?void 0:null===(l=r.original)||void 0===l?void 0:l.requester_ip_address})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"Status:"}),(0,c.jsx)("span",{className:"px-2 py-1 rounded-md text-xs font-medium inline-block text-center w-16 ".concat("failure"!==((null===(t=r.original.metadata)||void 0===t?void 0:t.status)||"Success").toLowerCase()?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"failure"!==((null===(a=r.original.metadata)||void 0===a?void 0:a.status)||"Success").toLowerCase()?"Success":"Failure"})]}),(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)("span",{className:"font-medium w-1/3",children:"End Time:"}),(0,c.jsx)("span",{children:r.original.endTime})]})]})]})]}),(0,c.jsx)(td,{show:!u&&!x}),(0,c.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,c.jsx)("h3",{className:"text-lg font-medium",children:"Request"}),(0,c.jsx)("button",{onClick:()=>navigator.clipboard.writeText(JSON.stringify(i(),null,2)),className:"p-1 hover:bg-gray-200 rounded",title:"Copy request",disabled:!u,children:(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,c.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,c.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})})]}),(0,c.jsx)("div",{className:"p-4 overflow-auto max-h-96",children:(0,c.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap break-all",children:JSON.stringify(i(),null,2)})})]}),(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,c.jsxs)("h3",{className:"text-lg font-medium",children:["Response",d&&(0,c.jsxs)("span",{className:"ml-2 text-sm text-red-600",children:["• HTTP code ",(null==m?void 0:m.error_code)||400]})]}),(0,c.jsx)("button",{onClick:()=>navigator.clipboard.writeText(JSON.stringify(h(),null,2)),className:"p-1 hover:bg-gray-200 rounded",title:"Copy response",disabled:!x,children:(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,c.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,c.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})})]}),(0,c.jsx)("div",{className:"p-4 overflow-auto max-h-96 bg-gray-50",children:x?(0,c.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap break-all",children:JSON.stringify(h(),null,2)}):(0,c.jsx)("div",{className:"text-gray-500 text-sm italic text-center py-4",children:"Response data not available"})})]})]}),p&&(0,c.jsx)(tx,{data:o.vector_store_request_metadata}),d&&m&&(0,c.jsx)(tc,{errorInfo:m}),r.original.request_tags&&Object.keys(r.original.request_tags).length>0&&(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsx)("div",{className:"flex justify-between items-center p-4 border-b",children:(0,c.jsx)("h3",{className:"text-lg font-medium",children:"Request Tags"})}),(0,c.jsx)("div",{className:"p-4",children:(0,c.jsx)("div",{className:"flex flex-wrap gap-2",children:Object.entries(r.original.request_tags).map(e=>{let[s,l]=e;return(0,c.jsxs)("span",{className:"px-2 py-1 bg-gray-100 rounded-full text-xs",children:[s,": ",String(l)]},s)})})})]}),r.original.metadata&&Object.keys(r.original.metadata).length>0&&(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center p-4 border-b",children:[(0,c.jsx)("h3",{className:"text-lg font-medium",children:"Metadata"}),(0,c.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(JSON.stringify(r.original.metadata,null,2))},className:"p-1 hover:bg-gray-200 rounded",title:"Copy metadata",children:(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,c.jsx)("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),(0,c.jsx)("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})]})})]}),(0,c.jsx)("div",{className:"p-4 overflow-auto max-h-64",children:(0,c.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap break-all",children:JSON.stringify(r.original.metadata,null,2)})})]})]})}var tg=l(92699),tj=l(14042);console.log=function(){};var tf=e=>{let{userID:s,userRole:l,accessToken:t,userSpend:a,userMaxBudget:r,selectedTeam:n}=e;console.log("userSpend: ".concat(a));let[i,o]=(0,d.useState)(null!==a?a:0),[m,u]=(0,d.useState)(n?n.max_budget:null);(0,d.useEffect)(()=>{if(n){if("Default Team"===n.team_alias)u(r);else{let e=!1;if(n.team_memberships)for(let l of n.team_memberships)l.user_id===s&&"max_budget"in l.litellm_budget_table&&null!==l.litellm_budget_table.max_budget&&(u(l.litellm_budget_table.max_budget),e=!0);e||u(n.max_budget)}}},[n,r]);let[x,h]=(0,d.useState)([]);(0,d.useEffect)(()=>{let e=async()=>{if(!t||!s||!l)return};(async()=>{try{if(null===s||null===l)return;if(null!==t){let e=(await (0,v.So)(t,s,l)).data.map(e=>e.id);console.log("available_model_names:",e),h(e)}}catch(e){console.error("Error fetching user models:",e)}})(),e()},[l,t,s]),(0,d.useEffect)(()=>{null!==a&&o(a)},[a]);let p=[];n&&n.models&&(p=n.models),p&&p.includes("all-proxy-models")?(console.log("user models:",x),p=x):p&&p.includes("all-team-models")?p=n.models:p&&0===p.length&&(p=x);let g=void 0!==i?i.toFixed(4):null;return console.log("spend in view user spend: ".concat(i)),(0,c.jsx)("div",{className:"flex items-center",children:(0,c.jsxs)("div",{className:"flex justify-between gap-x-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"text-tremor-default text-tremor-content dark:text-dark-tremor-content",children:"Total Spend"}),(0,c.jsxs)("p",{className:"text-2xl text-tremor-content-strong dark:text-dark-tremor-content-strong font-semibold",children:["$",g]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{className:"text-tremor-default text-tremor-content dark:text-dark-tremor-content",children:"Max Budget"}),(0,c.jsx)("p",{className:"text-2xl text-tremor-content-strong dark:text-dark-tremor-content-strong font-semibold",children:null!==m?"$".concat(m," limit"):"No limit"})]})]})})};let t_=e=>{let{key:s,info:l}=e;return{token:s,...l}};var tv=e=>{let{topKeys:s,accessToken:l,userID:t,userRole:a,teams:r}=e,[n,i]=(0,d.useState)(!1),[o,m]=(0,d.useState)(null),[u,x]=(0,d.useState)(void 0),[h,p]=(0,d.useState)("table"),g=async e=>{if(l)try{let s=await (0,v.t0)(l,e.api_key),t=t_(s);x(t),m(e.api_key),i(!0)}catch(e){console.error("Error fetching key info:",e)}},j=()=>{i(!1),m(null),x(void 0)};return d.useEffect(()=>{let e=e=>{"Escape"===e.key&&n&&j()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[n]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"mb-4 flex justify-end items-center",children:(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsx)("button",{onClick:()=>p("table"),className:"px-3 py-1 text-sm rounded-md ".concat("table"===h?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"),children:"Table View"}),(0,c.jsx)("button",{onClick:()=>p("chart"),className:"px-3 py-1 text-sm rounded-md ".concat("chart"===h?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700"),children:"Chart View"})]})}),"chart"===h?(0,c.jsx)("div",{className:"relative",children:(0,c.jsx)(sZ.Z,{className:"mt-4 h-40 cursor-pointer hover:opacity-90",data:s,index:"key_alias",categories:["spend"],colors:["cyan"],yAxisWidth:80,tickGap:5,layout:"vertical",showXAxis:!1,showLegend:!1,valueFormatter:e=>e?"$".concat(e.toFixed(2)):"No Key Alias",onValueChange:e=>g(e),showTooltip:!0,customTooltip:e=>{var s,l,t;let a=null===(l=e.payload)||void 0===l?void 0:null===(s=l[0])||void 0===s?void 0:s.payload;return(0,c.jsx)("div",{className:"p-3 bg-black/90 shadow-lg rounded-lg text-white",children:(0,c.jsxs)("div",{className:"space-y-1.5",children:[(0,c.jsxs)("div",{className:"text-sm",children:[(0,c.jsx)("span",{className:"text-gray-300",children:"Key: "}),(0,c.jsxs)("span",{className:"font-mono text-gray-100",children:[null==a?void 0:null===(t=a.api_key)||void 0===t?void 0:t.slice(0,10),"..."]})]}),(0,c.jsxs)("div",{className:"text-sm",children:[(0,c.jsx)("span",{className:"text-gray-300",children:"Spend: "}),(0,c.jsxs)("span",{className:"text-white font-medium",children:["$",null==a?void 0:a.spend.toFixed(2)]})]})]})})}})}):(0,c.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,c.jsx)(ts,{columns:[{header:"Key ID",accessorKey:"api_key",cell:e=>(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsx)(W.Z,{title:e.getValue(),children:(0,c.jsx)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left overflow-hidden truncate max-w-[200px]",onClick:()=>g(e.row.original),children:e.getValue()?"".concat(e.getValue().slice(0,7),"..."):"-"})})})},{header:"Key Alias",accessorKey:"key_alias",cell:e=>e.getValue()||"-"},{header:"Spend (USD)",accessorKey:"spend",cell:e=>"$".concat(Number(e.getValue()).toFixed(2))}],data:s,renderSubComponent:()=>(0,c.jsx)(c.Fragment,{}),getRowCanExpand:()=>!1,isLoading:!1})}),n&&o&&u&&(console.log("Rendering modal with:",{isModalOpen:n,selectedKey:o,keyData:u}),(0,c.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:e=>{e.target===e.currentTarget&&j()},children:(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow-xl relative w-11/12 max-w-6xl max-h-[90vh] overflow-y-auto min-h-[750px]",children:[(0,c.jsx)("button",{onClick:j,className:"absolute top-4 right-4 text-gray-500 hover:text-gray-700 focus:outline-none","aria-label":"Close",children:(0,c.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})}),(0,c.jsx)("div",{className:"p-6 h-full",children:(0,c.jsx)(eq,{keyId:o,onClose:j,keyData:u,accessToken:l,userID:t,userRole:a,teams:r})})]})}))]})},ty=l(44851);let tb=e=>{var s,l;let{modelName:t,metrics:a}=e;return(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)(w.Z,{numItems:4,className:"gap-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Requests"}),(0,c.jsx)(E.Z,{children:a.total_requests.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Successful Requests"}),(0,c.jsx)(E.Z,{children:a.total_successful_requests.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Tokens"}),(0,c.jsx)(E.Z,{children:a.total_tokens.toLocaleString()}),(0,c.jsxs)(A.Z,{children:[Math.round(a.total_tokens/a.total_successful_requests)," avg per successful request"]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Spend"}),(0,c.jsxs)(E.Z,{children:["$",a.total_spend.toFixed(2)]}),(0,c.jsxs)(A.Z,{children:["$",(a.total_spend/a.total_successful_requests).toFixed(3)," per successful request"]})]})]}),(0,c.jsxs)(w.Z,{numItems:2,className:"gap-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Tokens"}),(0,c.jsx)(sb.Z,{data:a.daily_data,index:"date",categories:["metrics.prompt_tokens","metrics.completion_tokens","metrics.total_tokens"],colors:["blue","cyan","indigo"],valueFormatter:e=>e.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Requests per day"}),(0,c.jsx)(sZ.Z,{data:a.daily_data,index:"date",categories:["metrics.api_requests"],colors:["blue"],valueFormatter:e=>e.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Spend per day"}),(0,c.jsx)(sZ.Z,{data:a.daily_data,index:"date",categories:["metrics.spend"],colors:["green"],valueFormatter:e=>"$".concat(e.toFixed(2))})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Success vs Failed Requests"}),(0,c.jsx)(sb.Z,{data:a.daily_data,index:"date",categories:["metrics.successful_requests","metrics.failed_requests"],colors:["emerald","red"],valueFormatter:e=>e.toLocaleString(),stack:!0})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Prompt Caching Metrics"}),(0,c.jsxs)("div",{className:"mb-2",children:[(0,c.jsxs)(A.Z,{children:["Cache Read: ",(null===(s=a.total_cache_read_input_tokens)||void 0===s?void 0:s.toLocaleString())||0," tokens"]}),(0,c.jsxs)(A.Z,{children:["Cache Creation: ",(null===(l=a.total_cache_creation_input_tokens)||void 0===l?void 0:l.toLocaleString())||0," tokens"]})]}),(0,c.jsx)(sb.Z,{data:a.daily_data,index:"date",categories:["metrics.cache_read_input_tokens","metrics.cache_creation_input_tokens"],colors:["cyan","purple"],valueFormatter:e=>e.toLocaleString()})]})]})]})},tZ=e=>{let{modelMetrics:s}=e,l=Object.keys(s).sort((e,l)=>""===e?1:""===l?-1:s[l].total_spend-s[e].total_spend),t={total_requests:0,total_successful_requests:0,total_tokens:0,total_spend:0,total_cache_read_input_tokens:0,total_cache_creation_input_tokens:0,daily_data:{}};Object.values(s).forEach(e=>{t.total_requests+=e.total_requests,t.total_successful_requests+=e.total_successful_requests,t.total_tokens+=e.total_tokens,t.total_spend+=e.total_spend,t.total_cache_read_input_tokens+=e.total_cache_read_input_tokens||0,t.total_cache_creation_input_tokens+=e.total_cache_creation_input_tokens||0,e.daily_data.forEach(e=>{t.daily_data[e.date]||(t.daily_data[e.date]={prompt_tokens:0,completion_tokens:0,total_tokens:0,api_requests:0,spend:0,successful_requests:0,failed_requests:0,cache_read_input_tokens:0,cache_creation_input_tokens:0}),t.daily_data[e.date].prompt_tokens+=e.metrics.prompt_tokens,t.daily_data[e.date].completion_tokens+=e.metrics.completion_tokens,t.daily_data[e.date].total_tokens+=e.metrics.total_tokens,t.daily_data[e.date].api_requests+=e.metrics.api_requests,t.daily_data[e.date].spend+=e.metrics.spend,t.daily_data[e.date].successful_requests+=e.metrics.successful_requests,t.daily_data[e.date].failed_requests+=e.metrics.failed_requests,t.daily_data[e.date].cache_read_input_tokens+=e.metrics.cache_read_input_tokens||0,t.daily_data[e.date].cache_creation_input_tokens+=e.metrics.cache_creation_input_tokens||0})});let a=Object.entries(t.daily_data).map(e=>{let[s,l]=e;return{date:s,metrics:l}}).sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime());return(0,c.jsxs)("div",{className:"space-y-8",children:[(0,c.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,c.jsx)(E.Z,{children:"Overall Usage"}),(0,c.jsxs)(w.Z,{numItems:4,className:"gap-4 mb-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Requests"}),(0,c.jsx)(E.Z,{children:t.total_requests.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Successful Requests"}),(0,c.jsx)(E.Z,{children:t.total_successful_requests.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Tokens"}),(0,c.jsx)(E.Z,{children:t.total_tokens.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(A.Z,{children:"Total Spend"}),(0,c.jsxs)(E.Z,{children:["$",t.total_spend.toFixed(2)]})]})]}),(0,c.jsxs)(w.Z,{numItems:2,className:"gap-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Tokens Over Time"}),(0,c.jsx)(sb.Z,{data:a,index:"date",categories:["metrics.prompt_tokens","metrics.completion_tokens","metrics.total_tokens"],colors:["blue","cyan","indigo"],valueFormatter:e=>e.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Requests Over Time"}),(0,c.jsx)(sb.Z,{data:a,index:"date",categories:["metrics.successful_requests","metrics.failed_requests"],colors:["emerald","red"],valueFormatter:e=>e.toLocaleString(),stack:!0})]})]})]}),(0,c.jsx)(ty.Z,{defaultActiveKey:l[0],children:l.map(e=>(0,c.jsx)(ty.Z.Panel,{header:(0,c.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,c.jsx)(E.Z,{children:s[e].label||"Unknown Item"}),(0,c.jsxs)("div",{className:"flex space-x-4 text-sm text-gray-500",children:[(0,c.jsxs)("span",{children:["$",s[e].total_spend.toFixed(2)]}),(0,c.jsxs)("span",{children:[s[e].total_requests.toLocaleString()," requests"]})]})]}),children:(0,c.jsx)(tb,{modelName:e||"Unknown Model",metrics:s[e]})},e))})]})},tN=(e,s)=>{let l=e.metadata.key_alias||"key-hash-".concat(s),t=e.metadata.team_id;return t?"".concat(l," (team_id: ").concat(t,")"):l},tw=(e,s)=>{let l={};return e.results.forEach(e=>{Object.entries(e.breakdown[s]||{}).forEach(t=>{let[a,r]=t;l[a]||(l[a]={label:"api_keys"===s?tN(r,a):a,total_requests:0,total_successful_requests:0,total_failed_requests:0,total_tokens:0,prompt_tokens:0,completion_tokens:0,total_spend:0,total_cache_read_input_tokens:0,total_cache_creation_input_tokens:0,daily_data:[]}),l[a].total_requests+=r.metrics.api_requests,l[a].prompt_tokens+=r.metrics.prompt_tokens,l[a].completion_tokens+=r.metrics.completion_tokens,l[a].total_tokens+=r.metrics.total_tokens,l[a].total_spend+=r.metrics.spend,l[a].total_successful_requests+=r.metrics.successful_requests,l[a].total_failed_requests+=r.metrics.failed_requests,l[a].total_cache_read_input_tokens+=r.metrics.cache_read_input_tokens||0,l[a].total_cache_creation_input_tokens+=r.metrics.cache_creation_input_tokens||0,l[a].daily_data.push({date:e.date,metrics:{prompt_tokens:r.metrics.prompt_tokens,completion_tokens:r.metrics.completion_tokens,total_tokens:r.metrics.total_tokens,api_requests:r.metrics.api_requests,spend:r.metrics.spend,successful_requests:r.metrics.successful_requests,failed_requests:r.metrics.failed_requests,cache_read_input_tokens:r.metrics.cache_read_input_tokens||0,cache_creation_input_tokens:r.metrics.cache_creation_input_tokens||0}})})}),Object.values(l).forEach(e=>{e.daily_data.sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime())}),l};var tk=e=>{let{accessToken:s,entityType:l,entityId:t,userID:a,userRole:r,entityList:n}=e,[i,o]=(0,d.useState)({results:[],metadata:{total_spend:0,total_api_requests:0,total_successful_requests:0,total_failed_requests:0,total_tokens:0}}),m=tw(i,"models"),u=tw(i,"api_keys"),[x,h]=(0,d.useState)([]),[p,g]=(0,d.useState)({from:new Date(Date.now()-24192e5),to:new Date}),j=async()=>{if(!s||!p.from||!p.to)return;let e=p.from,t=p.to;if("tag"===l)o(await (0,v.Z9)(s,e,t,1,x.length>0?x:null));else if("team"===l)o(await (0,v.ol)(s,e,t,1,x.length>0?x:null));else throw Error("Invalid entity type")};(0,d.useEffect)(()=>{j()},[s,p,t,x]);let f=()=>{let e={};return i.results.forEach(s=>{Object.entries(s.breakdown.providers||{}).forEach(s=>{let[l,t]=s;e[l]||(e[l]={provider:l,spend:0,requests:0,successful_requests:0,failed_requests:0,tokens:0});try{e[l].spend+=t.metrics.spend,e[l].requests+=t.metrics.api_requests,e[l].successful_requests+=t.metrics.successful_requests,e[l].failed_requests+=t.metrics.failed_requests,e[l].tokens+=t.metrics.total_tokens}catch(e){console.log("Error processing provider ".concat(l,": ").concat(e))}})}),Object.values(e).filter(e=>e.spend>0).sort((e,s)=>s.spend-e.spend)},_=e=>0===x.length?e:e.filter(e=>x.includes(e.metadata.id)),y=()=>{let e={};return i.results.forEach(s=>{Object.entries(s.breakdown.entities||{}).forEach(s=>{let[l,t]=s;e[l]||(e[l]={metrics:{spend:0,prompt_tokens:0,completion_tokens:0,total_tokens:0,api_requests:0,successful_requests:0,failed_requests:0,cache_read_input_tokens:0,cache_creation_input_tokens:0},metadata:{alias:t.metadata.team_alias||l,id:l}}),e[l].metrics.spend+=t.metrics.spend,e[l].metrics.api_requests+=t.metrics.api_requests,e[l].metrics.successful_requests+=t.metrics.successful_requests,e[l].metrics.failed_requests+=t.metrics.failed_requests,e[l].metrics.total_tokens+=t.metrics.total_tokens})}),_(Object.values(e).sort((e,s)=>s.metrics.spend-e.metrics.spend))};return(0,c.jsxs)("div",{style:{width:"100%"},children:[(0,c.jsxs)(w.Z,{numItems:2,className:"gap-2 w-full mb-4",children:[(0,c.jsxs)(N.Z,{children:[(0,c.jsx)(A.Z,{children:"Select Time Range"}),(0,c.jsx)(sv.Z,{enableSelect:!0,value:p,onValueChange:g})]}),n&&n.length>0&&(0,c.jsxs)(N.Z,{children:[(0,c.jsxs)(A.Z,{children:["Filter by ","tag"===l?"Tags":"Teams"]}),(0,c.jsx)(O.default,{mode:"multiple",style:{width:"100%"},placeholder:"Select ".concat("tag"===l?"tags":"teams"," to filter..."),value:x,onChange:h,options:(()=>{if(n)return n})(),className:"mt-2",allowClear:!0})]})]}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{variant:"solid",className:"mt-1",children:[(0,c.jsx)(eT.Z,{children:"Cost"}),(0,c.jsx)(eT.Z,{children:"Model Activity"}),(0,c.jsx)(eT.Z,{children:"Key Activity"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:2,className:"gap-2 w-full",children:[(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)(E.Z,{children:["tag"===l?"Tag":"Team"," Spend Overview"]}),(0,c.jsxs)(w.Z,{numItems:5,className:"gap-4 mt-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Spend"}),(0,c.jsxs)(A.Z,{className:"text-2xl font-bold mt-2",children:["$",i.metadata.total_spend.toFixed(2)]})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Requests"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2",children:i.metadata.total_api_requests.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Successful Requests"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2 text-green-600",children:i.metadata.total_successful_requests.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Failed Requests"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2 text-red-600",children:i.metadata.total_failed_requests.toLocaleString()})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Tokens"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2",children:i.metadata.total_tokens.toLocaleString()})]})]})]})}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Daily Spend"}),(0,c.jsx)(sZ.Z,{data:[...i.results].sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime()),index:"date",categories:["metrics.spend"],colors:["cyan"],valueFormatter:e=>"$".concat(e.toFixed(2)),yAxisWidth:100,showLegend:!1,customTooltip:e=>{let{payload:s,active:t}=e;if(!t||!(null==s?void 0:s[0]))return null;let a=s[0].payload;return(0,c.jsxs)("div",{className:"bg-white p-4 shadow-lg rounded-lg border",children:[(0,c.jsx)("p",{className:"font-bold",children:a.date}),(0,c.jsxs)("p",{className:"text-cyan-500",children:["Total Spend: $",a.metrics.spend.toFixed(2)]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Total Requests: ",a.metrics.api_requests]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Successful: ",a.metrics.successful_requests]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Failed: ",a.metrics.failed_requests]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Total Tokens: ",a.metrics.total_tokens]}),(0,c.jsxs)("div",{className:"mt-2 border-t pt-2",children:[(0,c.jsxs)("p",{className:"font-semibold",children:["Spend by ","tag"===l?"Tag":"Team",":"]}),Object.entries(a.breakdown.entities||{}).map(e=>{let[s,l]=e;return(0,c.jsxs)("p",{className:"text-sm text-gray-600",children:[l.metadata.team_alias||s,": $",l.metrics.spend.toFixed(2)]},s)})]})]})}})]})}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsx)(eI.Z,{children:(0,c.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,c.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,c.jsxs)(E.Z,{children:["Spend Per ","tag"===l?"Tag":"Team"]}),(0,c.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,c.jsxs)("span",{children:["Get Started Tracking cost per ",l," "]}),(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/tags",className:"text-blue-500 hover:text-blue-700 ml-1",children:"here"})]})]}),(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(sZ.Z,{className:"mt-4 h-52",data:y(),index:"metadata.alias",categories:["metrics.spend"],colors:["cyan"],valueFormatter:e=>"$".concat(e.toFixed(4)),layout:"vertical",showLegend:!1,yAxisWidth:100})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"tag"===l?"Tag":"Team"}),(0,c.jsx)(e1.Z,{children:"Spend"}),(0,c.jsx)(e1.Z,{className:"text-green-600",children:"Successful"}),(0,c.jsx)(e1.Z,{className:"text-red-600",children:"Failed"}),(0,c.jsx)(e1.Z,{children:"Tokens"})]})}),(0,c.jsx)(eX.Z,{children:y().filter(e=>e.metrics.spend>0).map(e=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.metadata.alias}),(0,c.jsxs)(eQ.Z,{children:["$",e.metrics.spend.toFixed(4)]}),(0,c.jsx)(eQ.Z,{className:"text-green-600",children:e.metrics.successful_requests.toLocaleString()}),(0,c.jsx)(eQ.Z,{className:"text-red-600",children:e.metrics.failed_requests.toLocaleString()}),(0,c.jsx)(eQ.Z,{children:e.metrics.total_tokens.toLocaleString()})]},e.metadata.id))})]})})]})]})})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Top API Keys"}),(0,c.jsx)(tv,{topKeys:(()=>{let e={};return i.results.forEach(s=>{Object.entries(s.breakdown.api_keys||{}).forEach(s=>{let[l,t]=s;e[l]||(e[l]={metrics:{spend:0,prompt_tokens:0,completion_tokens:0,total_tokens:0,api_requests:0,successful_requests:0,failed_requests:0,cache_read_input_tokens:0,cache_creation_input_tokens:0},metadata:{key_alias:t.metadata.key_alias}}),e[l].metrics.spend+=t.metrics.spend,e[l].metrics.prompt_tokens+=t.metrics.prompt_tokens,e[l].metrics.completion_tokens+=t.metrics.completion_tokens,e[l].metrics.total_tokens+=t.metrics.total_tokens,e[l].metrics.api_requests+=t.metrics.api_requests,e[l].metrics.successful_requests+=t.metrics.successful_requests,e[l].metrics.failed_requests+=t.metrics.failed_requests,e[l].metrics.cache_read_input_tokens+=t.metrics.cache_read_input_tokens||0,e[l].metrics.cache_creation_input_tokens+=t.metrics.cache_creation_input_tokens||0})}),Object.entries(e).map(e=>{let[s,l]=e;return{api_key:s,key_alias:l.metadata.key_alias||"-",spend:l.metrics.spend}}).sort((e,s)=>s.spend-e.spend).slice(0,5)})(),accessToken:s,userID:a,userRole:r,teams:null})]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Top Models"}),(0,c.jsx)(sZ.Z,{className:"mt-4 h-40",data:(()=>{let e={};return i.results.forEach(s=>{Object.entries(s.breakdown.models||{}).forEach(s=>{let[l,t]=s;e[l]||(e[l]={spend:0,requests:0,successful_requests:0,failed_requests:0,tokens:0});try{e[l].spend+=t.metrics.spend}catch(e){console.log("Error adding spend for ".concat(l,": ").concat(e,", got metrics: ").concat(JSON.stringify(t)))}e[l].requests+=t.metrics.api_requests,e[l].successful_requests+=t.metrics.successful_requests,e[l].failed_requests+=t.metrics.failed_requests,e[l].tokens+=t.metrics.total_tokens})}),Object.entries(e).map(e=>{let[s,l]=e;return{key:s,...l}}).sort((e,s)=>s.spend-e.spend).slice(0,5)})(),index:"key",categories:["spend"],colors:["cyan"],valueFormatter:e=>"$".concat(e.toFixed(2)),layout:"vertical",yAxisWidth:200,showLegend:!1})]})}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsx)(eI.Z,{children:(0,c.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,c.jsx)(E.Z,{children:"Provider Usage"}),(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(tj.Z,{className:"mt-4 h-40",data:f(),index:"provider",category:"spend",valueFormatter:e=>"$".concat(e.toFixed(2)),colors:["cyan","blue","indigo","violet","purple"]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Provider"}),(0,c.jsx)(e1.Z,{children:"Spend"}),(0,c.jsx)(e1.Z,{className:"text-green-600",children:"Successful"}),(0,c.jsx)(e1.Z,{className:"text-red-600",children:"Failed"}),(0,c.jsx)(e1.Z,{children:"Tokens"})]})}),(0,c.jsx)(eX.Z,{children:f().map(e=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.provider}),(0,c.jsxs)(eQ.Z,{children:["$",e.spend.toFixed(2)]}),(0,c.jsx)(eQ.Z,{className:"text-green-600",children:e.successful_requests.toLocaleString()}),(0,c.jsx)(eQ.Z,{className:"text-red-600",children:e.failed_requests.toLocaleString()}),(0,c.jsx)(eQ.Z,{children:e.tokens.toLocaleString()})]},e.provider))})]})})]})]})})})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(tZ,{modelMetrics:m})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(tZ,{modelMetrics:u})})]})]})]})},tS=e=>{var s,l,t,a,r,n,i,o,m,u;let{accessToken:x,userRole:h,userID:p,teams:g}=e,[j,f]=(0,d.useState)({results:[],metadata:{}}),[_,y]=(0,d.useState)({from:new Date(Date.now()-24192e5),to:new Date}),[b,Z]=(0,d.useState)([]),k=async()=>{x&&Z(Object.values(await (0,v.UM)(x)).map(e=>({label:e.name,value:e.name})))};(0,d.useEffect)(()=>{k()},[x]);let S=(null===(s=j.metadata)||void 0===s?void 0:s.total_spend)||0,C=()=>{let e={};return j.results.forEach(s=>{Object.entries(s.breakdown.providers||{}).forEach(s=>{let[l,t]=s;e[l]||(e[l]={metrics:{spend:0,prompt_tokens:0,completion_tokens:0,total_tokens:0,api_requests:0,successful_requests:0,failed_requests:0,cache_read_input_tokens:0,cache_creation_input_tokens:0},metadata:{}}),e[l].metrics.spend+=t.metrics.spend,e[l].metrics.prompt_tokens+=t.metrics.prompt_tokens,e[l].metrics.completion_tokens+=t.metrics.completion_tokens,e[l].metrics.total_tokens+=t.metrics.total_tokens,e[l].metrics.api_requests+=t.metrics.api_requests,e[l].metrics.successful_requests+=t.metrics.successful_requests||0,e[l].metrics.failed_requests+=t.metrics.failed_requests||0,e[l].metrics.cache_read_input_tokens+=t.metrics.cache_read_input_tokens||0,e[l].metrics.cache_creation_input_tokens+=t.metrics.cache_creation_input_tokens||0})}),Object.entries(e).map(e=>{let[s,l]=e;return{provider:s,spend:l.metrics.spend,requests:l.metrics.api_requests,successful_requests:l.metrics.successful_requests,failed_requests:l.metrics.failed_requests,tokens:l.metrics.total_tokens}})},I=async()=>{if(!x||!_.from||!_.to)return;let e=_.from,s=_.to;try{let l=await (0,v.xX)(x,e,s);if(l.metadata.total_pages>10)throw Error("Too many pages of data (>10). Please select a smaller date range.");if(1===l.metadata.total_pages){f(l);return}let t=[...l.results];for(let a=2;a<=l.metadata.total_pages;a++){let l=await (0,v.xX)(x,e,s,a);t.push(...l.results)}f({results:t,metadata:l.metadata})}catch(e){throw console.error("Error fetching user spend data:",e),e}};(0,d.useEffect)(()=>{I()},[x,_]);let T=tw(j,"models"),P=tw(j,"api_keys");return(0,c.jsxs)("div",{style:{width:"100%"},className:"p-8",children:[(0,c.jsxs)(A.Z,{className:"text-sm text-gray-500 mb-4",children:["This is the new usage dashboard. ",(0,c.jsx)("br",{})," You may see empty data, as these use ",(0,c.jsx)("a",{href:"https://github.com/BerriAI/litellm/blob/6de348125208dd4be81ff0e5813753df2fbe9735/schema.prisma#L320",className:"text-blue-500 hover:text-blue-700 ml-1",children:"new aggregate tables"})," to allow UI to work at 1M+ spend logs. To access the old dashboard, go to Experimental ",">"," Old Usage."]}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{variant:"solid",className:"mt-1",children:[eg.ZL.includes(h||"")?(0,c.jsx)(eT.Z,{children:"Global Usage"}):(0,c.jsx)(eT.Z,{children:"Your Usage"}),(0,c.jsx)(eT.Z,{children:"Team Usage"}),eg.ZL.includes(h||"")?(0,c.jsx)(eT.Z,{children:"Tag Usage"}):(0,c.jsx)(c.Fragment,{})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsxs)(eP.Z,{children:[(0,c.jsx)(w.Z,{numItems:2,className:"gap-2 w-full mb-4",children:(0,c.jsxs)(N.Z,{children:[(0,c.jsx)(A.Z,{children:"Select Time Range"}),(0,c.jsx)(sv.Z,{enableSelect:!0,value:_,onValueChange:e=>{y(e)}})]})}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{variant:"solid",className:"mt-1",children:[(0,c.jsx)(eT.Z,{children:"Cost"}),(0,c.jsx)(eT.Z,{children:"Model Activity"}),(0,c.jsx)(eT.Z,{children:"Key Activity"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:2,className:"gap-2 w-full",children:[(0,c.jsxs)(N.Z,{numColSpan:2,children:[(0,c.jsxs)(A.Z,{className:"text-tremor-default text-tremor-content dark:text-dark-tremor-content mb-2 mt-2 text-lg",children:["Project Spend ",new Date().toLocaleString("default",{month:"long"})," 1 - ",new Date(new Date().getFullYear(),new Date().getMonth()+1,0).getDate()]}),(0,c.jsx)(tf,{userID:p,userRole:h,accessToken:x,userSpend:S,selectedTeam:null,userMaxBudget:null})]}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Usage Metrics"}),(0,c.jsxs)(w.Z,{numItems:5,className:"gap-4 mt-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Requests"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2",children:(null===(t=j.metadata)||void 0===t?void 0:null===(l=t.total_api_requests)||void 0===l?void 0:l.toLocaleString())||0})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Successful Requests"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2 text-green-600",children:(null===(r=j.metadata)||void 0===r?void 0:null===(a=r.total_successful_requests)||void 0===a?void 0:a.toLocaleString())||0})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Failed Requests"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2 text-red-600",children:(null===(i=j.metadata)||void 0===i?void 0:null===(n=i.total_failed_requests)||void 0===n?void 0:n.toLocaleString())||0})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Total Tokens"}),(0,c.jsx)(A.Z,{className:"text-2xl font-bold mt-2",children:(null===(m=j.metadata)||void 0===m?void 0:null===(o=m.total_tokens)||void 0===o?void 0:o.toLocaleString())||0})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Average Cost per Request"}),(0,c.jsxs)(A.Z,{className:"text-2xl font-bold mt-2",children:["$",((S||0)/((null===(u=j.metadata)||void 0===u?void 0:u.total_api_requests)||1)).toFixed(4)]})]})]})]})}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Daily Spend"}),(0,c.jsx)(sZ.Z,{data:[...j.results].sort((e,s)=>new Date(e.date).getTime()-new Date(s.date).getTime()),index:"date",categories:["metrics.spend"],colors:["cyan"],valueFormatter:e=>"$".concat(e.toFixed(2)),yAxisWidth:100,showLegend:!1,customTooltip:e=>{let{payload:s,active:l}=e;if(!l||!(null==s?void 0:s[0]))return null;let t=s[0].payload;return(0,c.jsxs)("div",{className:"bg-white p-4 shadow-lg rounded-lg border",children:[(0,c.jsx)("p",{className:"font-bold",children:t.date}),(0,c.jsxs)("p",{className:"text-cyan-500",children:["Spend: $",t.metrics.spend.toFixed(2)]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Requests: ",t.metrics.api_requests]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Successful: ",t.metrics.successful_requests]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Failed: ",t.metrics.failed_requests]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Tokens: ",t.metrics.total_tokens]})]})}})]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(eI.Z,{className:"h-full",children:[(0,c.jsx)(E.Z,{children:"Top API Keys"}),(0,c.jsx)(tv,{topKeys:(()=>{let e={};return j.results.forEach(s=>{Object.entries(s.breakdown.api_keys||{}).forEach(s=>{let[l,t]=s;e[l]||(e[l]={metrics:{spend:0,prompt_tokens:0,completion_tokens:0,total_tokens:0,api_requests:0,successful_requests:0,failed_requests:0,cache_read_input_tokens:0,cache_creation_input_tokens:0},metadata:{key_alias:t.metadata.key_alias}}),e[l].metrics.spend+=t.metrics.spend,e[l].metrics.prompt_tokens+=t.metrics.prompt_tokens,e[l].metrics.completion_tokens+=t.metrics.completion_tokens,e[l].metrics.total_tokens+=t.metrics.total_tokens,e[l].metrics.api_requests+=t.metrics.api_requests,e[l].metrics.successful_requests+=t.metrics.successful_requests,e[l].metrics.failed_requests+=t.metrics.failed_requests,e[l].metrics.cache_read_input_tokens+=t.metrics.cache_read_input_tokens||0,e[l].metrics.cache_creation_input_tokens+=t.metrics.cache_creation_input_tokens||0})}),Object.entries(e).map(e=>{let[s,l]=e;return{api_key:s,key_alias:l.metadata.key_alias||"-",spend:l.metrics.spend}}).sort((e,s)=>s.spend-e.spend).slice(0,5)})(),accessToken:x,userID:p,userRole:h,teams:null})]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(eI.Z,{className:"h-full",children:[(0,c.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,c.jsx)(E.Z,{children:"Top Models"})}),(0,c.jsx)(sZ.Z,{className:"mt-4 h-40",data:(()=>{let e={};return j.results.forEach(s=>{Object.entries(s.breakdown.models||{}).forEach(s=>{let[l,t]=s;e[l]||(e[l]={metrics:{spend:0,prompt_tokens:0,completion_tokens:0,total_tokens:0,api_requests:0,successful_requests:0,failed_requests:0,cache_read_input_tokens:0,cache_creation_input_tokens:0},metadata:{}}),e[l].metrics.spend+=t.metrics.spend,e[l].metrics.prompt_tokens+=t.metrics.prompt_tokens,e[l].metrics.completion_tokens+=t.metrics.completion_tokens,e[l].metrics.total_tokens+=t.metrics.total_tokens,e[l].metrics.api_requests+=t.metrics.api_requests,e[l].metrics.successful_requests+=t.metrics.successful_requests||0,e[l].metrics.failed_requests+=t.metrics.failed_requests||0,e[l].metrics.cache_read_input_tokens+=t.metrics.cache_read_input_tokens||0,e[l].metrics.cache_creation_input_tokens+=t.metrics.cache_creation_input_tokens||0})}),Object.entries(e).map(e=>{let[s,l]=e;return{key:s,spend:l.metrics.spend,requests:l.metrics.api_requests,successful_requests:l.metrics.successful_requests,failed_requests:l.metrics.failed_requests,tokens:l.metrics.total_tokens}}).sort((e,s)=>s.spend-e.spend).slice(0,5)})(),index:"key",categories:["spend"],colors:["cyan"],valueFormatter:e=>"$".concat(e.toFixed(2)),layout:"vertical",yAxisWidth:200,showLegend:!1,customTooltip:e=>{let{payload:s,active:l}=e;if(!l||!(null==s?void 0:s[0]))return null;let t=s[0].payload;return(0,c.jsxs)("div",{className:"bg-white p-4 shadow-lg rounded-lg border",children:[(0,c.jsx)("p",{className:"font-bold",children:t.key}),(0,c.jsxs)("p",{className:"text-cyan-500",children:["Spend: $",t.spend.toFixed(2)]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Total Requests: ",t.requests.toLocaleString()]}),(0,c.jsxs)("p",{className:"text-green-600",children:["Successful: ",t.successful_requests.toLocaleString()]}),(0,c.jsxs)("p",{className:"text-red-600",children:["Failed: ",t.failed_requests.toLocaleString()]}),(0,c.jsxs)("p",{className:"text-gray-600",children:["Tokens: ",t.tokens.toLocaleString()]})]})}})]})}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{className:"h-full",children:[(0,c.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,c.jsx)(E.Z,{children:"Spend by Provider"})}),(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(tj.Z,{className:"mt-4 h-40",data:C(),index:"provider",category:"spend",valueFormatter:e=>"$".concat(e.toFixed(2)),colors:["cyan"]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Provider"}),(0,c.jsx)(e1.Z,{children:"Spend"}),(0,c.jsx)(e1.Z,{className:"text-green-600",children:"Successful"}),(0,c.jsx)(e1.Z,{className:"text-red-600",children:"Failed"}),(0,c.jsx)(e1.Z,{children:"Tokens"})]})}),(0,c.jsx)(eX.Z,{children:C().filter(e=>e.spend>0).map(e=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.provider}),(0,c.jsxs)(eQ.Z,{children:["$",e.spend<1e-5?"less than 0.00001":e.spend.toFixed(2)]}),(0,c.jsx)(eQ.Z,{className:"text-green-600",children:e.successful_requests.toLocaleString()}),(0,c.jsx)(eQ.Z,{className:"text-red-600",children:e.failed_requests.toLocaleString()}),(0,c.jsx)(eQ.Z,{children:e.tokens.toLocaleString()})]},e.provider))})]})})]})]})})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(tZ,{modelMetrics:T})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(tZ,{modelMetrics:P})})]})]})]}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(tk,{accessToken:x,entityType:"team",userID:p,userRole:h,entityList:(null==g?void 0:g.map(e=>({label:e.team_alias,value:e.team_id})))||null})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(tk,{accessToken:x,entityType:"tag",userID:p,userRole:h,entityList:b})})]})]})]})},tC=e=>{let{proxySettings:s}=e,l="<your_proxy_base_url>";return s&&s.PROXY_BASE_URL&&void 0!==s.PROXY_BASE_URL&&(l=s.PROXY_BASE_URL),(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(w.Z,{className:"gap-2 p-8 h-[80vh] w-full mt-2",children:(0,c.jsxs)("div",{className:"mb-5",children:[(0,c.jsx)("p",{className:"text-2xl text-tremor-content-strong dark:text-dark-tremor-content-strong font-semibold",children:"OpenAI Compatible Proxy: API Reference"}),(0,c.jsx)(A.Z,{className:"mt-2 mb-2",children:"LiteLLM is OpenAI Compatible. This means your API Key works with the OpenAI SDK. Just replace the base_url to point to your litellm proxy. Example Below "}),(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{children:[(0,c.jsx)(eT.Z,{children:"OpenAI Python SDK"}),(0,c.jsx)(eT.Z,{children:"LlamaIndex"}),(0,c.jsx)(eT.Z,{children:"Langchain Py"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsx)(l8.Z,{language:"python",children:'\nimport openai\nclient = openai.OpenAI(\n    api_key="your_api_key",\n    base_url="'.concat(l,'" # LiteLLM Proxy is OpenAI compatible, Read More: https://docs.litellm.ai/docs/proxy/user_keys\n)\n\nresponse = client.chat.completions.create(\n    model="gpt-3.5-turbo", # model to send to the proxy\n    messages = [\n        {\n            "role": "user",\n            "content": "this is a test request, write a short poem"\n        }\n    ]\n)\n\nprint(response)\n            ')})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(l8.Z,{language:"python",children:'\nimport os, dotenv\n\nfrom llama_index.llms import AzureOpenAI\nfrom llama_index.embeddings import AzureOpenAIEmbedding\nfrom llama_index import VectorStoreIndex, SimpleDirectoryReader, ServiceContext\n\nllm = AzureOpenAI(\n    engine="azure-gpt-3.5",               # model_name on litellm proxy\n    temperature=0.0,\n    azure_endpoint="'.concat(l,'", # litellm proxy endpoint\n    api_key="sk-1234",                    # litellm proxy API Key\n    api_version="2023-07-01-preview",\n)\n\nembed_model = AzureOpenAIEmbedding(\n    deployment_name="azure-embedding-model",\n    azure_endpoint="').concat(l,'",\n    api_key="sk-1234",\n    api_version="2023-07-01-preview",\n)\n\n\ndocuments = SimpleDirectoryReader("llama_index_data").load_data()\nservice_context = ServiceContext.from_defaults(llm=llm, embed_model=embed_model)\nindex = VectorStoreIndex.from_documents(documents, service_context=service_context)\n\nquery_engine = index.as_query_engine()\nresponse = query_engine.query("What did the author do growing up?")\nprint(response)\n\n            ')})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(l8.Z,{language:"python",children:'\nfrom langchain.chat_models import ChatOpenAI\nfrom langchain.prompts.chat import (\n    ChatPromptTemplate,\n    HumanMessagePromptTemplate,\n    SystemMessagePromptTemplate,\n)\nfrom langchain.schema import HumanMessage, SystemMessage\n\nchat = ChatOpenAI(\n    openai_api_base="'.concat(l,'",\n    model = "gpt-3.5-turbo",\n    temperature=0.1\n)\n\nmessages = [\n    SystemMessage(\n        content="You are a helpful assistant that im using to make a test request to."\n    ),\n    HumanMessage(\n        content="test from litellm. tell me why it\'s amazing in 1 sentence"\n    ),\n]\nresponse = chat(messages)\n\nprint(response)\n\n            ')})})]})]})]})})})},tI=l(243),tT=l(93837);async function tA(e,s,l,t,a,r,n,i,o,c,d){console.log=function(){},console.log("isLocal:",!1);let m=window.location.origin,u={};a&&a.length>0&&(u["x-litellm-tags"]=a.join(","));let x=new lG.ZP.OpenAI({apiKey:t,baseURL:m,dangerouslyAllowBrowser:!0,defaultHeaders:u});try{let t;let a=Date.now(),m=!1;for await(let u of(await x.chat.completions.create({model:l,stream:!0,stream_options:{include_usage:!0},litellm_trace_id:c,messages:e,...d?{vector_store_ids:d}:{}},{signal:r}))){var h,p,g,j,f,_,v,y;console.log("Stream chunk:",u);let e=null===(h=u.choices[0])||void 0===h?void 0:h.delta;if(console.log("Delta content:",null===(g=u.choices[0])||void 0===g?void 0:null===(p=g.delta)||void 0===p?void 0:p.content),console.log("Delta reasoning content:",null==e?void 0:e.reasoning_content),!m&&((null===(f=u.choices[0])||void 0===f?void 0:null===(j=f.delta)||void 0===j?void 0:j.content)||e&&e.reasoning_content)&&(m=!0,t=Date.now()-a,console.log("First token received! Time:",t,"ms"),i?(console.log("Calling onTimingData with:",t),i(t)):console.log("onTimingData callback is not defined!")),null===(v=u.choices[0])||void 0===v?void 0:null===(_=v.delta)||void 0===_?void 0:_.content){let e=u.choices[0].delta.content;s(e,u.model)}if(e&&e.reasoning_content){let s=e.reasoning_content;n&&n(s)}if(u.usage&&o){console.log("Usage data found:",u.usage);let e={completionTokens:u.usage.completion_tokens,promptTokens:u.usage.prompt_tokens,totalTokens:u.usage.total_tokens};(null===(y=u.usage.completion_tokens_details)||void 0===y?void 0:y.reasoning_tokens)&&(e.reasoningTokens=u.usage.completion_tokens_details.reasoning_tokens),o(e)}}}catch(e){throw(null==r?void 0:r.aborted)?console.log("Chat completion request was cancelled"):D.ZP.error("Error occurred while generating model response. Please try again. Error: ".concat(e),20),e}}async function tE(e,s,l,t,a,r){console.log=function(){},console.log("isLocal:",!1);let n=window.location.origin,i=new lG.ZP.OpenAI({apiKey:t,baseURL:n,dangerouslyAllowBrowser:!0,defaultHeaders:a&&a.length>0?{"x-litellm-tags":a.join(",")}:void 0});try{let t=await i.images.generate({model:l,prompt:e},{signal:r});if(console.log(t.data),t.data&&t.data[0]){if(t.data[0].url)s(t.data[0].url,l);else if(t.data[0].b64_json){let e=t.data[0].b64_json;s("data:image/png;base64,".concat(e),l)}else throw Error("No image data found in response")}else throw Error("Invalid response format")}catch(e){throw(null==r?void 0:r.aborted)?console.log("Image generation request was cancelled"):D.ZP.error("Error occurred while generating image. Please try again. Error: ".concat(e),20),e}}async function tP(e,s,l,t){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[],r=arguments.length>5?arguments[5]:void 0,n=arguments.length>6?arguments[6]:void 0,i=arguments.length>7?arguments[7]:void 0,o=arguments.length>8?arguments[8]:void 0,c=arguments.length>9?arguments[9]:void 0,d=arguments.length>10?arguments[10]:void 0;if(!t)throw Error("API key is required");console.log=function(){};let m=window.location.origin,u={};a&&a.length>0&&(u["x-litellm-tags"]=a.join(","));let x=new lG.ZP.OpenAI({apiKey:t,baseURL:m,dangerouslyAllowBrowser:!0,defaultHeaders:u});try{let t=Date.now(),a=!1,m=e.map(e=>({role:e.role,content:e.content,type:"message"}));for await(let e of(await x.responses.create({model:l,input:m,stream:!0,litellm_trace_id:c,...d?{vector_store_ids:d}:{}},{signal:r})))if(console.log("Response event:",e),"object"==typeof e&&null!==e){if("response.role.delta"===e.type)continue;if("response.output_text.delta"===e.type&&"string"==typeof e.delta){let r=e.delta;if(console.log("Text delta",r),r.trim().length>0&&(s("assistant",r,l),!a)){a=!0;let e=Date.now()-t;console.log("First token received! Time:",e,"ms"),i&&i(e)}}if("response.reasoning.delta"===e.type&&"delta"in e){let s=e.delta;"string"==typeof s&&n&&n(s)}if("response.completed"===e.type&&"response"in e){let s=e.response.usage;if(console.log("Usage data:",s),s&&o){var h;console.log("Usage data:",s);let e={completionTokens:s.output_tokens,promptTokens:s.input_tokens,totalTokens:s.total_tokens};(null===(h=s.completion_tokens_details)||void 0===h?void 0:h.reasoning_tokens)&&(e.reasoningTokens=s.completion_tokens_details.reasoning_tokens),o(e)}}}}catch(e){throw(null==r?void 0:r.aborted)?console.log("Responses API request was cancelled"):D.ZP.error("Error occurred while generating model response. Please try again. Error: ".concat(e),20),e}}let tO=async e=>{try{let s=await (0,v.kn)(e);if(console.log("model_info:",s),(null==s?void 0:s.data.length)>0){let e=s.data.map(e=>({model_group:e.model_group,mode:null==e?void 0:e.mode}));return e.sort((e,s)=>e.model_group.localeCompare(s.model_group)),e}return[]}catch(e){throw console.error("Error fetching model info:",e),e}};(a=i||(i={})).IMAGE_GENERATION="image_generation",a.CHAT="chat",a.RESPONSES="responses",(r=o||(o={})).IMAGE="image",r.CHAT="chat",r.RESPONSES="responses";let tL={image_generation:"image",chat:"chat",responses:"responses"},tD=e=>{if(console.log("getEndpointType:",e),Object.values(i).includes(e)){let s=tL[e];return console.log("endpointType:",s),s}return"chat"};var tM=l(94263),tF=e=>{let{endpointType:s,onEndpointChange:l,className:t}=e,a=[{value:o.CHAT,label:"/v1/chat/completions"},{value:o.RESPONSES,label:"/v1/responses"},{value:o.IMAGE,label:"/v1/images/generations"}];return(0,c.jsxs)("div",{className:t,children:[(0,c.jsx)(A.Z,{children:"Endpoint Type:"}),(0,c.jsx)(O.default,{value:s,style:{width:"100%"},onChange:l,options:a,className:"rounded-md"})]})},tR=e=>{let{onChange:s,value:l,className:t,accessToken:a}=e,[r,n]=(0,d.useState)([]),[i,o]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{(async()=>{if(a)try{let e=await (0,v.UM)(a);console.log("List tags response:",e),n(Object.values(e))}catch(e){console.error("Error fetching tags:",e)}})()},[]),(0,c.jsx)(O.default,{mode:"multiple",placeholder:"Select tags",onChange:s,value:l,loading:i,className:t,options:r.map(e=>({label:e.name,value:e.name,title:e.description||e.name})),optionFilterProp:"label",showSearch:!0,style:{width:"100%"}})},tq=e=>{let{onChange:s,value:l,className:t,accessToken:a}=e,[r,n]=(0,d.useState)([]),[i,o]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{(async()=>{if(a){o(!0);try{let e=await (0,v.Ou)(a);e.data&&n(e.data)}catch(e){console.error("Error fetching vector stores:",e)}finally{o(!1)}}})()},[a]),(0,c.jsx)("div",{children:(0,c.jsx)(O.default,{mode:"multiple",placeholder:"Select vector stores",onChange:s,value:l,loading:i,className:t,options:r.map(e=>({label:"".concat(e.vector_store_name||e.vector_store_id," (").concat(e.vector_store_id,")"),value:e.vector_store_id,title:e.vector_store_description||e.vector_store_id})),optionFilterProp:"label",showSearch:!0,style:{width:"100%"}})})};let tU=(e,s)=>{let l=s.find(s=>s.model_group===e);return(null==l?void 0:l.mode)?tD(l.mode):o.CHAT};var tz=l(83322),tV=l(70464),tK=l(77565),tB=e=>{let{reasoningContent:s}=e,[l,t]=(0,d.useState)(!0);return s?(0,c.jsxs)("div",{className:"reasoning-content mt-1 mb-2",children:[(0,c.jsxs)(R.ZP,{type:"text",className:"flex items-center text-xs text-gray-500 hover:text-gray-700",onClick:()=>t(!l),icon:(0,c.jsx)(tz.Z,{}),children:[l?"Hide reasoning":"Show reasoning",l?(0,c.jsx)(tV.Z,{className:"ml-1"}):(0,c.jsx)(tK.Z,{className:"ml-1"})]}),l&&(0,c.jsx)("div",{className:"mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md text-sm text-gray-700",children:(0,c.jsx)(tI.U,{components:{code(e){let{node:s,inline:l,className:t,children:a,...r}=e,n=/language-(\w+)/.exec(t||"");return!l&&n?(0,c.jsx)(l8.Z,{style:tM.Z,language:n[1],PreTag:"div",className:"rounded-md my-2",...r,children:String(a).replace(/\n$/,"")}):(0,c.jsx)("code",{className:"".concat(t," px-1.5 py-0.5 rounded bg-gray-100 text-sm font-mono"),...r,children:a})}},children:s})})]}):null},tH=l(5540),tJ=l(71282),tW=l(11741),tG=l(16601),tY=e=>{let{timeToFirstToken:s,usage:l}=e;return s||l?(0,c.jsxs)("div",{className:"response-metrics mt-2 pt-2 border-t border-gray-100 text-xs text-gray-500 flex flex-wrap gap-3",children:[void 0!==s&&(0,c.jsx)(W.Z,{title:"Time to first token",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(tH.Z,{className:"mr-1"}),(0,c.jsxs)("span",{children:[(s/1e3).toFixed(2),"s"]})]})}),(null==l?void 0:l.promptTokens)!==void 0&&(0,c.jsx)(W.Z,{title:"Prompt tokens",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(tJ.Z,{className:"mr-1"}),(0,c.jsxs)("span",{children:["In: ",l.promptTokens]})]})}),(null==l?void 0:l.completionTokens)!==void 0&&(0,c.jsx)(W.Z,{title:"Completion tokens",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(tW.Z,{className:"mr-1"}),(0,c.jsxs)("span",{children:["Out: ",l.completionTokens]})]})}),(null==l?void 0:l.reasoningTokens)!==void 0&&(0,c.jsx)(W.Z,{title:"Reasoning tokens",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(tz.Z,{className:"mr-1"}),(0,c.jsxs)("span",{children:["Reasoning: ",l.reasoningTokens]})]})}),(null==l?void 0:l.totalTokens)!==void 0&&(0,c.jsx)(W.Z,{title:"Total tokens",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(tG.Z,{className:"mr-1"}),(0,c.jsxs)("span",{children:["Total: ",l.totalTokens]})]})})]}):null},t$=l(61935),tX=l(69993),tQ=l(12660),t0=l(71891),t1=l(44625),t2=l(26430),t4=l(26349),t5=l(23907);let{TextArea:t3}=q.default;var t6=e=>{let{accessToken:s,token:l,userRole:t,userID:a,disabledPersonalKeyCreation:r}=e,[n,i]=(0,d.useState)(r?"custom":"session"),[m,u]=(0,d.useState)(""),[x,h]=(0,d.useState)(""),[p,g]=(0,d.useState)([]),[f,_]=(0,d.useState)(void 0),[v,y]=(0,d.useState)(!1),[b,Z]=(0,d.useState)([]),N=(0,d.useRef)(null),[w,C]=(0,d.useState)(o.CHAT),[I,T]=(0,d.useState)(!1),E=(0,d.useRef)(null),[P,L]=(0,d.useState)([]),[M,F]=(0,d.useState)([]),[R,q]=(0,d.useState)(null),U=(0,d.useRef)(null);(0,d.useEffect)(()=>{let e="session"===n?s:m;if(!e||!l||!t||!a){console.log("userApiKey or token or userRole or userID is missing = ",e,l,t,a);return}(async()=>{try{if(!e){console.log("userApiKey is missing");return}let s=await tO(e);if(console.log("Fetched models:",s),s.length>0&&(Z(s),_(s[0].model_group),s[0].mode)){let e=tU(s[0].model_group,s);C(e)}}catch(e){console.error("Error fetching model info:",e)}})()},[s,a,t,n,m]),(0,d.useEffect)(()=>{U.current&&setTimeout(()=>{var e;null===(e=U.current)||void 0===e||e.scrollIntoView({behavior:"smooth",block:"end"})},100)},[p]);let z=(e,s,l)=>{console.log("updateTextUI called with:",e,s,l),g(t=>{let a=t[t.length-1];if(!a||a.role!==e||a.isImage)return[...t,{role:e,content:s,model:l}];{var r;let e={...a,content:a.content+s,model:null!==(r=a.model)&&void 0!==r?r:l};return[...t.slice(0,-1),e]}})},V=e=>{g(s=>{let l=s[s.length-1];return l&&"assistant"===l.role&&!l.isImage?[...s.slice(0,s.length-1),{...l,reasoningContent:(l.reasoningContent||"")+e}]:s.length>0&&"user"===s[s.length-1].role?[...s,{role:"assistant",content:"",reasoningContent:e}]:s})},K=e=>{console.log("updateTimingData called with:",e),g(s=>{let l=s[s.length-1];if(console.log("Current last message:",l),l&&"assistant"===l.role){console.log("Updating assistant message with timeToFirstToken:",e);let t=[...s.slice(0,s.length-1),{...l,timeToFirstToken:e}];return console.log("Updated chat history:",t),t}return l&&"user"===l.role?(console.log("Creating new assistant message with timeToFirstToken:",e),[...s,{role:"assistant",content:"",timeToFirstToken:e}]):(console.log("No appropriate message found to update timing"),s)})},B=e=>{console.log("Received usage data:",e),g(s=>{let l=s[s.length-1];if(l&&"assistant"===l.role){console.log("Updating message with usage data:",e);let t={...l,usage:e};return console.log("Updated message:",t),[...s.slice(0,s.length-1),t]}return s})},H=(e,s)=>{g(l=>[...l,{role:"assistant",content:e,model:s,isImage:!0}])},G=async()=>{if(""===x.trim()||!l||!t||!a)return;let e="session"===n?s:m;if(!e){D.ZP.error("Please provide an API key or select Current UI Session");return}E.current=new AbortController;let r=E.current.signal,i={role:"user",content:x},c=R||(0,tT.Z)();R||q(c),g([...p,i]),T(!0);try{if(f){if(w===o.CHAT){let s=[...p.filter(e=>!e.isImage).map(e=>{let{role:s,content:l}=e;return{role:s,content:l}}),i];await tA(s,(e,s)=>z("assistant",e,s),f,e,P,r,V,K,B,c,M.length>0?M:void 0)}else if(w===o.IMAGE)await tE(x,(e,s)=>H(e,s),f,e,P,r);else if(w===o.RESPONSES){let s=[...p.filter(e=>!e.isImage).map(e=>{let{role:s,content:l}=e;return{role:s,content:l}}),i];await tP(s,(e,s,l)=>z(e,s,l),f,e,P,r,V,K,B,c,M.length>0?M:void 0)}}}catch(e){r.aborted?console.log("Request was cancelled"):(console.error("Error fetching response",e),z("assistant","Error fetching response"))}finally{T(!1),E.current=null}h("")};if(t&&"Admin Viewer"===t){let{Title:e,Paragraph:s}=es.default;return(0,c.jsxs)("div",{children:[(0,c.jsx)(e,{level:1,children:"Access Denied"}),(0,c.jsx)(s,{children:"Ask your proxy admin for access to test models"})]})}let Y=(0,c.jsx)(t$.Z,{style:{fontSize:24},spin:!0});return(0,c.jsx)("div",{className:"w-full h-screen p-4 bg-white",children:(0,c.jsx)(eI.Z,{className:"w-full rounded-xl shadow-md overflow-hidden",children:(0,c.jsxs)("div",{className:"flex h-[80vh] w-full",children:[(0,c.jsx)("div",{className:"w-1/4 p-4 border-r border-gray-200 bg-gray-50",children:(0,c.jsx)("div",{className:"mb-6",children:(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)(A.Z,{className:"font-medium block mb-2 text-gray-700 flex items-center",children:[(0,c.jsx)(lP.Z,{className:"mr-2"})," API Key Source"]}),(0,c.jsx)(O.default,{disabled:r,defaultValue:"session",style:{width:"100%"},onChange:e=>i(e),options:[{value:"session",label:"Current UI Session"},{value:"custom",label:"Virtual Key"}],className:"rounded-md"}),"custom"===n&&(0,c.jsx)(S.Z,{className:"mt-2",placeholder:"Enter custom API key",type:"password",onValueChange:u,value:m,icon:lP.Z})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)(A.Z,{className:"font-medium block mb-2 text-gray-700 flex items-center",children:[(0,c.jsx)(tX.Z,{className:"mr-2"})," Select Model"]}),(0,c.jsx)(O.default,{placeholder:"Select a Model",onChange:e=>{console.log("selected ".concat(e)),_(e),"custom"!==e&&C(tU(e,b)),y("custom"===e)},options:[...Array.from(new Set(b.map(e=>e.model_group))).map((e,s)=>({value:e,label:e,key:s})),{value:"custom",label:"Enter custom model",key:"custom"}],style:{width:"100%"},showSearch:!0,className:"rounded-md"}),v&&(0,c.jsx)(S.Z,{className:"mt-2",placeholder:"Enter custom model name",onValueChange:e=>{N.current&&clearTimeout(N.current),N.current=setTimeout(()=>{_(e)},500)}})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)(A.Z,{className:"font-medium block mb-2 text-gray-700 flex items-center",children:[(0,c.jsx)(tQ.Z,{className:"mr-2"})," Endpoint Type"]}),(0,c.jsx)(tF,{endpointType:w,onEndpointChange:e=>{C(e)},className:"mb-4"})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)(A.Z,{className:"font-medium block mb-2 text-gray-700 flex items-center",children:[(0,c.jsx)(t0.Z,{className:"mr-2"})," Tags"]}),(0,c.jsx)(tR,{value:P,onChange:L,className:"mb-4",accessToken:s||""})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)(A.Z,{className:"font-medium block mb-2 text-gray-700 flex items-center",children:[(0,c.jsx)(t1.Z,{className:"mr-2"})," Vector Store",(0,c.jsx)(W.Z,{className:"ml-1",title:(0,c.jsxs)("span",{children:["Select vector store(s) to use for this LLM API call. You can set up your vector store ",(0,c.jsx)("a",{href:"?page=vector-stores",style:{color:"#1890ff"},children:"here"}),"."]}),children:(0,c.jsx)(J.Z,{})})]}),(0,c.jsx)(tq,{value:M,onChange:F,className:"mb-4",accessToken:s||""})]}),(0,c.jsx)(k.Z,{onClick:()=>{g([]),q(null),D.ZP.success("Chat history cleared.")},className:"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300 mt-4",icon:t2.Z,children:"Clear Chat"})]})})}),(0,c.jsxs)("div",{className:"w-3/4 flex flex-col bg-white",children:[(0,c.jsxs)("div",{className:"flex-1 overflow-auto p-4 pb-0",children:[0===p.length&&(0,c.jsxs)("div",{className:"h-full flex flex-col items-center justify-center text-gray-400",children:[(0,c.jsx)(tX.Z,{style:{fontSize:"48px",marginBottom:"16px"}}),(0,c.jsx)(A.Z,{children:"Start a conversation or generate an image"})]}),p.map((e,s)=>(0,c.jsx)("div",{className:"mb-4 ".concat("user"===e.role?"text-right":"text-left"),children:(0,c.jsxs)("div",{className:"inline-block max-w-[80%] rounded-lg shadow-sm p-3.5 px-4",style:{backgroundColor:"user"===e.role?"#f0f8ff":"#ffffff",border:"user"===e.role?"1px solid #e6f0fa":"1px solid #f0f0f0",textAlign:"left"},children:[(0,c.jsxs)("div",{className:"flex items-center gap-2 mb-1.5",children:[(0,c.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-full mr-1",style:{backgroundColor:"user"===e.role?"#e6f0fa":"#f5f5f5"},children:"user"===e.role?(0,c.jsx)(j.Z,{style:{fontSize:"12px",color:"#2563eb"}}):(0,c.jsx)(tX.Z,{style:{fontSize:"12px",color:"#4b5563"}})}),(0,c.jsx)("strong",{className:"text-sm capitalize",children:e.role}),"assistant"===e.role&&e.model&&(0,c.jsx)("span",{className:"text-xs px-2 py-0.5 rounded bg-gray-100 text-gray-600 font-normal",children:e.model})]}),e.reasoningContent&&(0,c.jsx)(tB,{reasoningContent:e.reasoningContent}),(0,c.jsxs)("div",{className:"whitespace-pre-wrap break-words max-w-full message-content",style:{wordWrap:"break-word",overflowWrap:"break-word",wordBreak:"break-word",hyphens:"auto"},children:[e.isImage?(0,c.jsx)("img",{src:e.content,alt:"Generated image",className:"max-w-full rounded-md border border-gray-200 shadow-sm",style:{maxHeight:"500px"}}):(0,c.jsx)(tI.U,{components:{code(e){let{node:s,inline:l,className:t,children:a,...r}=e,n=/language-(\w+)/.exec(t||"");return!l&&n?(0,c.jsx)(l8.Z,{style:tM.Z,language:n[1],PreTag:"div",className:"rounded-md my-2",wrapLines:!0,wrapLongLines:!0,...r,children:String(a).replace(/\n$/,"")}):(0,c.jsx)("code",{className:"".concat(t," px-1.5 py-0.5 rounded bg-gray-100 text-sm font-mono"),style:{wordBreak:"break-word"},...r,children:a})},pre:e=>{let{node:s,...l}=e;return(0,c.jsx)("pre",{style:{overflowX:"auto",maxWidth:"100%"},...l})}},children:e.content}),"assistant"===e.role&&(e.timeToFirstToken||e.usage)&&(0,c.jsx)(tY,{timeToFirstToken:e.timeToFirstToken,usage:e.usage})]})]})},s)),I&&(0,c.jsx)("div",{className:"flex justify-center items-center my-4",children:(0,c.jsx)(lg.Z,{indicator:Y})}),(0,c.jsx)("div",{ref:U,style:{height:"1px"}})]}),(0,c.jsx)("div",{className:"p-4 border-t border-gray-200 bg-white",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)(t3,{value:x,onChange:e=>h(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),G())},placeholder:w===o.CHAT||w===o.RESPONSES?"Type your message... (Shift+Enter for new line)":"Describe the image you want to generate...",disabled:I,className:"flex-1",autoSize:{minRows:1,maxRows:6},style:{resize:"none",paddingRight:"10px",paddingLeft:"10px"}}),I?(0,c.jsx)(k.Z,{onClick:()=>{E.current&&(E.current.abort(),E.current=null,T(!1),D.ZP.info("Request cancelled"))},className:"ml-2 bg-red-50 hover:bg-red-100 text-red-600 border-red-200",icon:t4.Z,children:"Cancel"}):(0,c.jsx)(k.Z,{onClick:G,className:"ml-2 text-white",icon:w===o.CHAT?t5.Z:tX.Z,children:w===o.CHAT?"Send":"Generate"})]})})]})]})})})},t8=l(19226),t7=l(45937),t9=l(28595),ae=l(68208),as=l(9775),al=l(41361),at=l(37527),aa=l(88009),ar=l(48231),an=l(41169),ai=l(57400),ao=l(58630),ac=l(55322);let{Sider:ad}=t8.default;var am=e=>{let{setPage:s,userRole:l,defaultSelectedKey:t}=e,a=[{key:"1",page:"api-keys",label:"Virtual Keys",icon:(0,c.jsx)(lP.Z,{})},{key:"3",page:"llm-playground",label:"Test Key",icon:(0,c.jsx)(t9.Z,{}),roles:eg.LQ},{key:"2",page:"models",label:"Models",icon:(0,c.jsx)(ae.Z,{}),roles:eg.LQ},{key:"12",page:"new_usage",label:"Usage",icon:(0,c.jsx)(as.Z,{}),roles:[...eg.ZL,...eg.lo]},{key:"6",page:"teams",label:"Teams",icon:(0,c.jsx)(al.Z,{})},{key:"17",page:"organizations",label:"Organizations",icon:(0,c.jsx)(at.Z,{}),roles:eg.ZL},{key:"5",page:"users",label:"Internal Users",icon:(0,c.jsx)(j.Z,{}),roles:eg.ZL},{key:"14",page:"api_ref",label:"API Reference",icon:(0,c.jsx)(tQ.Z,{})},{key:"16",page:"model-hub",label:"Model Hub",icon:(0,c.jsx)(aa.Z,{})},{key:"15",page:"logs",label:"Logs",icon:(0,c.jsx)(ar.Z,{})},{key:"experimental",page:"experimental",label:"Experimental",icon:(0,c.jsx)(an.Z,{}),children:[{key:"9",page:"caching",label:"Caching",icon:(0,c.jsx)(t1.Z,{}),roles:eg.ZL},{key:"10",page:"budgets",label:"Budgets",icon:(0,c.jsx)(at.Z,{}),roles:eg.ZL},{key:"11",page:"guardrails",label:"Guardrails",icon:(0,c.jsx)(ai.Z,{}),roles:eg.ZL},{key:"20",page:"transform-request",label:"API Playground",icon:(0,c.jsx)(tQ.Z,{}),roles:[...eg.ZL,...eg.lo]},{key:"18",page:"mcp-tools",label:"MCP Tools",icon:(0,c.jsx)(ao.Z,{}),roles:eg.ZL},{key:"19",page:"tag-management",label:"Tag Management",icon:(0,c.jsx)(t0.Z,{}),roles:eg.ZL},{key:"21",page:"vector-stores",label:"Vector Stores",icon:(0,c.jsx)(t1.Z,{}),roles:eg.ZL},{key:"4",page:"usage",label:"Old Usage",icon:(0,c.jsx)(as.Z,{})}]},{key:"settings",page:"settings",label:"Settings",icon:(0,c.jsx)(ac.Z,{}),roles:eg.ZL,children:[{key:"11",page:"general-settings",label:"Router Settings",icon:(0,c.jsx)(ac.Z,{}),roles:eg.ZL},{key:"12",page:"pass-through-settings",label:"Pass-Through",icon:(0,c.jsx)(tQ.Z,{}),roles:eg.ZL},{key:"8",page:"settings",label:"Logging & Alerts",icon:(0,c.jsx)(ac.Z,{}),roles:eg.ZL},{key:"13",page:"admin-panel",label:"Admin Settings",icon:(0,c.jsx)(ac.Z,{}),roles:eg.ZL}]}],r=(e=>{let s=a.find(s=>s.page===e);if(s)return s.key;for(let s of a)if(s.children){let l=s.children.find(s=>s.page===e);if(l)return l.key}return"1"})(t),n=a.filter(e=>!!(!e.roles||e.roles.includes(l))&&(e.children&&(e.children=e.children.filter(e=>!e.roles||e.roles.includes(l))),!0));return(0,c.jsx)(t8.default,{style:{minHeight:"100vh"},children:(0,c.jsx)(ad,{theme:"light",width:220,children:(0,c.jsx)(t7.Z,{mode:"inline",selectedKeys:[r],style:{borderRight:0,backgroundColor:"transparent",fontSize:"14px"},items:n.map(e=>{var l;return{key:e.key,icon:e.icon,label:e.label,children:null===(l=e.children)||void 0===l?void 0:l.map(e=>({key:e.key,icon:e.icon,label:e.label,onClick:()=>{let l=new URLSearchParams(window.location.search);l.set("page",e.page),window.history.pushState(null,"","?".concat(l.toString())),s(e.page)}})),onClick:e.children?void 0:()=>{let l=new URLSearchParams(window.location.search);l.set("page",e.page),window.history.pushState(null,"","?".concat(l.toString())),s(e.page)}}})})})})},au=l(96889);console.log("process.env.NODE_ENV","production"),console.log=function(){};let ax=e=>null!==e&&("Admin"===e||"Admin Viewer"===e);var ah=e=>{let{accessToken:s,token:l,userRole:t,userID:a,keys:r,premiumUser:n}=e,i=new Date,[o,m]=(0,d.useState)([]),[u,x]=(0,d.useState)([]),[h,p]=(0,d.useState)([]),[g,j]=(0,d.useState)([]),[f,_]=(0,d.useState)([]),[y,b]=(0,d.useState)([]),[Z,S]=(0,d.useState)([]),[C,I]=(0,d.useState)([]),[T,P]=(0,d.useState)([]),[O,L]=(0,d.useState)([]),[D,M]=(0,d.useState)({}),[F,R]=(0,d.useState)([]),[q,U]=(0,d.useState)(""),[z,V]=(0,d.useState)(["all-tags"]),[K,B]=(0,d.useState)({from:new Date(Date.now()-6048e5),to:new Date}),[H,J]=(0,d.useState)(null),[W,G]=(0,d.useState)(0),Y=new Date(i.getFullYear(),i.getMonth(),1),$=new Date(i.getFullYear(),i.getMonth()+1,0),X=er(Y),Q=er($);function es(e){return new Intl.NumberFormat("en-US",{maximumFractionDigits:0,notation:"compact",compactDisplay:"short"}).format(e)}console.log("keys in usage",r),console.log("premium user in usage",n);let el=async()=>{if(s)try{let e=await (0,v.g)(s);return console.log("usage tab: proxy_settings",e),e}catch(e){console.error("Error fetching proxy settings:",e)}};(0,d.useEffect)(()=>{ea(K.from,K.to)},[K,z]);let et=async(e,l,t)=>{if(!e||!l||!s)return;l.setHours(23,59,59,999),e.setHours(0,0,0,0),console.log("uiSelectedKey",t);let a=await (0,v.b1)(s,t,e.toISOString(),l.toISOString());console.log("End user data updated successfully",a),j(a)},ea=async(e,l)=>{if(!e||!l||!s)return;let t=await el();null!=t&&t.DISABLE_EXPENSIVE_DB_QUERIES||(l.setHours(23,59,59,999),e.setHours(0,0,0,0),b((await (0,v.J$)(s,e.toISOString(),l.toISOString(),0===z.length?void 0:z)).spend_per_tag),console.log("Tag spend data updated successfully"))};function er(e){let s=e.getFullYear(),l=e.getMonth()+1,t=e.getDate();return"".concat(s,"-").concat(l<10?"0"+l:l,"-").concat(t<10?"0"+t:t)}console.log("Start date is ".concat(X)),console.log("End date is ".concat(Q));let en=async(e,s,l)=>{try{let l=await e();s(l)}catch(e){console.error(l,e)}},ei=(e,s,l,t)=>{let a=[],r=new Date(s),n=e=>{if(e.includes("-"))return e;{let[s,l]=e.split(" ");return new Date(new Date().getFullYear(),new Date("".concat(s," 01 2024")).getMonth(),parseInt(l)).toISOString().split("T")[0]}},i=new Map(e.map(e=>{let s=n(e.date);return[s,{...e,date:s}]}));for(;r<=l;){let e=r.toISOString().split("T")[0];if(i.has(e))a.push(i.get(e));else{let s={date:e,api_requests:0,total_tokens:0};t.forEach(e=>{s[e]||(s[e]=0)}),a.push(s)}r.setDate(r.getDate()+1)}return a},eo=async()=>{if(s)try{let e=await (0,v.FC)(s),l=new Date,t=new Date(l.getFullYear(),l.getMonth(),1),a=new Date(l.getFullYear(),l.getMonth()+1,0),r=ei(e,t,a,[]),n=Number(r.reduce((e,s)=>e+(s.spend||0),0).toFixed(2));G(n),m(r)}catch(e){console.error("Error fetching overall spend:",e)}},ec=()=>en(()=>s&&l?(0,v.OU)(s,l,X,Q):Promise.reject("No access token or token"),L,"Error fetching provider spend"),ed=async()=>{s&&await en(async()=>(await (0,v.tN)(s)).map(e=>({key:e.api_key.substring(0,10),api_key:e.api_key,key_alias:e.key_alias,spend:Number(e.total_spend.toFixed(2))})),x,"Error fetching top keys")},em=async()=>{s&&await en(async()=>(await (0,v.Au)(s)).map(e=>({key:e.model,spend:Number(e.total_spend.toFixed(2))})),p,"Error fetching top models")},eu=async()=>{s&&await en(async()=>{let e=await (0,v.mR)(s),l=new Date,t=new Date(l.getFullYear(),l.getMonth(),1),a=new Date(l.getFullYear(),l.getMonth()+1,0);return _(ei(e.daily_spend,t,a,e.teams)),I(e.teams),e.total_spend_per_team.map(e=>({name:e.team_id||"",value:Number(e.total_spend||0).toFixed(2)}))},P,"Error fetching team spend")},ex=()=>{s&&en(async()=>(await (0,v.X)(s)).tag_names,S,"Error fetching tag names")},eh=()=>{s&&en(()=>{var e,l;return(0,v.J$)(s,null===(e=K.from)||void 0===e?void 0:e.toISOString(),null===(l=K.to)||void 0===l?void 0:l.toISOString(),void 0)},e=>b(e.spend_per_tag),"Error fetching top tags")},ep=()=>{s&&en(()=>(0,v.b1)(s,null,void 0,void 0),j,"Error fetching top end users")},eg=async()=>{if(s)try{let e=await (0,v.wd)(s,X,Q),l=new Date,t=new Date(l.getFullYear(),l.getMonth(),1),a=new Date(l.getFullYear(),l.getMonth()+1,0),r=ei(e.daily_data||[],t,a,["api_requests","total_tokens"]);M({...e,daily_data:r})}catch(e){console.error("Error fetching global activity:",e)}},ej=async()=>{if(s)try{let e=await (0,v.xA)(s,X,Q),l=new Date,t=new Date(l.getFullYear(),l.getMonth(),1),a=new Date(l.getFullYear(),l.getMonth()+1,0),r=e.map(e=>({...e,daily_data:ei(e.daily_data||[],t,a,["api_requests","total_tokens"])}));R(r)}catch(e){console.error("Error fetching global activity per model:",e)}};return((0,d.useEffect)(()=>{(async()=>{if(s&&l&&t&&a){let e=await el();e&&(J(e),null!=e&&e.DISABLE_EXPENSIVE_DB_QUERIES)||(console.log("fetching data - valiue of proxySettings",H),eo(),ec(),ed(),em(),eg(),ej(),ax(t)&&(eu(),ex(),eh(),ep()))}})()},[s,l,t,a,X,Q]),null==H?void 0:H.DISABLE_EXPENSIVE_DB_QUERIES)?(0,c.jsx)("div",{style:{width:"100%"},className:"p-8",children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Database Query Limit Reached"}),(0,c.jsxs)(A.Z,{className:"mt-4",children:["SpendLogs in DB has ",H.NUM_SPEND_LOGS_ROWS," rows.",(0,c.jsx)("br",{}),"Please follow our guide to view usage when SpendLogs has more than 1M rows."]}),(0,c.jsx)(k.Z,{className:"mt-4",children:(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/spending_monitoring",target:"_blank",children:"View Usage Guide"})})]})}):(0,c.jsx)("div",{style:{width:"100%"},className:"p-8",children:(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{className:"mt-2",children:[(0,c.jsx)(eT.Z,{children:"All Up"}),ax(t)?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(eT.Z,{children:"Team Based Usage"}),(0,c.jsx)(eT.Z,{children:"Customer Usage"}),(0,c.jsx)(eT.Z,{children:"Tag Based Usage"})]}):(0,c.jsx)(c.Fragment,{children:(0,c.jsx)("div",{})})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{variant:"solid",className:"mt-1",children:[(0,c.jsx)(eT.Z,{children:"Cost"}),(0,c.jsx)(eT.Z,{children:"Activity"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:2,className:"gap-2 h-[100vh] w-full",children:[(0,c.jsxs)(N.Z,{numColSpan:2,children:[(0,c.jsxs)(A.Z,{className:"text-tremor-default text-tremor-content dark:text-dark-tremor-content mb-2 mt-2 text-lg",children:["Project Spend ",new Date().toLocaleString("default",{month:"long"})," 1 - ",new Date(new Date().getFullYear(),new Date().getMonth()+1,0).getDate()]}),(0,c.jsx)(tf,{userID:a,userRole:t,accessToken:s,userSpend:W,selectedTeam:null,userMaxBudget:null})]}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Monthly Spend"}),(0,c.jsx)(sZ.Z,{data:o,index:"date",categories:["spend"],colors:["cyan"],valueFormatter:e=>"$ ".concat(e.toFixed(2)),yAxisWidth:100,tickGap:5})]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(eI.Z,{className:"h-full",children:[(0,c.jsx)(E.Z,{children:"Top API Keys"}),(0,c.jsx)(tv,{topKeys:u,accessToken:s,userID:a,userRole:t,teams:null})]})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(eI.Z,{className:"h-full",children:[(0,c.jsx)(E.Z,{children:"Top Models"}),(0,c.jsx)(sZ.Z,{className:"mt-4 h-40",data:h,index:"key",categories:["spend"],colors:["cyan"],yAxisWidth:200,layout:"vertical",showXAxis:!1,showLegend:!1,valueFormatter:e=>"$".concat(e.toFixed(2))})]})}),(0,c.jsx)(N.Z,{numColSpan:1}),(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{className:"mb-2",children:[(0,c.jsx)(E.Z,{children:"Spend by Provider"}),(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(tj.Z,{className:"mt-4 h-40",variant:"pie",data:O,index:"provider",category:"spend",colors:["cyan"],valueFormatter:e=>"$".concat(e.toFixed(2))})}),(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Provider"}),(0,c.jsx)(e1.Z,{children:"Spend"})]})}),(0,c.jsx)(eX.Z,{children:O.map(e=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.provider}),(0,c.jsx)(eQ.Z,{children:1e-5>parseFloat(e.spend.toFixed(2))?"less than 0.00":e.spend.toFixed(2)})]},e.provider))})]})})]})})]})})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:1,className:"gap-2 h-[75vh] w-full",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"All Up"}),(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsxs)(N.Z,{children:[(0,c.jsxs)(se.Z,{style:{fontSize:"15px",fontWeight:"normal",color:"#535452"},children:["API Requests ",es(D.sum_api_requests)]}),(0,c.jsx)(sb.Z,{className:"h-40",data:D.daily_data,valueFormatter:es,index:"date",colors:["cyan"],categories:["api_requests"],onValueChange:e=>console.log(e)})]}),(0,c.jsxs)(N.Z,{children:[(0,c.jsxs)(se.Z,{style:{fontSize:"15px",fontWeight:"normal",color:"#535452"},children:["Tokens ",es(D.sum_total_tokens)]}),(0,c.jsx)(sZ.Z,{className:"h-40",data:D.daily_data,valueFormatter:es,index:"date",colors:["cyan"],categories:["total_tokens"],onValueChange:e=>console.log(e)})]})]})]}),(0,c.jsx)(c.Fragment,{children:F.map((e,s)=>(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:e.model}),(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsxs)(N.Z,{children:[(0,c.jsxs)(se.Z,{style:{fontSize:"15px",fontWeight:"normal",color:"#535452"},children:["API Requests ",es(e.sum_api_requests)]}),(0,c.jsx)(sb.Z,{className:"h-40",data:e.daily_data,index:"date",colors:["cyan"],categories:["api_requests"],valueFormatter:es,onValueChange:e=>console.log(e)})]}),(0,c.jsxs)(N.Z,{children:[(0,c.jsxs)(se.Z,{style:{fontSize:"15px",fontWeight:"normal",color:"#535452"},children:["Tokens ",es(e.sum_total_tokens)]}),(0,c.jsx)(sZ.Z,{className:"h-40",data:e.daily_data,index:"date",colors:["cyan"],categories:["total_tokens"],valueFormatter:es,onValueChange:e=>console.log(e)})]})]})]},s))})]})})]})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(w.Z,{numItems:2,className:"gap-2 h-[75vh] w-full",children:[(0,c.jsxs)(N.Z,{numColSpan:2,children:[(0,c.jsxs)(eI.Z,{className:"mb-2",children:[(0,c.jsx)(E.Z,{children:"Total Spend Per Team"}),(0,c.jsx)(au.Z,{data:T})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Daily Spend Per Team"}),(0,c.jsx)(sZ.Z,{className:"h-72",data:f,showLegend:!0,index:"date",categories:C,yAxisWidth:80,stack:!0})]})]}),(0,c.jsx)(N.Z,{numColSpan:2})]})}),(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)("p",{className:"mb-2 text-gray-500 italic text-[12px]",children:["Customers of your LLM API calls. Tracked when a `user` param is passed in your LLM calls ",(0,c.jsx)("a",{className:"text-blue-500",href:"https://docs.litellm.ai/docs/proxy/users",target:"_blank",children:"docs here"})]}),(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsxs)(N.Z,{children:[(0,c.jsx)(A.Z,{children:"Select Time Range"}),(0,c.jsx)(sv.Z,{enableSelect:!0,value:K,onValueChange:e=>{B(e),et(e.from,e.to,null)}})]}),(0,c.jsxs)(N.Z,{children:[(0,c.jsx)(A.Z,{children:"Select Key"}),(0,c.jsxs)(eS.Z,{defaultValue:"all-keys",children:[(0,c.jsx)(ee.Z,{value:"all-keys",onClick:()=>{et(K.from,K.to,null)},children:"All Keys"},"all-keys"),null==r?void 0:r.map((e,s)=>e&&null!==e.key_alias&&e.key_alias.length>0?(0,c.jsx)(ee.Z,{value:String(s),onClick:()=>{et(K.from,K.to,e.token)},children:e.key_alias},s):null)]})]})]}),(0,c.jsx)(eI.Z,{className:"mt-4",children:(0,c.jsxs)(e$.Z,{className:"max-h-[70vh] min-h-[500px]",children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Customer"}),(0,c.jsx)(e1.Z,{children:"Spend"}),(0,c.jsx)(e1.Z,{children:"Total Events"})]})}),(0,c.jsx)(eX.Z,{children:null==g?void 0:g.map((e,s)=>{var l;return(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.end_user}),(0,c.jsx)(eQ.Z,{children:null===(l=e.total_spend)||void 0===l?void 0:l.toFixed(4)}),(0,c.jsx)(eQ.Z,{children:e.total_count})]},s)})})]})})]}),(0,c.jsxs)(eP.Z,{children:[(0,c.jsxs)(w.Z,{numItems:2,children:[(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(sv.Z,{className:"mb-4",enableSelect:!0,value:K,onValueChange:e=>{B(e),ea(e.from,e.to)}})}),(0,c.jsx)(N.Z,{children:n?(0,c.jsx)("div",{children:(0,c.jsxs)(lB.Z,{value:z,onValueChange:e=>V(e),children:[(0,c.jsx)(lH.Z,{value:"all-tags",onClick:()=>V(["all-tags"]),children:"All Tags"},"all-tags"),Z&&Z.filter(e=>"all-tags"!==e).map((e,s)=>(0,c.jsx)(lH.Z,{value:String(e),children:e},e))]})}):(0,c.jsx)("div",{children:(0,c.jsxs)(lB.Z,{value:z,onValueChange:e=>V(e),children:[(0,c.jsx)(lH.Z,{value:"all-tags",onClick:()=>V(["all-tags"]),children:"All Tags"},"all-tags"),Z&&Z.filter(e=>"all-tags"!==e).map((e,s)=>(0,c.jsxs)(ee.Z,{value:String(e),disabled:!0,children:["✨ ",e," (Enterprise only Feature)"]},e))]})})})]}),(0,c.jsxs)(w.Z,{numItems:2,className:"gap-2 h-[75vh] w-full mb-4",children:[(0,c.jsx)(N.Z,{numColSpan:2,children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Spend Per Tag"}),(0,c.jsxs)(A.Z,{children:["Get Started Tracking cost per tag ",(0,c.jsx)("a",{className:"text-blue-500",href:"https://docs.litellm.ai/docs/proxy/cost_tracking",target:"_blank",children:"here"})]}),(0,c.jsx)(sZ.Z,{className:"h-72",data:y,index:"name",categories:["spend"],colors:["cyan"]})]})}),(0,c.jsx)(N.Z,{numColSpan:2})]})]})]})]})})},ap=l(51853);let ag=e=>{let{responseTimeMs:s}=e;return null==s?null:(0,c.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500 font-mono",children:[(0,c.jsx)("svg",{className:"w-4 h-4",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)("path",{d:"M12 6V12L16 14M12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),(0,c.jsxs)("span",{children:[s.toFixed(0),"ms"]})]})},aj=e=>{let s=e;if("string"==typeof s)try{s=JSON.parse(s)}catch(e){}return s},af=e=>{let{label:s,value:l}=e,[t,a]=d.useState(!1),[r,n]=d.useState(!1),i=(null==l?void 0:l.toString())||"N/A",o=i.length>50?i.substring(0,50)+"...":i;return(0,c.jsx)("tr",{className:"hover:bg-gray-50",children:(0,c.jsx)("td",{className:"px-4 py-2 align-top",colSpan:2,children:(0,c.jsxs)("div",{className:"flex items-center justify-between group",children:[(0,c.jsxs)("div",{className:"flex items-center flex-1",children:[(0,c.jsx)("button",{onClick:()=>a(!t),className:"text-gray-400 hover:text-gray-600 mr-2",children:t?"▼":"▶"}),(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{className:"text-sm text-gray-600",children:s}),(0,c.jsx)("pre",{className:"mt-1 text-sm font-mono text-gray-800 whitespace-pre-wrap",children:t?i:o})]})]}),(0,c.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(i),n(!0),setTimeout(()=>n(!1),2e3)},className:"opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600",children:(0,c.jsx)(ap.Z,{className:"h-4 w-4"})})]})})})},a_=e=>{var s,l,t,a,r,n,i,o,d,m,u,x,h,p;let{response:g}=e,j=null,f={},_={};try{if(null==g?void 0:g.error)try{let e="string"==typeof g.error.message?JSON.parse(g.error.message):g.error.message;j={message:(null==e?void 0:e.message)||"Unknown error",traceback:(null==e?void 0:e.traceback)||"No traceback available",litellm_params:(null==e?void 0:e.litellm_cache_params)||{},health_check_cache_params:(null==e?void 0:e.health_check_cache_params)||{}},f=aj(j.litellm_params)||{},_=aj(j.health_check_cache_params)||{}}catch(e){console.warn("Error parsing error details:",e),j={message:String(g.error.message||"Unknown error"),traceback:"Error parsing details",litellm_params:{},health_check_cache_params:{}}}else f=aj(null==g?void 0:g.litellm_cache_params)||{},_=aj(null==g?void 0:g.health_check_cache_params)||{}}catch(e){console.warn("Error in response parsing:",e),f={},_={}}let v={redis_host:(null==_?void 0:null===(t=_.redis_client)||void 0===t?void 0:null===(l=t.connection_pool)||void 0===l?void 0:null===(s=l.connection_kwargs)||void 0===s?void 0:s.host)||(null==_?void 0:null===(n=_.redis_async_client)||void 0===n?void 0:null===(r=n.connection_pool)||void 0===r?void 0:null===(a=r.connection_kwargs)||void 0===a?void 0:a.host)||(null==_?void 0:null===(i=_.connection_kwargs)||void 0===i?void 0:i.host)||(null==_?void 0:_.host)||"N/A",redis_port:(null==_?void 0:null===(m=_.redis_client)||void 0===m?void 0:null===(d=m.connection_pool)||void 0===d?void 0:null===(o=d.connection_kwargs)||void 0===o?void 0:o.port)||(null==_?void 0:null===(h=_.redis_async_client)||void 0===h?void 0:null===(x=h.connection_pool)||void 0===x?void 0:null===(u=x.connection_kwargs)||void 0===u?void 0:u.port)||(null==_?void 0:null===(p=_.connection_kwargs)||void 0===p?void 0:p.port)||(null==_?void 0:_.port)||"N/A",redis_version:(null==_?void 0:_.redis_version)||"N/A",startup_nodes:(()=>{try{var e,s,l,t,a,r,n,i,o,c,d,m,u;if(null==_?void 0:null===(e=_.redis_kwargs)||void 0===e?void 0:e.startup_nodes)return JSON.stringify(_.redis_kwargs.startup_nodes);let x=(null==_?void 0:null===(t=_.redis_client)||void 0===t?void 0:null===(l=t.connection_pool)||void 0===l?void 0:null===(s=l.connection_kwargs)||void 0===s?void 0:s.host)||(null==_?void 0:null===(n=_.redis_async_client)||void 0===n?void 0:null===(r=n.connection_pool)||void 0===r?void 0:null===(a=r.connection_kwargs)||void 0===a?void 0:a.host),h=(null==_?void 0:null===(c=_.redis_client)||void 0===c?void 0:null===(o=c.connection_pool)||void 0===o?void 0:null===(i=o.connection_kwargs)||void 0===i?void 0:i.port)||(null==_?void 0:null===(u=_.redis_async_client)||void 0===u?void 0:null===(m=u.connection_pool)||void 0===m?void 0:null===(d=m.connection_kwargs)||void 0===d?void 0:d.port);return x&&h?JSON.stringify([{host:x,port:h}]):"N/A"}catch(e){return"N/A"}})(),namespace:(null==_?void 0:_.namespace)||"N/A"};return(0,c.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,c.jsxs)(eA.Z,{children:[(0,c.jsxs)(eE.Z,{className:"border-b border-gray-200 px-4",children:[(0,c.jsx)(eT.Z,{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800",children:"Summary"}),(0,c.jsx)(eT.Z,{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800",children:"Raw Response"})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{className:"p-4",children:(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex items-center mb-6",children:[(null==g?void 0:g.status)==="healthy"?(0,c.jsx)(ed.Z,{className:"h-5 w-5 text-green-500 mr-2"}):(0,c.jsx)(ec.Z,{className:"h-5 w-5 text-red-500 mr-2"}),(0,c.jsxs)(A.Z,{className:"text-sm font-medium ".concat((null==g?void 0:g.status)==="healthy"?"text-green-500":"text-red-500"),children:["Cache Status: ",(null==g?void 0:g.status)||"unhealthy"]})]}),(0,c.jsx)("table",{className:"w-full border-collapse",children:(0,c.jsxs)("tbody",{children:[j&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("tr",{children:(0,c.jsx)("td",{colSpan:2,className:"pt-4 pb-2 font-semibold text-red-600",children:"Error Details"})}),(0,c.jsx)(af,{label:"Error Message",value:j.message}),(0,c.jsx)(af,{label:"Traceback",value:j.traceback})]}),(0,c.jsx)("tr",{children:(0,c.jsx)("td",{colSpan:2,className:"pt-4 pb-2 font-semibold",children:"Cache Details"})}),(0,c.jsx)(af,{label:"Cache Configuration",value:String(null==f?void 0:f.type)}),(0,c.jsx)(af,{label:"Ping Response",value:String(g.ping_response)}),(0,c.jsx)(af,{label:"Set Cache Response",value:g.set_cache_response||"N/A"}),(0,c.jsx)(af,{label:"litellm_settings.cache_params",value:JSON.stringify(f,null,2)}),(null==f?void 0:f.type)==="redis"&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("tr",{children:(0,c.jsx)("td",{colSpan:2,className:"pt-4 pb-2 font-semibold",children:"Redis Details"})}),(0,c.jsx)(af,{label:"Redis Host",value:v.redis_host||"N/A"}),(0,c.jsx)(af,{label:"Redis Port",value:v.redis_port||"N/A"}),(0,c.jsx)(af,{label:"Redis Version",value:v.redis_version||"N/A"}),(0,c.jsx)(af,{label:"Startup Nodes",value:v.startup_nodes||"N/A"}),(0,c.jsx)(af,{label:"Namespace",value:v.namespace||"N/A"})]})]})})]})}),(0,c.jsx)(eP.Z,{className:"p-4",children:(0,c.jsx)("div",{className:"bg-gray-50 rounded-md p-4 font-mono text-sm",children:(0,c.jsx)("pre",{className:"whitespace-pre-wrap break-words overflow-auto max-h-[500px]",children:(()=>{try{let e={...g,litellm_cache_params:f,health_check_cache_params:_},s=JSON.parse(JSON.stringify(e,(e,s)=>{if("string"==typeof s)try{return JSON.parse(s)}catch(e){}return s}));return JSON.stringify(s,null,2)}catch(e){return"Error formatting JSON: "+e.message}})()})})})]})]})})},av=e=>{let{accessToken:s,healthCheckResponse:l,runCachingHealthCheck:t,responseTimeMs:a}=e,[r,n]=d.useState(null),[i,o]=d.useState(!1),m=async()=>{o(!0);let e=performance.now();await t(),n(performance.now()-e),o(!1)};return(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)(k.Z,{onClick:m,disabled:i,className:"bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white text-sm px-4 py-2 rounded-md",children:i?"Running Health Check...":"Run Health Check"}),(0,c.jsx)(ag,{responseTimeMs:r})]}),l&&(0,c.jsx)(a_,{response:l})]})},ay=e=>{if(e)return e.toISOString().split("T")[0]};function ab(e){return new Intl.NumberFormat("en-US",{maximumFractionDigits:0,notation:"compact",compactDisplay:"short"}).format(e)}var aZ=e=>{let{accessToken:s,token:l,userRole:t,userID:a,premiumUser:r}=e,[n,i]=(0,d.useState)([]),[o,m]=(0,d.useState)([]),[u,x]=(0,d.useState)([]),[h,p]=(0,d.useState)([]),[g,j]=(0,d.useState)("0"),[f,_]=(0,d.useState)("0"),[y,b]=(0,d.useState)("0"),[Z,k]=(0,d.useState)({from:new Date(Date.now()-6048e5),to:new Date}),[S,C]=(0,d.useState)(""),[I,T]=(0,d.useState)("");(0,d.useEffect)(()=>{s&&Z&&((async()=>{p(await (0,v.zg)(s,ay(Z.from),ay(Z.to)))})(),C(new Date().toLocaleString()))},[s]);let E=Array.from(new Set(h.map(e=>{var s;return null!==(s=null==e?void 0:e.api_key)&&void 0!==s?s:""}))),P=Array.from(new Set(h.map(e=>{var s;return null!==(s=null==e?void 0:e.model)&&void 0!==s?s:""})));Array.from(new Set(h.map(e=>{var s;return null!==(s=null==e?void 0:e.call_type)&&void 0!==s?s:""})));let O=async(e,l)=>{e&&l&&s&&(l.setHours(23,59,59,999),e.setHours(0,0,0,0),p(await (0,v.zg)(s,ay(e),ay(l))))};(0,d.useEffect)(()=>{console.log("DATA IN CACHE DASHBOARD",h);let e=h;o.length>0&&(e=e.filter(e=>o.includes(e.api_key))),u.length>0&&(e=e.filter(e=>u.includes(e.model))),console.log("before processed data in cache dashboard",e);let s=0,l=0,t=0,a=e.reduce((e,a)=>{console.log("Processing item:",a),a.call_type||(console.log("Item has no call_type:",a),a.call_type="Unknown"),s+=(a.total_rows||0)-(a.cache_hit_true_rows||0),l+=a.cache_hit_true_rows||0,t+=a.cached_completion_tokens||0;let r=e.find(e=>e.name===a.call_type);return r?(r["LLM API requests"]+=(a.total_rows||0)-(a.cache_hit_true_rows||0),r["Cache hit"]+=a.cache_hit_true_rows||0,r["Cached Completion Tokens"]+=a.cached_completion_tokens||0,r["Generated Completion Tokens"]+=a.generated_completion_tokens||0):e.push({name:a.call_type,"LLM API requests":(a.total_rows||0)-(a.cache_hit_true_rows||0),"Cache hit":a.cache_hit_true_rows||0,"Cached Completion Tokens":a.cached_completion_tokens||0,"Generated Completion Tokens":a.generated_completion_tokens||0}),e},[]);j(ab(l)),_(ab(t));let r=l+s;r>0?b((l/r*100).toFixed(2)):b("0"),i(a),console.log("PROCESSED DATA IN CACHE DASHBOARD",a)},[o,u,Z,h]);let L=async()=>{try{D.ZP.info("Running cache health check..."),T("");let e=await (0,v.Tj)(null!==s?s:"");console.log("CACHING HEALTH CHECK RESPONSE",e),T(e)}catch(s){let e;if(console.error("Error running health check:",s),s&&s.message)try{let l=JSON.parse(s.message);l.error&&(l=l.error),e=l}catch(l){e={message:s.message}}else e={message:"Unknown error occurred"};T({error:e})}};return(0,c.jsxs)(eA.Z,{className:"gap-2 p-8 h-full w-full mt-2 mb-8",children:[(0,c.jsxs)(eE.Z,{className:"flex justify-between mt-2 w-full items-center",children:[(0,c.jsxs)("div",{className:"flex",children:[(0,c.jsx)(eT.Z,{children:"Cache Analytics"}),(0,c.jsx)(eT.Z,{children:(0,c.jsx)("pre",{children:"Cache Health"})})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[S&&(0,c.jsxs)(A.Z,{children:["Last Refreshed: ",S]}),(0,c.jsx)(sy.Z,{icon:eD.Z,variant:"shadow",size:"xs",className:"self-center",onClick:()=>{C(new Date().toLocaleString())}})]})]}),(0,c.jsxs)(eO.Z,{children:[(0,c.jsx)(eP.Z,{children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsxs)(w.Z,{numItems:3,className:"gap-4 mt-4",children:[(0,c.jsx)(N.Z,{children:(0,c.jsx)(lB.Z,{placeholder:"Select API Keys",value:o,onValueChange:m,children:E.map(e=>(0,c.jsx)(lH.Z,{value:e,children:e},e))})}),(0,c.jsx)(N.Z,{children:(0,c.jsx)(lB.Z,{placeholder:"Select Models",value:u,onValueChange:x,children:P.map(e=>(0,c.jsx)(lH.Z,{value:e,children:e},e))})}),(0,c.jsx)(N.Z,{children:(0,c.jsx)(sv.Z,{enableSelect:!0,value:Z,onValueChange:e=>{k(e),O(e.from,e.to)},selectPlaceholder:"Select date range"})})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 mt-4",children:[(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)("p",{className:"text-tremor-default font-medium text-tremor-content dark:text-dark-tremor-content",children:"Cache Hit Ratio"}),(0,c.jsx)("div",{className:"mt-2 flex items-baseline space-x-2.5",children:(0,c.jsxs)("p",{className:"text-tremor-metric font-semibold text-tremor-content-strong dark:text-dark-tremor-content-strong",children:[y,"%"]})})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)("p",{className:"text-tremor-default font-medium text-tremor-content dark:text-dark-tremor-content",children:"Cache Hits"}),(0,c.jsx)("div",{className:"mt-2 flex items-baseline space-x-2.5",children:(0,c.jsx)("p",{className:"text-tremor-metric font-semibold text-tremor-content-strong dark:text-dark-tremor-content-strong",children:g})})]}),(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)("p",{className:"text-tremor-default font-medium text-tremor-content dark:text-dark-tremor-content",children:"Cached Tokens"}),(0,c.jsx)("div",{className:"mt-2 flex items-baseline space-x-2.5",children:(0,c.jsx)("p",{className:"text-tremor-metric font-semibold text-tremor-content-strong dark:text-dark-tremor-content-strong",children:f})})]})]}),(0,c.jsx)(se.Z,{className:"mt-4",children:"Cache Hits vs API Requests"}),(0,c.jsx)(sZ.Z,{title:"Cache Hits vs API Requests",data:n,stack:!0,index:"name",valueFormatter:ab,categories:["LLM API requests","Cache hit"],colors:["sky","teal"],yAxisWidth:48}),(0,c.jsx)(se.Z,{className:"mt-4",children:"Cached Completion Tokens vs Generated Completion Tokens"}),(0,c.jsx)(sZ.Z,{className:"mt-6",data:n,stack:!0,index:"name",valueFormatter:ab,categories:["Generated Completion Tokens","Cached Completion Tokens"],colors:["sky","teal"],yAxisWidth:48})]})}),(0,c.jsx)(eP.Z,{children:(0,c.jsx)(av,{accessToken:s,healthCheckResponse:I,runCachingHealthCheck:L})})]})]})},aN=e=>{let{accessToken:s}=e,[l,t]=(0,d.useState)([]);return(0,d.useEffect)(()=>{s&&(async()=>{try{let e=await (0,v.t3)(s);console.log("guardrails: ".concat(JSON.stringify(e))),t(e.guardrails)}catch(e){console.error("Error fetching guardrails:",e)}})()},[s]),(0,c.jsxs)("div",{className:"w-full mx-auto flex-auto overflow-y-auto m-8 p-2",children:[(0,c.jsxs)(A.Z,{className:"mb-4",children:["Configured guardrails and their current status. Setup guardrails in config.yaml."," ",(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/guardrails/quick_start",target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:text-blue-700 underline",children:"Docs"})]}),(0,c.jsx)(eI.Z,{children:(0,c.jsxs)(e$.Z,{children:[(0,c.jsx)(e0.Z,{children:(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(e1.Z,{children:"Guardrail Name"}),(0,c.jsx)(e1.Z,{children:"Mode"}),(0,c.jsx)(e1.Z,{children:"Status"})]})}),(0,c.jsx)(eX.Z,{children:l&&0!==l.length?null==l?void 0:l.map((e,s)=>(0,c.jsxs)(e2.Z,{children:[(0,c.jsx)(eQ.Z,{children:e.guardrail_name}),(0,c.jsx)(eQ.Z,{children:e.litellm_params.mode}),(0,c.jsx)(eQ.Z,{children:(0,c.jsx)("div",{className:"inline-flex rounded-full px-2 py-1 text-xs font-medium\n                    ".concat(e.litellm_params.default_on?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.litellm_params.default_on?"Always On":"Per Request"})})]},s)):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:3,className:"mt-4 text-gray-500 text-center py-4",children:"No guardrails configured"})})})]})})]})},aw=e=>{let{accessToken:s}=e,[l,t]=(0,d.useState)('{\n  "model": "openai/gpt-4o",\n  "messages": [\n    {\n      "role": "system",\n      "content": "You are a helpful assistant."\n    },\n    {\n      "role": "user",\n      "content": "Explain quantum computing in simple terms"\n    }\n  ],\n  "temperature": 0.7,\n  "max_tokens": 500,\n  "stream": true\n}'),[a,r]=(0,d.useState)(""),[n,i]=(0,d.useState)(!1),o=(e,s,l)=>{let t=JSON.stringify(s,null,2).split("\n").map(e=>"  ".concat(e)).join("\n"),a=Object.entries(l).map(e=>{let[s,l]=e;return"-H '".concat(s,": ").concat(l,"'")}).join(" \\\n  ");return"curl -X POST \\\n  ".concat(e," \\\n  ").concat(a?"".concat(a," \\\n  "):"","-H 'Content-Type: application/json' \\\n  -d '{\n").concat(t,"\n  }'")},m=async()=>{i(!0);try{let e;try{e=JSON.parse(l)}catch(e){D.ZP.error("Invalid JSON in request body"),i(!1);return}let t={call_type:"completion",request_body:e};if(!s){D.ZP.error("No access token found"),i(!1);return}let a=await (0,v.Yo)(s,t);if(a.raw_request_api_base&&a.raw_request_body){let e=o(a.raw_request_api_base,a.raw_request_body,a.raw_request_headers||{});r(e),D.ZP.success("Request transformed successfully")}else{let e="string"==typeof a?a:JSON.stringify(a);r(e),D.ZP.info("Transformed request received in unexpected format")}}catch(e){console.error("Error transforming request:",e),D.ZP.error("Failed to transform request")}finally{i(!1)}};return(0,c.jsxs)("div",{className:"w-full m-2",style:{overflow:"hidden"},children:[(0,c.jsx)(E.Z,{children:"Playground"}),(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"See how LiteLLM transforms your request for the specified provider."}),(0,c.jsxs)("div",{style:{display:"flex",gap:"16px",width:"100%",minWidth:0,overflow:"hidden"},className:"mt-4",children:[(0,c.jsxs)("div",{style:{flex:"1 1 50%",display:"flex",flexDirection:"column",border:"1px solid #e8e8e8",borderRadius:"8px",padding:"24px",overflow:"hidden",maxHeight:"600px",minWidth:0},children:[(0,c.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,c.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",margin:"0 0 4px 0"},children:"Original Request"}),(0,c.jsx)("p",{style:{color:"#666",margin:0},children:"The request you would send to LiteLLM /chat/completions endpoint."})]}),(0,c.jsx)("textarea",{style:{flex:"1 1 auto",width:"100%",minHeight:"240px",padding:"16px",border:"1px solid #e8e8e8",borderRadius:"6px",fontFamily:"monospace",fontSize:"14px",resize:"none",marginBottom:"24px",overflow:"auto"},value:l,onChange:e=>t(e.target.value),onKeyDown:e=>{(e.metaKey||e.ctrlKey)&&"Enter"===e.key&&(e.preventDefault(),m())},placeholder:"Press Cmd/Ctrl + Enter to transform"}),(0,c.jsx)("div",{style:{display:"flex",justifyContent:"flex-end",marginTop:"auto"},children:(0,c.jsxs)(R.ZP,{type:"primary",style:{backgroundColor:"#000",display:"flex",alignItems:"center",gap:"8px"},onClick:m,loading:n,children:[(0,c.jsx)("span",{children:"Transform"}),(0,c.jsx)("span",{children:"→"})]})})]}),(0,c.jsxs)("div",{style:{flex:"1 1 50%",display:"flex",flexDirection:"column",border:"1px solid #e8e8e8",borderRadius:"8px",padding:"24px",overflow:"hidden",maxHeight:"800px",minWidth:0},children:[(0,c.jsxs)("div",{style:{marginBottom:"24px"},children:[(0,c.jsx)("h2",{style:{fontSize:"24px",fontWeight:"bold",margin:"0 0 4px 0"},children:"Transformed Request"}),(0,c.jsx)("p",{style:{color:"#666",margin:0},children:"How LiteLLM transforms your request for the specified provider."}),(0,c.jsx)("br",{}),(0,c.jsx)("p",{style:{color:"#666",margin:0},className:"text-xs",children:"Note: Sensitive headers are not shown."})]}),(0,c.jsxs)("div",{style:{position:"relative",backgroundColor:"#f5f5f5",borderRadius:"6px",flex:"1 1 auto",display:"flex",flexDirection:"column",overflow:"hidden"},children:[(0,c.jsx)("pre",{style:{padding:"16px",fontFamily:"monospace",fontSize:"14px",margin:0,overflow:"auto",flex:"1 1 auto"},children:a||'curl -X POST \\\n  https://api.openai.com/v1/chat/completions \\\n  -H \'Authorization: Bearer sk-xxx\' \\\n  -H \'Content-Type: application/json\' \\\n  -d \'{\n  "model": "gpt-4",\n  "messages": [\n    {\n      "role": "system",\n      "content": "You are a helpful assistant."\n    }\n  ],\n  "temperature": 0.7\n  }\''}),(0,c.jsx)(R.ZP,{type:"text",icon:(0,c.jsx)(s3.Z,{}),style:{position:"absolute",right:"8px",top:"8px"},size:"small",onClick:()=>{navigator.clipboard.writeText(a||""),D.ZP.success("Copied to clipboard")}})]})]})]}),(0,c.jsx)("div",{className:"mt-4 text-right w-full",children:(0,c.jsxs)("p",{className:"text-sm text-gray-500",children:["Found an error? File an issue ",(0,c.jsx)("a",{href:"https://github.com/BerriAI/litellm/issues",target:"_blank",rel:"noopener noreferrer",children:"here"}),"."]})})]})},ak=l(21770);let aS=[{accessorKey:"mcp_info.server_name",header:"Provider",cell:e=>{let{row:s}=e,l=s.original.mcp_info.server_name,t=s.original.mcp_info.logo_url;return(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,c.jsx)("img",{src:t,alt:"".concat(l," logo"),className:"h-5 w-5 object-contain"}),(0,c.jsx)("span",{className:"font-medium",children:l})]})}},{accessorKey:"name",header:"Tool Name",cell:e=>{let{row:s}=e,l=s.getValue("name");return(0,c.jsx)("div",{children:(0,c.jsx)("span",{className:"font-mono text-sm",children:l})})}},{accessorKey:"description",header:"Description",cell:e=>{let{row:s}=e,l=s.getValue("description");return(0,c.jsx)("div",{className:"max-w-md",children:(0,c.jsx)("span",{className:"text-sm text-gray-700",children:l})})}},{id:"actions",header:"Actions",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("div",{className:"flex items-center space-x-2",children:(0,c.jsx)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5 text-left overflow-hidden truncate max-w-[200px]",onClick:()=>{"function"==typeof s.original.onToolSelect&&s.original.onToolSelect(l)},children:"Test Tool"})})}}];function aC(e){let{tool:s,onSubmit:l,isLoading:t,result:a,error:r,onClose:n}=e,[i,o]=d.useState({}),m=d.useMemo(()=>"string"==typeof s.inputSchema?{type:"object",properties:{input:{type:"string",description:"Input for this tool"}},required:["input"]}:s.inputSchema,[s.inputSchema]),u=(e,s)=>{o(l=>({...l,[e]:s}))};return(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow-lg border p-6 max-w-4xl w-full",children:[(0,c.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("h2",{className:"text-xl font-bold",children:["Test Tool: ",(0,c.jsx)("span",{className:"font-mono",children:s.name})]}),(0,c.jsx)("p",{className:"text-gray-600",children:s.description}),(0,c.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Provider: ",s.mcp_info.server_name]})]}),(0,c.jsx)("button",{onClick:n,className:"p-1 rounded-full hover:bg-gray-200",children:(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,c.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,c.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,c.jsx)("h3",{className:"font-medium mb-4",children:"Input Parameters"}),(0,c.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l(i)},children:["string"==typeof s.inputSchema?(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:"This tool uses a dynamic input schema."}),(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Input ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("input",{type:"text",value:i.input||"",onChange:e=>u("input",e.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]})]}):Object.entries(m.properties).map(e=>{var s,l,t;let[a,r]=e;return(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[a," ",(null===(s=m.required)||void 0===s?void 0:s.includes(a))&&(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),r.description&&(0,c.jsx)("p",{className:"text-xs text-gray-500 mb-1",children:r.description}),"string"===r.type&&(0,c.jsx)("input",{type:"text",value:i[a]||"",onChange:e=>u(a,e.target.value),required:null===(l=m.required)||void 0===l?void 0:l.includes(a),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"}),"number"===r.type&&(0,c.jsx)("input",{type:"number",value:i[a]||"",onChange:e=>u(a,parseFloat(e.target.value)),required:null===(t=m.required)||void 0===t?void 0:t.includes(a),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"}),"boolean"===r.type&&(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",checked:i[a]||!1,onChange:e=>u(a,e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,c.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Enable"})]})]},a)}),(0,c.jsx)("div",{className:"mt-6",children:(0,c.jsx)(k.Z,{type:"submit",disabled:t,className:"w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white",children:t?"Calling...":"Call Tool"})})]})]}),(0,c.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg overflow-auto max-h-[500px]",children:[(0,c.jsx)("h3",{className:"font-medium mb-4",children:"Result"}),t&&(0,c.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,c.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"})}),r&&(0,c.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md",children:[(0,c.jsx)("p",{className:"font-medium",children:"Error"}),(0,c.jsx)("pre",{className:"mt-2 text-xs overflow-auto whitespace-pre-wrap",children:r.message})]}),a&&!t&&!r&&(0,c.jsxs)("div",{children:[a.map((e,s)=>(0,c.jsxs)("div",{className:"mb-4",children:["text"===e.type&&(0,c.jsx)("div",{className:"bg-white border p-3 rounded-md",children:(0,c.jsx)("p",{className:"whitespace-pre-wrap text-sm",children:e.text})}),"image"===e.type&&e.url&&(0,c.jsx)("div",{className:"bg-white border p-3 rounded-md",children:(0,c.jsx)("img",{src:e.url,alt:"Tool result",className:"max-w-full h-auto rounded"})}),"embedded_resource"===e.type&&(0,c.jsxs)("div",{className:"bg-white border p-3 rounded-md",children:[(0,c.jsx)("p",{className:"text-sm font-medium",children:"Embedded Resource"}),(0,c.jsxs)("p",{className:"text-xs text-gray-500",children:["Type: ",e.resource_type]}),e.url&&(0,c.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:underline",children:"View Resource"})]})]},s)),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsxs)("details",{className:"text-xs",children:[(0,c.jsx)("summary",{className:"cursor-pointer text-gray-500 hover:text-gray-700",children:"Raw JSON Response"}),(0,c.jsx)("pre",{className:"mt-2 bg-gray-100 p-2 rounded-md overflow-auto max-h-[300px]",children:JSON.stringify(a,null,2)})]})})]}),!a&&!t&&!r&&(0,c.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,c.jsx)("p",{children:"The result will appear here after you call the tool."})})]})]})]})}function aI(e){let{columns:s,data:l,isLoading:t}=e;return(0,c.jsx)(ts,{columns:s,data:l,isLoading:t,renderSubComponent:()=>(0,c.jsx)("div",{}),getRowCanExpand:()=>!1,loadingMessage:"\uD83D\uDE85 Loading tools...",noDataMessage:"No tools found"})}function aT(e){let{accessToken:s,userRole:l,userID:t}=e,[a,r]=(0,d.useState)(""),[n,i]=(0,d.useState)(null),[o,m]=(0,d.useState)(null),[u,x]=(0,d.useState)(null),{data:h,isLoading:p}=(0,eV.a)({queryKey:["mcpTools"],queryFn:()=>{if(!s)throw Error("Access Token required");return(0,v.lU)(s)},enabled:!!s}),{mutate:g,isPending:j}=(0,ak.D)({mutationFn:e=>{if(!s)throw Error("Access Token required");return(0,v.tB)(s,e.tool.name,e.arguments)},onSuccess:e=>{m(e),x(null)},onError:e=>{x(e),m(null)}}),f=d.useMemo(()=>h?h.map(e=>({...e,onToolSelect:e=>{i(e),m(null),x(null)}})):[],[h]),_=d.useMemo(()=>f.filter(e=>{let s=a.toLowerCase();return e.name.toLowerCase().includes(s)||e.description.toLowerCase().includes(s)||e.mcp_info.server_name.toLowerCase().includes(s)}),[f,a]);return s&&l&&t?(0,c.jsxs)("div",{className:"w-full p-6",children:[(0,c.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,c.jsx)("h1",{className:"text-xl font-semibold",children:"MCP Tools"})}),(0,c.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,c.jsx)("div",{className:"border-b px-6 py-4",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{className:"relative w-64",children:[(0,c.jsx)("input",{type:"text",placeholder:"Search tools...",className:"w-full px-3 py-2 pl-8 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",value:a,onChange:e=>r(e.target.value)}),(0,c.jsx)("svg",{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),(0,c.jsxs)("div",{className:"text-sm text-gray-500",children:[_.length," tool",1!==_.length?"s":""," available"]})]})}),(0,c.jsx)(aI,{columns:aS,data:_,isLoading:p})]}),n&&(0,c.jsx)("div",{className:"fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4",children:(0,c.jsx)(aC,{tool:n,onSubmit:e=>{n&&g({tool:n,arguments:e})},isLoading:j,result:o,error:u,onClose:()=>i(null)})})]}):(0,c.jsx)("div",{className:"p-6 text-center text-gray-500",children:"Missing required authentication parameters."})}var aA=e=>{let{tagId:s,onClose:l,accessToken:t,is_admin:a,editTag:r}=e,[n]=L.Z.useForm(),[i,o]=(0,d.useState)(null),[m,u]=(0,d.useState)(r),[x,h]=(0,d.useState)([]),p=async()=>{if(t)try{let e=(await (0,v.mC)(t,[s]))[s];e&&(o(e),r&&n.setFieldsValue({name:e.name,description:e.description,models:e.models}))}catch(e){console.error("Error fetching tag details:",e),D.ZP.error("Error fetching tag details: "+e)}};(0,d.useEffect)(()=>{p()},[s,t]),(0,d.useEffect)(()=>{t&&eZ("dummy-user","Admin",t,h)},[t]);let g=async e=>{if(t)try{await (0,v.n9)(t,{name:e.name,description:e.description,models:e.models}),D.ZP.success("Tag updated successfully"),u(!1),p()}catch(e){console.error("Error updating tag:",e),D.ZP.error("Error updating tag: "+e)}};return i?(0,c.jsxs)("div",{className:"p-4",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(k.Z,{onClick:l,className:"mb-4",children:"← Back to Tags"}),(0,c.jsxs)(E.Z,{children:["Tag Name: ",i.name]}),(0,c.jsx)(A.Z,{className:"text-gray-500",children:i.description||"No description"})]}),a&&!m&&(0,c.jsx)(k.Z,{onClick:()=>u(!0),children:"Edit Tag"})]}),m?(0,c.jsx)(eI.Z,{children:(0,c.jsxs)(L.Z,{form:n,onFinish:g,layout:"vertical",initialValues:i,children:[(0,c.jsx)(L.Z.Item,{label:"Tag Name",name:"name",rules:[{required:!0,message:"Please input a tag name"}],children:(0,c.jsx)(q.default,{})}),(0,c.jsx)(L.Z.Item,{label:"Description",name:"description",children:(0,c.jsx)(q.default.TextArea,{rows:4})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Allowed LLMs"," ",(0,c.jsx)(W.Z,{title:"Select which LLMs are allowed to process this type of data",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"models",children:(0,c.jsx)(O.default,{mode:"multiple",placeholder:"Select LLMs",children:x.map(e=>(0,c.jsx)(O.default.Option,{value:e,children:K(e)},e))})}),(0,c.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,c.jsx)(k.Z,{onClick:()=>u(!1),children:"Cancel"}),(0,c.jsx)(k.Z,{type:"submit",children:"Save Changes"})]})]})}):(0,c.jsx)("div",{className:"space-y-6",children:(0,c.jsxs)(eI.Z,{children:[(0,c.jsx)(E.Z,{children:"Tag Details"}),(0,c.jsxs)("div",{className:"space-y-4 mt-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Name"}),(0,c.jsx)(A.Z,{children:i.name})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Description"}),(0,c.jsx)(A.Z,{children:i.description||"-"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Allowed LLMs"}),(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:0===i.models.length?(0,c.jsx)(eC.Z,{color:"red",children:"All Models"}):i.models.map(e=>{var s;return(0,c.jsx)(eC.Z,{color:"blue",children:(0,c.jsx)(W.Z,{title:"ID: ".concat(e),children:(null===(s=i.model_info)||void 0===s?void 0:s[e])||e})},e)})})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Created"}),(0,c.jsx)(A.Z,{children:i.created_at?new Date(i.created_at).toLocaleString():"-"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)(A.Z,{className:"font-medium",children:"Last Updated"}),(0,c.jsx)(A.Z,{children:i.updated_at?new Date(i.updated_at).toLocaleString():"-"})]})]})]})})]}):(0,c.jsx)("div",{children:"Loading..."})},aE=e=>{let{data:s,onEdit:l,onDelete:t,onSelectTag:a}=e,[r,n]=d.useState([{id:"created_at",desc:!0}]),i=[{header:"Tag Name",accessorKey:"name",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsx)(W.Z,{title:l.name,children:(0,c.jsx)(k.Z,{size:"xs",variant:"light",className:"font-mono text-blue-500 bg-blue-50 hover:bg-blue-100 text-xs font-normal px-2 py-0.5",onClick:()=>a(l.name),children:l.name})})})}},{header:"Description",accessorKey:"description",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)(W.Z,{title:l.description,children:(0,c.jsx)("span",{className:"text-xs",children:l.description||"-"})})}},{header:"Allowed LLMs",accessorKey:"models",cell:e=>{var s,l;let{row:t}=e,a=t.original;return(0,c.jsx)("div",{style:{display:"flex",flexDirection:"column"},children:(null==a?void 0:null===(s=a.models)||void 0===s?void 0:s.length)===0?(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"red",children:"All Models"}):null==a?void 0:null===(l=a.models)||void 0===l?void 0:l.map(e=>{var s;return(0,c.jsx)(eC.Z,{size:"xs",className:"mb-1",color:"blue",children:(0,c.jsx)(W.Z,{title:"ID: ".concat(e),children:(0,c.jsx)(A.Z,{children:(null===(s=a.model_info)||void 0===s?void 0:s[e])||e})})},e)})})}},{header:"Created",accessorKey:"created_at",sortingFn:"datetime",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("span",{className:"text-xs",children:new Date(l.created_at).toLocaleDateString()})}},{id:"actions",header:"",cell:e=>{let{row:s}=e,a=s.original;return(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsx)(sy.Z,{icon:sc.Z,size:"sm",onClick:()=>l(a),className:"cursor-pointer"}),(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>t(a.name),className:"cursor-pointer"})]})}}],o=(0,eG.b7)({data:s,columns:i,state:{sorting:r},onSortingChange:n,getCoreRowModel:(0,eY.sC)(),getSortedRowModel:(0,eY.tj)(),enableSorting:!0});return(0,c.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,c.jsx)("div",{className:"overflow-x-auto",children:(0,c.jsxs)(e$.Z,{className:"[&_td]:py-0.5 [&_th]:py-1",children:[(0,c.jsx)(e0.Z,{children:o.getHeaderGroups().map(e=>(0,c.jsx)(e2.Z,{children:e.headers.map(e=>(0,c.jsx)(e1.Z,{className:"py-1 h-8 ".concat("actions"===e.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),onClick:e.column.getToggleSortingHandler(),children:(0,c.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,c.jsx)("div",{className:"flex items-center",children:e.isPlaceholder?null:(0,eG.ie)(e.column.columnDef.header,e.getContext())}),"actions"!==e.id&&(0,c.jsx)("div",{className:"w-4",children:e.column.getIsSorted()?({asc:(0,c.jsx)(e4.Z,{className:"h-4 w-4 text-blue-500"}),desc:(0,c.jsx)(e5.Z,{className:"h-4 w-4 text-blue-500"})})[e.column.getIsSorted()]:(0,c.jsx)(e3.Z,{className:"h-4 w-4 text-gray-400"})})]})},e.id))},e.id))}),(0,c.jsx)(eX.Z,{children:o.getRowModel().rows.length>0?o.getRowModel().rows.map(e=>(0,c.jsx)(e2.Z,{className:"h-8",children:e.getVisibleCells().map(e=>(0,c.jsx)(eQ.Z,{className:"py-0.5 max-h-8 overflow-hidden text-ellipsis whitespace-nowrap ".concat("actions"===e.column.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),children:(0,eG.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:i.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"No tags found"})})})})})]})})})},aP=e=>{let{accessToken:s,userID:l,userRole:t}=e,[a,r]=(0,d.useState)([]),[n,i]=(0,d.useState)(!1),[o,m]=(0,d.useState)(null),[u,x]=(0,d.useState)(!1),[h,p]=(0,d.useState)(!1),[g,j]=(0,d.useState)(null),[f,_]=(0,d.useState)(""),[y]=L.Z.useForm(),[b,Z]=(0,d.useState)([]),C=async()=>{if(s)try{let e=await (0,v.UM)(s);console.log("List tags response:",e),r(Object.values(e))}catch(e){console.error("Error fetching tags:",e),D.ZP.error("Error fetching tags: "+e)}},I=async e=>{if(s)try{await (0,v.mY)(s,{name:e.tag_name,description:e.description,models:e.allowed_llms}),D.ZP.success("Tag created successfully"),i(!1),y.resetFields(),C()}catch(e){console.error("Error creating tag:",e),D.ZP.error("Error creating tag: "+e)}},T=async e=>{j(e),p(!0)},E=async()=>{if(s&&g){try{await (0,v.fE)(s,g),D.ZP.success("Tag deleted successfully"),C()}catch(e){console.error("Error deleting tag:",e),D.ZP.error("Error deleting tag: "+e)}p(!1),j(null)}};return(0,d.useEffect)(()=>{l&&t&&s&&(async()=>{try{let e=await (0,v.AZ)(s,l,t);e&&e.data&&Z(e.data)}catch(e){console.error("Error fetching models:",e),D.ZP.error("Error fetching models: "+e)}})()},[s,l,t]),(0,d.useEffect)(()=>{C()},[s]),(0,c.jsx)("div",{className:"w-full mx-4 h-[75vh]",children:o?(0,c.jsx)(aA,{tagId:o,onClose:()=>{m(null),x(!1)},accessToken:s,is_admin:"Admin"===t,editTag:u}):(0,c.jsxs)("div",{className:"gap-2 p-8 h-[75vh] w-full mt-2",children:[(0,c.jsxs)("div",{className:"flex justify-between mt-2 w-full items-center mb-4",children:[(0,c.jsx)("h1",{children:"Tag Management"}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[f&&(0,c.jsxs)(A.Z,{children:["Last Refreshed: ",f]}),(0,c.jsx)(sy.Z,{icon:eD.Z,variant:"shadow",size:"xs",className:"self-center cursor-pointer",onClick:()=>{C(),_(new Date().toLocaleString())}})]})]}),(0,c.jsxs)(A.Z,{className:"mb-4",children:["Click on a tag name to view and edit its details.",(0,c.jsxs)("p",{children:["You can use tags to restrict the usage of certain LLMs based on tags passed in the request. Read more about tag routing ",(0,c.jsx)("a",{href:"https://docs.litellm.ai/docs/proxy/tag_routing",target:"_blank",rel:"noopener noreferrer",children:"here"}),"."]})]}),(0,c.jsx)(k.Z,{className:"mb-4",onClick:()=>i(!0),children:"+ Create New Tag"}),(0,c.jsx)(w.Z,{numItems:1,className:"gap-2 pt-2 pb-2 h-[75vh] w-full mt-2",children:(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(aE,{data:a,onEdit:e=>{m(e.name),x(!0)},onDelete:T,onSelectTag:m})})}),(0,c.jsx)(M.Z,{title:"Create New Tag",visible:n,width:800,footer:null,onCancel:()=>{i(!1),y.resetFields()},children:(0,c.jsxs)(L.Z,{form:y,onFinish:I,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsx)(L.Z.Item,{label:"Tag Name",name:"tag_name",rules:[{required:!0,message:"Please input a tag name"}],children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"Description",name:"description",children:(0,c.jsx)(q.default.TextArea,{rows:4})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Allowed Models"," ",(0,c.jsx)(W.Z,{title:"Select which LLMs are allowed to process requests from this tag",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"allowed_llms",children:(0,c.jsx)(O.default,{mode:"multiple",placeholder:"Select LLMs",children:b.map(e=>(0,c.jsx)(O.default.Option,{value:e.model_info.id,children:(0,c.jsxs)("div",{children:[(0,c.jsx)("span",{children:e.model_name}),(0,c.jsxs)("span",{className:"text-gray-400 ml-2",children:["(",e.model_info.id,")"]})]})},e.model_info.id))})}),(0,c.jsx)("div",{style:{textAlign:"right",marginTop:"10px"},children:(0,c.jsx)(k.Z,{type:"submit",children:"Create Tag"})})]})}),h&&(0,c.jsx)("div",{className:"fixed z-10 inset-0 overflow-y-auto",children:(0,c.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,c.jsx)("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:(0,c.jsx)("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),(0,c.jsxs)("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,c.jsx)("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,c.jsx)("div",{className:"sm:flex sm:items-start",children:(0,c.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,c.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Delete Tag"}),(0,c.jsx)("div",{className:"mt-2",children:(0,c.jsx)("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this tag?"})})]})})}),(0,c.jsxs)("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:E,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:()=>{p(!1),j(null)},children:"Cancel"})]})]})]})})]})})},aO=e=>{let{data:s,onDelete:l}=e,[t,a]=d.useState([{id:"created_at",desc:!0}]),r=[{header:"Vector Store ID",accessorKey:"vector_store_id",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("div",{className:"overflow-hidden",children:(0,c.jsx)(W.Z,{title:l.vector_store_id,children:(0,c.jsx)("span",{className:"font-mono text-blue-500 text-xs font-normal",children:l.vector_store_id})})})}},{header:"Name",accessorKey:"vector_store_name",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)(W.Z,{title:l.vector_store_name,children:(0,c.jsx)("span",{className:"text-xs",children:l.vector_store_name||"-"})})}},{header:"Description",accessorKey:"vector_store_description",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)(W.Z,{title:l.vector_store_description,children:(0,c.jsx)("span",{className:"text-xs",children:l.vector_store_description||"-"})})}},{header:"Provider",accessorKey:"custom_llm_provider",cell:e=>{let{row:s}=e,{displayName:l,logo:t}=sa(s.original.custom_llm_provider);return(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,c.jsx)("img",{src:t,alt:l,className:"h-4 w-4"}),(0,c.jsx)("span",{className:"text-xs",children:l})]})}},{header:"Created",accessorKey:"created_at",sortingFn:"datetime",cell:e=>{let{row:s}=e,l=s.original;return(0,c.jsx)("span",{className:"text-xs",children:new Date(l.created_at).toLocaleDateString()})}},{id:"actions",header:"",cell:e=>{let{row:s}=e,t=s.original;return(0,c.jsx)("div",{className:"flex space-x-2",children:(0,c.jsx)(sy.Z,{icon:eM.Z,size:"sm",onClick:()=>l(t.vector_store_id),className:"cursor-pointer"})})}}],n=(0,eG.b7)({data:s,columns:r,state:{sorting:t},onSortingChange:a,getCoreRowModel:(0,eY.sC)(),getSortedRowModel:(0,eY.tj)(),enableSorting:!0});return(0,c.jsx)("div",{className:"rounded-lg custom-border relative",children:(0,c.jsx)("div",{className:"overflow-x-auto",children:(0,c.jsxs)(e$.Z,{className:"[&_td]:py-0.5 [&_th]:py-1",children:[(0,c.jsx)(e0.Z,{children:n.getHeaderGroups().map(e=>(0,c.jsx)(e2.Z,{children:e.headers.map(e=>(0,c.jsx)(e1.Z,{className:"py-1 h-8 ".concat("actions"===e.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),onClick:e.column.getToggleSortingHandler(),children:(0,c.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,c.jsx)("div",{className:"flex items-center",children:e.isPlaceholder?null:(0,eG.ie)(e.column.columnDef.header,e.getContext())}),"actions"!==e.id&&(0,c.jsx)("div",{className:"w-4",children:e.column.getIsSorted()?({asc:(0,c.jsx)(e4.Z,{className:"h-4 w-4 text-blue-500"}),desc:(0,c.jsx)(e5.Z,{className:"h-4 w-4 text-blue-500"})})[e.column.getIsSorted()]:(0,c.jsx)(e3.Z,{className:"h-4 w-4 text-gray-400"})})]})},e.id))},e.id))}),(0,c.jsx)(eX.Z,{children:n.getRowModel().rows.length>0?n.getRowModel().rows.map(e=>(0,c.jsx)(e2.Z,{className:"h-8",children:e.getVisibleCells().map(e=>(0,c.jsx)(eQ.Z,{className:"py-0.5 max-h-8 overflow-hidden text-ellipsis whitespace-nowrap ".concat("actions"===e.column.id?"sticky right-0 bg-white shadow-[-4px_0_8px_-6px_rgba(0,0,0,0.1)]":""),children:(0,eG.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,c.jsx)(e2.Z,{children:(0,c.jsx)(eQ.Z,{colSpan:r.length,className:"h-8 text-center",children:(0,c.jsx)("div",{className:"text-center text-gray-500",children:(0,c.jsx)("p",{children:"No vector stores found"})})})})})]})})})},aL=e=>{let{isVisible:s,onCancel:l,onSuccess:t,accessToken:a,credentials:r}=e,[i]=L.Z.useForm(),[o,m]=(0,d.useState)("{}"),u=async e=>{if(a)try{let s={};try{s=o.trim()?JSON.parse(o):{}}catch(e){D.ZP.error("Invalid JSON in metadata field");return}await (0,v.Mx)(a,{vector_store_id:e.vector_store_id,custom_llm_provider:e.custom_llm_provider,vector_store_name:e.vector_store_name,vector_store_description:e.vector_store_description,vector_store_metadata:s,litellm_credential_name:e.litellm_credential_name}),D.ZP.success("Vector store created successfully"),i.resetFields(),m("{}"),t()}catch(e){console.error("Error creating vector store:",e),D.ZP.error("Error creating vector store: "+e)}},x=()=>{i.resetFields(),m("{}"),l()};return(0,c.jsx)(M.Z,{title:"Create New Vector Store",visible:s,width:800,footer:null,onCancel:x,children:(0,c.jsxs)(L.Z,{form:i,onFinish:u,labelCol:{span:8},wrapperCol:{span:16},labelAlign:"left",children:[(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Provider"," ",(0,c.jsx)(W.Z,{title:"Select the provider for this vector store",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"custom_llm_provider",rules:[{required:!0,message:"Please select a provider"}],initialValue:"bedrock",children:(0,c.jsx)(O.default,{children:Object.entries(n).map(e=>{let[s,l]=e;return"Bedrock"===s?(0,c.jsx)(O.default.Option,{value:ss[s],children:(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("img",{src:st[l],alt:"".concat(s," logo"),className:"w-5 h-5",onError:e=>{let s=e.target,t=s.parentElement;if(t){let e=document.createElement("div");e.className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs",e.textContent=l.charAt(0),t.replaceChild(e,s)}}}),(0,c.jsx)("span",{children:l})]})},s):null})})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Vector Store ID"," ",(0,c.jsx)(W.Z,{title:"Enter the vector store ID from your api provider",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"vector_store_id",rules:[{required:!0,message:"Please input the vector store ID from your api provider"}],children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Vector Store Name"," ",(0,c.jsx)(W.Z,{title:"Custom name you want to give to the vector store, this name will be rendered on the LiteLLM UI",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"vector_store_name",children:(0,c.jsx)(S.Z,{})}),(0,c.jsx)(L.Z.Item,{label:"Description",name:"vector_store_description",children:(0,c.jsx)(q.default.TextArea,{rows:4})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Existing Credentials"," ",(0,c.jsx)(W.Z,{title:"Optionally select API provider credentials for this vector store eg. Bedrock API KEY",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),name:"litellm_credential_name",children:(0,c.jsx)(O.default,{showSearch:!0,placeholder:"Select or search for existing credentials",optionFilterProp:"children",filterOption:(e,s)=>{var l;return(null!==(l=null==s?void 0:s.label)&&void 0!==l?l:"").toLowerCase().includes(e.toLowerCase())},options:[{value:null,label:"None"},...r.map(e=>({value:e.credential_name,label:e.credential_name}))],allowClear:!0})}),(0,c.jsx)(L.Z.Item,{label:(0,c.jsxs)("span",{children:["Metadata"," ",(0,c.jsx)(W.Z,{title:"JSON metadata for the vector store (optional)",children:(0,c.jsx)(J.Z,{style:{marginLeft:"4px"}})})]}),children:(0,c.jsx)(q.default.TextArea,{rows:4,value:o,onChange:e=>m(e.target.value),placeholder:'{"key": "value"}'})}),(0,c.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,c.jsx)(k.Z,{onClick:x,variant:"secondary",children:"Cancel"}),(0,c.jsx)(k.Z,{variant:"primary",type:"submit",children:"Create"})]})]})})},aD=e=>{let{isVisible:s,onCancel:l,onConfirm:t}=e;return(0,c.jsxs)(M.Z,{title:"Delete Vector Store",visible:s,footer:null,onCancel:l,children:[(0,c.jsx)("p",{children:"Are you sure you want to delete this vector store? This action cannot be undone."}),(0,c.jsxs)("div",{className:"px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,c.jsx)(k.Z,{onClick:t,color:"red",className:"ml-2",children:"Delete"}),(0,c.jsx)(k.Z,{onClick:l,variant:"primary",children:"Cancel"})]})]})},aM=e=>{let{accessToken:s,userID:l,userRole:t}=e,[a,r]=(0,d.useState)([]),[n,i]=(0,d.useState)(!1),[o,m]=(0,d.useState)(!1),[u,x]=(0,d.useState)(null),[h,p]=(0,d.useState)(""),[g,j]=(0,d.useState)([]),f=async()=>{if(s)try{let e=await (0,v.Ou)(s);console.log("List vector stores response:",e),r(e.data||[])}catch(e){console.error("Error fetching vector stores:",e),D.ZP.error("Error fetching vector stores: "+e)}},_=async()=>{if(s)try{let e=await (0,v.N3)(s);console.log("List credentials response:",e),j(e.credentials||[])}catch(e){console.error("Error fetching credentials:",e),D.ZP.error("Error fetching credentials: "+e)}},y=async e=>{x(e),m(!0)},b=async()=>{if(s&&u){try{await (0,v.Pv)(s,u),D.ZP.success("Vector store deleted successfully"),f()}catch(e){console.error("Error deleting vector store:",e),D.ZP.error("Error deleting vector store: "+e)}m(!1),x(null)}};return(0,d.useEffect)(()=>{f(),_()},[s]),(0,c.jsx)("div",{className:"w-full mx-4 h-[75vh]",children:(0,c.jsxs)("div",{className:"gap-2 p-8 h-[75vh] w-full mt-2",children:[(0,c.jsxs)("div",{className:"flex justify-between mt-2 w-full items-center mb-4",children:[(0,c.jsx)("h1",{children:"Vector Store Management"}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[h&&(0,c.jsxs)(A.Z,{children:["Last Refreshed: ",h]}),(0,c.jsx)(sy.Z,{icon:eD.Z,variant:"shadow",size:"xs",className:"self-center cursor-pointer",onClick:()=>{f(),_(),p(new Date().toLocaleString())}})]})]}),(0,c.jsx)(A.Z,{className:"mb-4",children:(0,c.jsx)("p",{children:"You can use vector stores to store and retrieve LLM embeddings. Currently, we support Amazon Bedrock vector stores."})}),(0,c.jsx)(k.Z,{className:"mb-4",onClick:()=>i(!0),children:"+ Create Vector Store"}),(0,c.jsx)(w.Z,{numItems:1,className:"gap-2 pt-2 pb-2 h-[75vh] w-full mt-2",children:(0,c.jsx)(N.Z,{numColSpan:1,children:(0,c.jsx)(aO,{data:a,onDelete:y})})}),(0,c.jsx)(aL,{isVisible:n,onCancel:()=>i(!1),onSuccess:()=>{i(!1),f()},accessToken:s,credentials:g}),(0,c.jsx)(aD,{isVisible:o,onCancel:()=>m(!1),onConfirm:b})]})})},aF=l(49096),aR=l(53335);let{cva:aq,cx:aU,compose:az}=(0,aF.ZD)({hooks:{onComplete:e=>(0,aR.m6)(e)}});function aV(e){var s,l;let{className:t="",...a}=e,r=(0,d.useId)();return s=()=>{let e=document.getAnimations().filter(e=>e instanceof CSSAnimation&&"spin"===e.animationName),s=e.find(e=>{var s;return(null===(s=e.effect.target)||void 0===s?void 0:s.getAttribute("data-spinner-id"))===r}),l=e.find(e=>{var s;return e.effect instanceof KeyframeEffect&&(null===(s=e.effect.target)||void 0===s?void 0:s.getAttribute("data-spinner-id"))!==r});s&&l&&(s.currentTime=l.currentTime)},l=[r],(0,d.useLayoutEffect)(s,l),(0,c.jsxs)("svg",{"data-spinner-id":r,className:aU("pointer-events-none size-12 animate-spin text-current",t),fill:"none",viewBox:"0 0 24 24",...a,children:[(0,c.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,c.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}let aK=new x.S;function aB(){return(0,c.jsxs)("div",{className:aU("h-screen","flex items-center justify-center gap-4"),children:[(0,c.jsx)("div",{className:"text-lg font-medium py-2 pr-4 border-r border-r-gray-200",children:"\uD83D\uDE85 LiteLLM"}),(0,c.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,c.jsx)(aV,{className:"size-4"}),(0,c.jsx)("span",{className:"text-gray-600 text-sm",children:"Loading..."})]})]})}function aH(){let[e,s]=(0,d.useState)(""),[l,t]=(0,d.useState)(!1),[a,r]=(0,d.useState)(!1),[n,i]=(0,d.useState)(null),[o,x]=(0,d.useState)(null),[p,g]=(0,d.useState)([]),[j,f]=(0,d.useState)([]),[_,y]=(0,d.useState)([]),[N,w]=(0,d.useState)({PROXY_BASE_URL:"",PROXY_LOGOUT_URL:""}),[k,S]=(0,d.useState)(!0),C=(0,m.useSearchParams)(),[I,T]=(0,d.useState)({data:[]}),[A,E]=(0,d.useState)(null),[P,O]=(0,d.useState)(!1),[L,D]=(0,d.useState)(!0),[M,F]=(0,d.useState)(null),R=C.get("invitation_id"),[q,U]=(0,d.useState)(()=>C.get("page")||"api-keys"),[z,V]=(0,d.useState)(null),K=e=>{g(s=>s?[...s,e]:[e]),O(()=>!P)},B=!1===L&&null===A&&null===R;return((0,d.useEffect)(()=>{E(function(e){let s=document.cookie.split("; ").find(s=>s.startsWith(e+"="));return s?s.split("=")[1]:null}("token")),D(!1)},[]),(0,d.useEffect)(()=>{B&&(window.location.href=(v.H2||"")+"/sso/key/generate")},[B]),(0,d.useEffect)(()=>{if(!A)return;let e=(0,u.o)(A);if(e){if(console.log("Decoded token:",e),console.log("Decoded key:",e.key),V(e.key),r(e.disabled_non_admin_personal_key_creation),e.user_role){let l=function(e){if(!e)return"Undefined Role";switch(console.log("Received user role: ".concat(e.toLowerCase())),console.log("Received user role length: ".concat(e.toLowerCase().length)),e.toLowerCase()){case"app_owner":case"demo_app_owner":return"App Owner";case"app_admin":case"proxy_admin":return"Admin";case"proxy_admin_viewer":return"Admin Viewer";case"org_admin":return"Org Admin";case"internal_user":return"Internal User";case"internal_viewer":return"Internal Viewer";case"app_user":return"App User";default:return"Unknown Role"}}(e.user_role);console.log("Decoded user_role:",l),s(l),"Admin Viewer"==l&&U("usage")}else console.log("User role not defined");e.user_email?i(e.user_email):console.log("User Email is not set ".concat(e)),e.login_method?S("username_password"==e.login_method):console.log("User Email is not set ".concat(e)),e.premium_user&&t(e.premium_user),e.auth_header_name&&(0,v.K8)(e.auth_header_name),e.user_id&&F(e.user_id)}},[A]),(0,d.useEffect)(()=>{z&&M&&e&&eZ(M,e,z,y),z&&M&&e&&Z(z,M,e,null,x),z&&lk(z,f)},[z,M,e]),L||B)?(0,c.jsx)(aB,{}):(0,c.jsx)(d.Suspense,{fallback:(0,c.jsx)(aB,{}),children:(0,c.jsx)(h.aH,{client:aK,children:R?(0,c.jsx)(e9,{userID:M,userRole:e,premiumUser:l,teams:o,keys:p,setUserRole:s,userEmail:n,setUserEmail:i,setTeams:x,setKeys:g,organizations:j,addKey:K,createClicked:P}):(0,c.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,c.jsx)(b,{userID:M,userRole:e,premiumUser:l,userEmail:n,setProxySettings:w,proxySettings:N,accessToken:z}),(0,c.jsxs)("div",{className:"flex flex-1 overflow-auto",children:[(0,c.jsx)("div",{className:"mt-8",children:(0,c.jsx)(am,{setPage:e=>{let s=new URLSearchParams(C);s.set("page",e),window.history.pushState(null,"","?".concat(s.toString())),U(e)},userRole:e,defaultSelectedKey:q})}),"api-keys"==q?(0,c.jsx)(e9,{userID:M,userRole:e,premiumUser:l,teams:o,keys:p,setUserRole:s,userEmail:n,setUserEmail:i,setTeams:x,setKeys:g,organizations:j,addKey:K,createClicked:P}):"models"==q?(0,c.jsx)(li,{userID:M,userRole:e,token:A,keys:p,accessToken:z,modelData:I,setModelData:T,premiumUser:l,teams:o}):"llm-playground"==q?(0,c.jsx)(t6,{userID:M,userRole:e,token:A,accessToken:z,disabledPersonalKeyCreation:a}):"users"==q?(0,c.jsx)(lv,{userID:M,userRole:e,token:A,keys:p,teams:o,accessToken:z,setKeys:g}):"teams"==q?(0,c.jsx)(lN,{teams:o,setTeams:x,searchParams:C,accessToken:z,userID:M,userRole:e,organizations:j}):"organizations"==q?(0,c.jsx)(lS,{organizations:j,setOrganizations:f,userModels:_,accessToken:z,userRole:e,premiumUser:l}):"admin-panel"==q?(0,c.jsx)(lM,{setTeams:x,searchParams:C,accessToken:z,showSSOBanner:k,premiumUser:l,proxySettings:N}):"api_ref"==q?(0,c.jsx)(tC,{proxySettings:N}):"settings"==q?(0,c.jsx)(lK,{userID:M,userRole:e,accessToken:z,premiumUser:l}):"budgets"==q?(0,c.jsx)(l7,{accessToken:z}):"guardrails"==q?(0,c.jsx)(aN,{accessToken:z}):"transform-request"==q?(0,c.jsx)(aw,{accessToken:z}):"general-settings"==q?(0,c.jsx)(lQ,{userID:M,userRole:e,accessToken:z,modelData:I}):"model-hub"==q?(0,c.jsx)(tg.Z,{accessToken:z,publicPage:!1,premiumUser:l}):"caching"==q?(0,c.jsx)(aZ,{userID:M,userRole:e,token:A,accessToken:z,premiumUser:l}):"pass-through-settings"==q?(0,c.jsx)(l5,{userID:M,userRole:e,accessToken:z,modelData:I}):"logs"==q?(0,c.jsx)(th,{userID:M,userRole:e,token:A,accessToken:z,allTeams:null!=o?o:[]}):"mcp-tools"==q?(0,c.jsx)(aT,{accessToken:z,userRole:e,userID:M}):"tag-management"==q?(0,c.jsx)(aP,{accessToken:z,userRole:e,userID:M}):"vector-stores"==q?(0,c.jsx)(aM,{accessToken:z,userRole:e,userID:M}):"new_usage"==q?(0,c.jsx)(tS,{userID:M,userRole:e,accessToken:z,teams:null!=o?o:[]}):(0,c.jsx)(ah,{userID:M,userRole:e,token:A,accessToken:z,keys:p,premiumUser:l})]})]})})})}},3914:function(e,s,l){"use strict";function t(){let e=window.location.hostname,s=["Lax","Strict","None"];["/","/ui"].forEach(l=>{document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(l,";"),document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(l,"; domain=").concat(e,";"),s.forEach(s=>{let t="None"===s?" Secure;":"";document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(l,"; SameSite=").concat(s,";").concat(t),document.cookie="token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=".concat(l,"; domain=").concat(e,"; SameSite=").concat(s,";").concat(t)})}),console.log("After clearing cookies:",document.cookie)}function a(e){let s=document.cookie.split("; ").find(s=>s.startsWith(e+"="));return s?s.split("=")[1]:null}l.d(s,{b:function(){return t},e:function(){return a}})}},function(e){e.O(0,[665,990,42,261,899,894,250,699,971,117,744],function(){return e(e.s=1900)}),_N_E=e.O()}]);