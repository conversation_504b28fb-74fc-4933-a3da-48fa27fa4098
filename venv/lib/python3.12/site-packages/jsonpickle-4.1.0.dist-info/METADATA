Metadata-Version: 2.1
Name: jsonpickle
Version: 4.1.0
Summary: jsonpickle encodes/decodes any Python object to/from JSON
Author: Theelx
Author-email: <PERSON> <<EMAIL>>
License: BSD-3-Clause
Project-URL: Documentation, https://jsonpickle.readthedocs.io/
Project-URL: Homepage, https://jsonpickle.readthedocs.io/
Project-URL: Source, https://github.com/jsonpickle/jsonpickle
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: cov
Requires-Dist: pytest-cov ; extra == 'cov'
Provides-Extra: dev
Requires-Dist: black ; extra == 'dev'
Requires-Dist: pyupgrade ; extra == 'dev'
Provides-Extra: docs
Requires-Dist: furo ; extra == 'docs'
Requires-Dist: rst.linker (>=1.9) ; extra == 'docs'
Requires-Dist: sphinx (>=3.5) ; extra == 'docs'
Provides-Extra: packaging
Requires-Dist: build ; extra == 'packaging'
Requires-Dist: setuptools (>=61.2) ; extra == 'packaging'
Requires-Dist: setuptools-scm[toml] (>=6.0) ; extra == 'packaging'
Requires-Dist: twine ; extra == 'packaging'
Provides-Extra: testing
Requires-Dist: pytest (!=8.1.*,>=6.0) ; extra == 'testing'
Requires-Dist: pytest-benchmark ; extra == 'testing'
Requires-Dist: pytest-benchmark[histogram] ; extra == 'testing'
Requires-Dist: pytest-checkdocs (>=1.2.3) ; extra == 'testing'
Requires-Dist: pytest-enabler (>=1.0.1) ; extra == 'testing'
Requires-Dist: pytest-ruff (>=0.2.1) ; extra == 'testing'
Requires-Dist: bson ; extra == 'testing'
Requires-Dist: ecdsa ; extra == 'testing'
Requires-Dist: feedparser ; extra == 'testing'
Requires-Dist: gmpy2 ; extra == 'testing'
Requires-Dist: numpy ; extra == 'testing'
Requires-Dist: pandas ; extra == 'testing'
Requires-Dist: pymongo ; extra == 'testing'
Requires-Dist: PyYAML ; extra == 'testing'
Requires-Dist: scikit-learn ; extra == 'testing'
Requires-Dist: simplejson ; extra == 'testing'
Requires-Dist: sqlalchemy ; extra == 'testing'
Requires-Dist: ujson ; extra == 'testing'
Requires-Dist: atheris (~=2.3.0) ; (python_version < "3.12") and extra == 'testing'
Requires-Dist: scipy ; (python_version <= "3.10") and extra == 'testing'
Requires-Dist: scipy (>=1.9.3) ; (python_version > "3.10") and extra == 'testing'

.. image:: https://img.shields.io/pypi/v/jsonpickle.svg
   :target: `PyPI link`_

.. image:: https://img.shields.io/pypi/pyversions/jsonpickle.svg
   :target: `PyPI link`_

.. _PyPI link: https://pypi.org/project/jsonpickle

.. image:: https://readthedocs.org/projects/jsonpickle/badge/?version=latest
   :target: https://jsonpickle.readthedocs.io/en/latest/?badge=latest

.. image:: https://github.com/jsonpickle/jsonpickle/actions/workflows/test.yml/badge.svg
   :target: https://github.com/jsonpickle/jsonpickle/actions
   :alt: Github Actions

.. image:: https://img.shields.io/badge/License-BSD%203--Clause-blue.svg
   :target: https://github.com/jsonpickle/jsonpickle/blob/main/LICENSE
   :alt: BSD


jsonpickle
==========

jsonpickle is a library for the two-way conversion of complex Python objects
and `JSON <http://json.org/>`_.  jsonpickle builds upon existing JSON
encoders, such as simplejson, json, and ujson.

.. warning::

   jsonpickle can execute arbitrary Python code.

   Please see the Security section for more details.


For complete documentation, please visit the
`jsonpickle documentation <http://jsonpickle.readthedocs.io/>`_.

Bug reports and merge requests are encouraged at the
`jsonpickle repository on github <https://github.com/jsonpickle/jsonpickle>`_.

Usage
=====
The following is a very simple example of how one can use jsonpickle in their scripts/projects. Note the usage of jsonpickle.encode and decode, and how the data is written/encoded to a file and then read/decoded from the file.

.. code-block:: python

    import jsonpickle
    from dataclasses import dataclass
   
    @dataclass
    class Example:
        data: str
   
   
    ex = Example("value1")
    encoded_instance = jsonpickle.encode(ex)
    assert encoded_instance == '{"py/object": "__main__.Example", "data": "value1"}'
   
    with open("example.json", "w+") as f:
        f.write(encoded_instance)
   
    with open("example.json", "r+") as f:
        written_instance = f.read()
        decoded_instance = jsonpickle.decode(written_instance)
    assert decoded_instance == ex

For more examples, see the `examples directory on GitHub <https://github.com/jsonpickle/jsonpickle/tree/main/examples>`_ for example scripts. These can be run on your local machine to see how jsonpickle works and behaves, and how to use it. Contributions from users regarding how they use jsonpickle are welcome!


Why jsonpickle?
===============

Data serialized with python's pickle (or cPickle or dill) is not easily readable outside of python. Using the json format, jsonpickle allows simple data types to be stored in a human-readable format, and more complex data types such as numpy arrays and pandas dataframes, to be machine-readable on any platform that supports json. E.g., unlike pickled data, jsonpickled data stored in an Amazon S3 bucket is indexible by Amazon's Athena.

Security
========

jsonpickle should be treated the same as the
`Python stdlib pickle module <https://docs.python.org/3/library/pickle.html>`_
from a security perspective.

.. warning::

   The jsonpickle module **is not secure**.  Only unpickle data you trust.

   It is possible to construct malicious pickle data which will **execute
   arbitrary code during unpickling**.  Never unpickle data that could have come
   from an untrusted source, or that could have been tampered with.

   Consider signing data with an HMAC if you need to ensure that it has not
   been tampered with.

   Safer deserialization approaches, such as reading JSON directly,
   may be more appropriate if you are processing untrusted data.


Install
=======

Install from pip for the latest stable release:

::

    pip install jsonpickle

Install from github for the latest changes:

::

    pip install git+https://github.com/jsonpickle/jsonpickle.git


Numpy/Pandas Support
====================

jsonpickle includes built-in numpy and pandas extensions.  If you would
like to encode sklearn models, numpy arrays, pandas DataFrames, and other
numpy/pandas-based data, then you must enable the numpy and/or pandas
extensions by registering their handlers::

    >>> import jsonpickle.ext.numpy as jsonpickle_numpy
    >>> import jsonpickle.ext.pandas as jsonpickle_pandas
    >>> jsonpickle_numpy.register_handlers()
    >>> jsonpickle_pandas.register_handlers()


Development
===========

Use `make` to run the unit tests::

        make test

`pytest` is used to run unit tests internally.

A `tox` target is provided to run tests using all installed and supported Python versions::

        make tox

`jsonpickle` itself has no dependencies beyond the Python stdlib.
`tox` is required for testing when using the `tox` test runner only.

The testing requirements are specified in `setup.cfg`.
It is recommended to create a virtualenv and run tests from within the
virtualenv.::

        python3 -mvenv env3
        source env3/bin/activate
        pip install --editable '.[dev]'
        make test

You can also use a tool such as `vx <https://github.com/davvid/vx/>`_
to activate the virtualenv without polluting your shell environment::

        python3 -mvenv env3
        vx env3 pip install --editable '.[dev]'
        vx env3 make test

If you can't use a venv, you can install the testing packages as follows::

        pip install .[testing]

`jsonpickle` supports multiple Python versions, so using a combination of
multiple virtualenvs and `tox` is useful in order to catch compatibility
issues when developing.

GPG Signing
===========

Unfortunately, while versions of jsonpickle before 3.0.1 should still be signed,
GPG signing support was removed from PyPi
(https://blog.pypi.org/posts/2023-05-23-removing-pgp/) back in May 2023.

License
=======

Licensed under the BSD License. See the LICENSE file for more details.
