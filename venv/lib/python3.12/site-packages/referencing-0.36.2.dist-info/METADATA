Metadata-Version: 2.4
Name: referencing
Version: 0.36.2
Summary: JSON Referencing + Python
Project-URL: Documentation, https://referencing.readthedocs.io/
Project-URL: Homepage, https://github.com/python-jsonschema/referencing
Project-URL: Issues, https://github.com/python-jsonschema/referencing/issues/
Project-URL: Funding, https://github.com/sponsors/Julian
Project-URL: Tidelift, https://tidelift.com/subscription/pkg/pypi-referencing?utm_source=pypi-referencing&utm_medium=referral&utm_campaign=pypi-link
Project-URL: Changelog, https://referencing.readthedocs.io/en/stable/changes/
Project-URL: Source, https://github.com/python-jsonschema/referencing
Author-email: <PERSON> <<EMAIL>>
License-Expression: MIT
License-File: COPYING
Keywords: asyncapi,json,jsonschema,openapi,referencing
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: File Formats :: JSON
Classifier: Topic :: File Formats :: JSON :: JSON Schema
Requires-Python: >=3.9
Requires-Dist: attrs>=22.2.0
Requires-Dist: rpds-py>=0.7.0
Requires-Dist: typing-extensions>=4.4.0; python_version < '3.13'
Description-Content-Type: text/x-rst

===============
``referencing``
===============

|PyPI| |Pythons| |CI| |ReadTheDocs| |pre-commit|

.. |PyPI| image:: https://img.shields.io/pypi/v/referencing.svg
  :alt: PyPI version
  :target: https://pypi.org/project/referencing/

.. |Pythons| image:: https://img.shields.io/pypi/pyversions/referencing.svg
  :alt: Supported Python versions
  :target: https://pypi.org/project/referencing/

.. |CI| image:: https://github.com/python-jsonschema/referencing/workflows/CI/badge.svg
  :alt: Build status
  :target: https://github.com/python-jsonschema/referencing/actions?query=workflow%3ACI

.. |ReadTheDocs| image:: https://readthedocs.org/projects/referencing/badge/?version=stable&style=flat
   :alt: ReadTheDocs status
   :target: https://referencing.readthedocs.io/en/stable/

.. |pre-commit| image:: https://results.pre-commit.ci/badge/github/python-jsonschema/referencing/main.svg
  :alt: pre-commit.ci status
  :target: https://results.pre-commit.ci/latest/github/python-jsonschema/referencing/main


An implementation-agnostic implementation of JSON reference resolution.

See `the documentation <https://referencing.readthedocs.io/>`_ for more details.
