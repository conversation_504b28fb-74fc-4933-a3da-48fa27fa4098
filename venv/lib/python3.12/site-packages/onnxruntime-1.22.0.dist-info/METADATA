Metadata-Version: 2.4
Name: onnxruntime
Version: 1.22.0
Summary: ONNX Runtime is a runtime accelerator for Machine Learning models
Home-page: https://onnxruntime.ai
Download-URL: https://github.com/microsoft/onnxruntime/tags
Author: Microsoft Corporation
Author-email: <EMAIL>
License: MIT License
Keywords: onnx machine learning
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: MacOS
Classifier: Topic :: Scientific/Engineering
Classifier: Topic :: Scientific/Engineering :: Mathematics
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.10
Requires-Dist: coloredlogs
Requires-Dist: flatbuffers
Requires-Dist: numpy>=1.21.6
Requires-Dist: packaging
Requires-Dist: protobuf
Requires-Dist: sympy
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: download-url
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

ONNX Runtime
============

ONNX Runtime is a performance-focused scoring engine for Open Neural Network Exchange (ONNX) models.
For more information on ONNX Runtime, please see `aka.ms/onnxruntime <https://aka.ms/onnxruntime/>`_ or the `Github project <https://github.com/microsoft/onnxruntime/>`_.


Changes
-------

1.22.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.22.0

1.21.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.21.0

1.20.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.20.0

1.19.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.19.0

1.18.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.18.0

1.17.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.17.0

1.16.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.16.0

1.15.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.15.0

1.14.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.14.0

1.13.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.13.0

1.12.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.12.0

1.11.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.11.0

1.10.0
^^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.10.0

1.9.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.9.0

1.8.2
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.8.2

1.8.1
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.8.1

1.8.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.8.0

1.7.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.7.0

1.6.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.6.0

1.5.3
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.5.3

1.5.2
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.5.2

1.5.1
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.5.1


1.4.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.4.0

1.3.1
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.3.1

1.3.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.3.0

1.2.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.2.0

1.1.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.1.0

1.0.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v1.0.0

0.5.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v0.5.0

0.4.0
^^^^^

Release Notes : https://github.com/Microsoft/onnxruntime/releases/tag/v0.4.0
