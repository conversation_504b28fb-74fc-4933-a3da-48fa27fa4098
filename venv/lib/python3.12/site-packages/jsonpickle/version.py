try:
    from importlib import metadata
except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OSError):
    metadata = None


def _get_version():
    default_version = '0.0.0-alpha'
    try:
        version = metadata.version('jsonpickle')
    except (Attribut<PERSON><PERSON><PERSON><PERSON>, <PERSON>mportError, OSError):
        version = default_version
    return version


__version__ = _get_version()
