# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.32
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1ClusterTrustBundleProjection(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'label_selector': 'V1LabelSelector',
        'name': 'str',
        'optional': 'bool',
        'path': 'str',
        'signer_name': 'str'
    }

    attribute_map = {
        'label_selector': 'labelSelector',
        'name': 'name',
        'optional': 'optional',
        'path': 'path',
        'signer_name': 'signerName'
    }

    def __init__(self, label_selector=None, name=None, optional=None, path=None, signer_name=None, local_vars_configuration=None):  # noqa: E501
        """V1ClusterTrustBundleProjection - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._label_selector = None
        self._name = None
        self._optional = None
        self._path = None
        self._signer_name = None
        self.discriminator = None

        if label_selector is not None:
            self.label_selector = label_selector
        if name is not None:
            self.name = name
        if optional is not None:
            self.optional = optional
        self.path = path
        if signer_name is not None:
            self.signer_name = signer_name

    @property
    def label_selector(self):
        """Gets the label_selector of this V1ClusterTrustBundleProjection.  # noqa: E501


        :return: The label_selector of this V1ClusterTrustBundleProjection.  # noqa: E501
        :rtype: V1LabelSelector
        """
        return self._label_selector

    @label_selector.setter
    def label_selector(self, label_selector):
        """Sets the label_selector of this V1ClusterTrustBundleProjection.


        :param label_selector: The label_selector of this V1ClusterTrustBundleProjection.  # noqa: E501
        :type: V1LabelSelector
        """

        self._label_selector = label_selector

    @property
    def name(self):
        """Gets the name of this V1ClusterTrustBundleProjection.  # noqa: E501

        Select a single ClusterTrustBundle by object name.  Mutually-exclusive with signerName and labelSelector.  # noqa: E501

        :return: The name of this V1ClusterTrustBundleProjection.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this V1ClusterTrustBundleProjection.

        Select a single ClusterTrustBundle by object name.  Mutually-exclusive with signerName and labelSelector.  # noqa: E501

        :param name: The name of this V1ClusterTrustBundleProjection.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def optional(self):
        """Gets the optional of this V1ClusterTrustBundleProjection.  # noqa: E501

        If true, don't block pod startup if the referenced ClusterTrustBundle(s) aren't available.  If using name, then the named ClusterTrustBundle is allowed not to exist.  If using signerName, then the combination of signerName and labelSelector is allowed to match zero ClusterTrustBundles.  # noqa: E501

        :return: The optional of this V1ClusterTrustBundleProjection.  # noqa: E501
        :rtype: bool
        """
        return self._optional

    @optional.setter
    def optional(self, optional):
        """Sets the optional of this V1ClusterTrustBundleProjection.

        If true, don't block pod startup if the referenced ClusterTrustBundle(s) aren't available.  If using name, then the named ClusterTrustBundle is allowed not to exist.  If using signerName, then the combination of signerName and labelSelector is allowed to match zero ClusterTrustBundles.  # noqa: E501

        :param optional: The optional of this V1ClusterTrustBundleProjection.  # noqa: E501
        :type: bool
        """

        self._optional = optional

    @property
    def path(self):
        """Gets the path of this V1ClusterTrustBundleProjection.  # noqa: E501

        Relative path from the volume root to write the bundle.  # noqa: E501

        :return: The path of this V1ClusterTrustBundleProjection.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this V1ClusterTrustBundleProjection.

        Relative path from the volume root to write the bundle.  # noqa: E501

        :param path: The path of this V1ClusterTrustBundleProjection.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and path is None:  # noqa: E501
            raise ValueError("Invalid value for `path`, must not be `None`")  # noqa: E501

        self._path = path

    @property
    def signer_name(self):
        """Gets the signer_name of this V1ClusterTrustBundleProjection.  # noqa: E501

        Select all ClusterTrustBundles that match this signer name. Mutually-exclusive with name.  The contents of all selected ClusterTrustBundles will be unified and deduplicated.  # noqa: E501

        :return: The signer_name of this V1ClusterTrustBundleProjection.  # noqa: E501
        :rtype: str
        """
        return self._signer_name

    @signer_name.setter
    def signer_name(self, signer_name):
        """Sets the signer_name of this V1ClusterTrustBundleProjection.

        Select all ClusterTrustBundles that match this signer name. Mutually-exclusive with name.  The contents of all selected ClusterTrustBundles will be unified and deduplicated.  # noqa: E501

        :param signer_name: The signer_name of this V1ClusterTrustBundleProjection.  # noqa: E501
        :type: str
        """

        self._signer_name = signer_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1ClusterTrustBundleProjection):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1ClusterTrustBundleProjection):
            return True

        return self.to_dict() != other.to_dict()
