# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.30
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha2StructuredResourceHandle(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'node_name': 'str',
        'results': 'list[V1alpha2DriverAllocationResult]',
        'vendor_claim_parameters': 'object',
        'vendor_class_parameters': 'object'
    }

    attribute_map = {
        'node_name': 'nodeName',
        'results': 'results',
        'vendor_claim_parameters': 'vendorClaimParameters',
        'vendor_class_parameters': 'vendorClassParameters'
    }

    def __init__(self, node_name=None, results=None, vendor_claim_parameters=None, vendor_class_parameters=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha2StructuredResourceHandle - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._node_name = None
        self._results = None
        self._vendor_claim_parameters = None
        self._vendor_class_parameters = None
        self.discriminator = None

        if node_name is not None:
            self.node_name = node_name
        self.results = results
        if vendor_claim_parameters is not None:
            self.vendor_claim_parameters = vendor_claim_parameters
        if vendor_class_parameters is not None:
            self.vendor_class_parameters = vendor_class_parameters

    @property
    def node_name(self):
        """Gets the node_name of this V1alpha2StructuredResourceHandle.  # noqa: E501

        NodeName is the name of the node providing the necessary resources if the resources are local to a node.  # noqa: E501

        :return: The node_name of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this V1alpha2StructuredResourceHandle.

        NodeName is the name of the node providing the necessary resources if the resources are local to a node.  # noqa: E501

        :param node_name: The node_name of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def results(self):
        """Gets the results of this V1alpha2StructuredResourceHandle.  # noqa: E501

        Results lists all allocated driver resources.  # noqa: E501

        :return: The results of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :rtype: list[V1alpha2DriverAllocationResult]
        """
        return self._results

    @results.setter
    def results(self, results):
        """Sets the results of this V1alpha2StructuredResourceHandle.

        Results lists all allocated driver resources.  # noqa: E501

        :param results: The results of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :type: list[V1alpha2DriverAllocationResult]
        """
        if self.local_vars_configuration.client_side_validation and results is None:  # noqa: E501
            raise ValueError("Invalid value for `results`, must not be `None`")  # noqa: E501

        self._results = results

    @property
    def vendor_claim_parameters(self):
        """Gets the vendor_claim_parameters of this V1alpha2StructuredResourceHandle.  # noqa: E501

        VendorClaimParameters are the per-claim configuration parameters from the resource claim parameters at the time that the claim was allocated.  # noqa: E501

        :return: The vendor_claim_parameters of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :rtype: object
        """
        return self._vendor_claim_parameters

    @vendor_claim_parameters.setter
    def vendor_claim_parameters(self, vendor_claim_parameters):
        """Sets the vendor_claim_parameters of this V1alpha2StructuredResourceHandle.

        VendorClaimParameters are the per-claim configuration parameters from the resource claim parameters at the time that the claim was allocated.  # noqa: E501

        :param vendor_claim_parameters: The vendor_claim_parameters of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :type: object
        """

        self._vendor_claim_parameters = vendor_claim_parameters

    @property
    def vendor_class_parameters(self):
        """Gets the vendor_class_parameters of this V1alpha2StructuredResourceHandle.  # noqa: E501

        VendorClassParameters are the per-claim configuration parameters from the resource class at the time that the claim was allocated.  # noqa: E501

        :return: The vendor_class_parameters of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :rtype: object
        """
        return self._vendor_class_parameters

    @vendor_class_parameters.setter
    def vendor_class_parameters(self, vendor_class_parameters):
        """Sets the vendor_class_parameters of this V1alpha2StructuredResourceHandle.

        VendorClassParameters are the per-claim configuration parameters from the resource class at the time that the claim was allocated.  # noqa: E501

        :param vendor_class_parameters: The vendor_class_parameters of this V1alpha2StructuredResourceHandle.  # noqa: E501
        :type: object
        """

        self._vendor_class_parameters = vendor_class_parameters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha2StructuredResourceHandle):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha2StructuredResourceHandle):
            return True

        return self.to_dict() != other.to_dict()
