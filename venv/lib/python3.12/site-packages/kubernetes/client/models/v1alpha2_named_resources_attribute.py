# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.30
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha2NamedResourcesAttribute(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'bool': 'bool',
        'int': 'int',
        'int_slice': 'V1alpha2NamedResourcesIntSlice',
        'name': 'str',
        'quantity': 'str',
        'string': 'str',
        'string_slice': 'V1alpha2NamedResourcesStringSlice',
        'version': 'str'
    }

    attribute_map = {
        'bool': 'bool',
        'int': 'int',
        'int_slice': 'intSlice',
        'name': 'name',
        'quantity': 'quantity',
        'string': 'string',
        'string_slice': 'stringSlice',
        'version': 'version'
    }

    def __init__(self, bool=None, int=None, int_slice=None, name=None, quantity=None, string=None, string_slice=None, version=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha2NamedResourcesAttribute - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._bool = None
        self._int = None
        self._int_slice = None
        self._name = None
        self._quantity = None
        self._string = None
        self._string_slice = None
        self._version = None
        self.discriminator = None

        if bool is not None:
            self.bool = bool
        if int is not None:
            self.int = int
        if int_slice is not None:
            self.int_slice = int_slice
        self.name = name
        if quantity is not None:
            self.quantity = quantity
        if string is not None:
            self.string = string
        if string_slice is not None:
            self.string_slice = string_slice
        if version is not None:
            self.version = version

    @property
    def bool(self):
        """Gets the bool of this V1alpha2NamedResourcesAttribute.  # noqa: E501

        BoolValue is a true/false value.  # noqa: E501

        :return: The bool of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: bool
        """
        return self._bool

    @bool.setter
    def bool(self, bool):
        """Sets the bool of this V1alpha2NamedResourcesAttribute.

        BoolValue is a true/false value.  # noqa: E501

        :param bool: The bool of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: bool
        """

        self._bool = bool

    @property
    def int(self):
        """Gets the int of this V1alpha2NamedResourcesAttribute.  # noqa: E501

        IntValue is a 64-bit integer.  # noqa: E501

        :return: The int of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: int
        """
        return self._int

    @int.setter
    def int(self, int):
        """Sets the int of this V1alpha2NamedResourcesAttribute.

        IntValue is a 64-bit integer.  # noqa: E501

        :param int: The int of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: int
        """

        self._int = int

    @property
    def int_slice(self):
        """Gets the int_slice of this V1alpha2NamedResourcesAttribute.  # noqa: E501


        :return: The int_slice of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: V1alpha2NamedResourcesIntSlice
        """
        return self._int_slice

    @int_slice.setter
    def int_slice(self, int_slice):
        """Sets the int_slice of this V1alpha2NamedResourcesAttribute.


        :param int_slice: The int_slice of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: V1alpha2NamedResourcesIntSlice
        """

        self._int_slice = int_slice

    @property
    def name(self):
        """Gets the name of this V1alpha2NamedResourcesAttribute.  # noqa: E501

        Name is unique identifier among all resource instances managed by the driver on the node. It must be a DNS subdomain.  # noqa: E501

        :return: The name of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this V1alpha2NamedResourcesAttribute.

        Name is unique identifier among all resource instances managed by the driver on the node. It must be a DNS subdomain.  # noqa: E501

        :param name: The name of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and name is None:  # noqa: E501
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def quantity(self):
        """Gets the quantity of this V1alpha2NamedResourcesAttribute.  # noqa: E501

        QuantityValue is a quantity.  # noqa: E501

        :return: The quantity of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: str
        """
        return self._quantity

    @quantity.setter
    def quantity(self, quantity):
        """Sets the quantity of this V1alpha2NamedResourcesAttribute.

        QuantityValue is a quantity.  # noqa: E501

        :param quantity: The quantity of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: str
        """

        self._quantity = quantity

    @property
    def string(self):
        """Gets the string of this V1alpha2NamedResourcesAttribute.  # noqa: E501

        StringValue is a string.  # noqa: E501

        :return: The string of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: str
        """
        return self._string

    @string.setter
    def string(self, string):
        """Sets the string of this V1alpha2NamedResourcesAttribute.

        StringValue is a string.  # noqa: E501

        :param string: The string of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: str
        """

        self._string = string

    @property
    def string_slice(self):
        """Gets the string_slice of this V1alpha2NamedResourcesAttribute.  # noqa: E501


        :return: The string_slice of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: V1alpha2NamedResourcesStringSlice
        """
        return self._string_slice

    @string_slice.setter
    def string_slice(self, string_slice):
        """Sets the string_slice of this V1alpha2NamedResourcesAttribute.


        :param string_slice: The string_slice of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: V1alpha2NamedResourcesStringSlice
        """

        self._string_slice = string_slice

    @property
    def version(self):
        """Gets the version of this V1alpha2NamedResourcesAttribute.  # noqa: E501

        VersionValue is a semantic version according to semver.org spec 2.0.0.  # noqa: E501

        :return: The version of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this V1alpha2NamedResourcesAttribute.

        VersionValue is a semantic version according to semver.org spec 2.0.0.  # noqa: E501

        :param version: The version of this V1alpha2NamedResourcesAttribute.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha2NamedResourcesAttribute):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha2NamedResourcesAttribute):
            return True

        return self.to_dict() != other.to_dict()
