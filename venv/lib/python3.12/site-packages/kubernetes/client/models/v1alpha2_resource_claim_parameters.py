# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.30
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha2ResourceClaimParameters(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'api_version': 'str',
        'driver_requests': 'list[V1alpha2DriverRequests]',
        'generated_from': 'V1alpha2ResourceClaimParametersReference',
        'kind': 'str',
        'metadata': 'V1ObjectMeta',
        'shareable': 'bool'
    }

    attribute_map = {
        'api_version': 'apiVersion',
        'driver_requests': 'driverRequests',
        'generated_from': 'generatedFrom',
        'kind': 'kind',
        'metadata': 'metadata',
        'shareable': 'shareable'
    }

    def __init__(self, api_version=None, driver_requests=None, generated_from=None, kind=None, metadata=None, shareable=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha2ResourceClaimParameters - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._api_version = None
        self._driver_requests = None
        self._generated_from = None
        self._kind = None
        self._metadata = None
        self._shareable = None
        self.discriminator = None

        if api_version is not None:
            self.api_version = api_version
        if driver_requests is not None:
            self.driver_requests = driver_requests
        if generated_from is not None:
            self.generated_from = generated_from
        if kind is not None:
            self.kind = kind
        if metadata is not None:
            self.metadata = metadata
        if shareable is not None:
            self.shareable = shareable

    @property
    def api_version(self):
        """Gets the api_version of this V1alpha2ResourceClaimParameters.  # noqa: E501

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :return: The api_version of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :rtype: str
        """
        return self._api_version

    @api_version.setter
    def api_version(self, api_version):
        """Sets the api_version of this V1alpha2ResourceClaimParameters.

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :param api_version: The api_version of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :type: str
        """

        self._api_version = api_version

    @property
    def driver_requests(self):
        """Gets the driver_requests of this V1alpha2ResourceClaimParameters.  # noqa: E501

        DriverRequests describes all resources that are needed for the allocated claim. A single claim may use resources coming from different drivers. For each driver, this array has at most one entry which then may have one or more per-driver requests.  May be empty, in which case the claim can always be allocated.  # noqa: E501

        :return: The driver_requests of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :rtype: list[V1alpha2DriverRequests]
        """
        return self._driver_requests

    @driver_requests.setter
    def driver_requests(self, driver_requests):
        """Sets the driver_requests of this V1alpha2ResourceClaimParameters.

        DriverRequests describes all resources that are needed for the allocated claim. A single claim may use resources coming from different drivers. For each driver, this array has at most one entry which then may have one or more per-driver requests.  May be empty, in which case the claim can always be allocated.  # noqa: E501

        :param driver_requests: The driver_requests of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :type: list[V1alpha2DriverRequests]
        """

        self._driver_requests = driver_requests

    @property
    def generated_from(self):
        """Gets the generated_from of this V1alpha2ResourceClaimParameters.  # noqa: E501


        :return: The generated_from of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :rtype: V1alpha2ResourceClaimParametersReference
        """
        return self._generated_from

    @generated_from.setter
    def generated_from(self, generated_from):
        """Sets the generated_from of this V1alpha2ResourceClaimParameters.


        :param generated_from: The generated_from of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :type: V1alpha2ResourceClaimParametersReference
        """

        self._generated_from = generated_from

    @property
    def kind(self):
        """Gets the kind of this V1alpha2ResourceClaimParameters.  # noqa: E501

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :return: The kind of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this V1alpha2ResourceClaimParameters.

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :param kind: The kind of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def metadata(self):
        """Gets the metadata of this V1alpha2ResourceClaimParameters.  # noqa: E501


        :return: The metadata of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :rtype: V1ObjectMeta
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this V1alpha2ResourceClaimParameters.


        :param metadata: The metadata of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :type: V1ObjectMeta
        """

        self._metadata = metadata

    @property
    def shareable(self):
        """Gets the shareable of this V1alpha2ResourceClaimParameters.  # noqa: E501

        Shareable indicates whether the allocated claim is meant to be shareable by multiple consumers at the same time.  # noqa: E501

        :return: The shareable of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :rtype: bool
        """
        return self._shareable

    @shareable.setter
    def shareable(self, shareable):
        """Sets the shareable of this V1alpha2ResourceClaimParameters.

        Shareable indicates whether the allocated claim is meant to be shareable by multiple consumers at the same time.  # noqa: E501

        :param shareable: The shareable of this V1alpha2ResourceClaimParameters.  # noqa: E501
        :type: bool
        """

        self._shareable = shareable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha2ResourceClaimParameters):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha2ResourceClaimParameters):
            return True

        return self.to_dict() != other.to_dict()
