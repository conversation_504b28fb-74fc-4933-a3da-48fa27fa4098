# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.31
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3PodSchedulingContextSpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'potential_nodes': 'list[str]',
        'selected_node': 'str'
    }

    attribute_map = {
        'potential_nodes': 'potentialNodes',
        'selected_node': 'selectedNode'
    }

    def __init__(self, potential_nodes=None, selected_node=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3PodSchedulingContextSpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._potential_nodes = None
        self._selected_node = None
        self.discriminator = None

        if potential_nodes is not None:
            self.potential_nodes = potential_nodes
        if selected_node is not None:
            self.selected_node = selected_node

    @property
    def potential_nodes(self):
        """Gets the potential_nodes of this V1alpha3PodSchedulingContextSpec.  # noqa: E501

        PotentialNodes lists nodes where the Pod might be able to run.  The size of this field is limited to 128. This is large enough for many clusters. Larger clusters may need more attempts to find a node that suits all pending resources. This may get increased in the future, but not reduced.  # noqa: E501

        :return: The potential_nodes of this V1alpha3PodSchedulingContextSpec.  # noqa: E501
        :rtype: list[str]
        """
        return self._potential_nodes

    @potential_nodes.setter
    def potential_nodes(self, potential_nodes):
        """Sets the potential_nodes of this V1alpha3PodSchedulingContextSpec.

        PotentialNodes lists nodes where the Pod might be able to run.  The size of this field is limited to 128. This is large enough for many clusters. Larger clusters may need more attempts to find a node that suits all pending resources. This may get increased in the future, but not reduced.  # noqa: E501

        :param potential_nodes: The potential_nodes of this V1alpha3PodSchedulingContextSpec.  # noqa: E501
        :type: list[str]
        """

        self._potential_nodes = potential_nodes

    @property
    def selected_node(self):
        """Gets the selected_node of this V1alpha3PodSchedulingContextSpec.  # noqa: E501

        SelectedNode is the node for which allocation of ResourceClaims that are referenced by the Pod and that use \"WaitForFirstConsumer\" allocation is to be attempted.  # noqa: E501

        :return: The selected_node of this V1alpha3PodSchedulingContextSpec.  # noqa: E501
        :rtype: str
        """
        return self._selected_node

    @selected_node.setter
    def selected_node(self, selected_node):
        """Sets the selected_node of this V1alpha3PodSchedulingContextSpec.

        SelectedNode is the node for which allocation of ResourceClaims that are referenced by the Pod and that use \"WaitForFirstConsumer\" allocation is to be attempted.  # noqa: E501

        :param selected_node: The selected_node of this V1alpha3PodSchedulingContextSpec.  # noqa: E501
        :type: str
        """

        self._selected_node = selected_node

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3PodSchedulingContextSpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3PodSchedulingContextSpec):
            return True

        return self.to_dict() != other.to_dict()
