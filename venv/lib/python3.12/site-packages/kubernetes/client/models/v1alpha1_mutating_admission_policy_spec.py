# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.32
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha1MutatingAdmissionPolicySpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'failure_policy': 'str',
        'match_conditions': 'list[V1alpha1MatchCondition]',
        'match_constraints': 'V1alpha1MatchResources',
        'mutations': 'list[V1alpha1Mutation]',
        'param_kind': 'V1alpha1ParamKind',
        'reinvocation_policy': 'str',
        'variables': 'list[V1alpha1Variable]'
    }

    attribute_map = {
        'failure_policy': 'failurePolicy',
        'match_conditions': 'matchConditions',
        'match_constraints': 'matchConstraints',
        'mutations': 'mutations',
        'param_kind': 'paramKind',
        'reinvocation_policy': 'reinvocationPolicy',
        'variables': 'variables'
    }

    def __init__(self, failure_policy=None, match_conditions=None, match_constraints=None, mutations=None, param_kind=None, reinvocation_policy=None, variables=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha1MutatingAdmissionPolicySpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._failure_policy = None
        self._match_conditions = None
        self._match_constraints = None
        self._mutations = None
        self._param_kind = None
        self._reinvocation_policy = None
        self._variables = None
        self.discriminator = None

        if failure_policy is not None:
            self.failure_policy = failure_policy
        if match_conditions is not None:
            self.match_conditions = match_conditions
        if match_constraints is not None:
            self.match_constraints = match_constraints
        if mutations is not None:
            self.mutations = mutations
        if param_kind is not None:
            self.param_kind = param_kind
        if reinvocation_policy is not None:
            self.reinvocation_policy = reinvocation_policy
        if variables is not None:
            self.variables = variables

    @property
    def failure_policy(self):
        """Gets the failure_policy of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501

        failurePolicy defines how to handle failures for the admission policy. Failures can occur from CEL expression parse errors, type check errors, runtime errors and invalid or mis-configured policy definitions or bindings.  A policy is invalid if paramKind refers to a non-existent Kind. A binding is invalid if paramRef.name refers to a non-existent resource.  failurePolicy does not define how validations that evaluate to false are handled.  Allowed values are Ignore or Fail. Defaults to Fail.  # noqa: E501

        :return: The failure_policy of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :rtype: str
        """
        return self._failure_policy

    @failure_policy.setter
    def failure_policy(self, failure_policy):
        """Sets the failure_policy of this V1alpha1MutatingAdmissionPolicySpec.

        failurePolicy defines how to handle failures for the admission policy. Failures can occur from CEL expression parse errors, type check errors, runtime errors and invalid or mis-configured policy definitions or bindings.  A policy is invalid if paramKind refers to a non-existent Kind. A binding is invalid if paramRef.name refers to a non-existent resource.  failurePolicy does not define how validations that evaluate to false are handled.  Allowed values are Ignore or Fail. Defaults to Fail.  # noqa: E501

        :param failure_policy: The failure_policy of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :type: str
        """

        self._failure_policy = failure_policy

    @property
    def match_conditions(self):
        """Gets the match_conditions of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501

        matchConditions is a list of conditions that must be met for a request to be validated. Match conditions filter requests that have already been matched by the matchConstraints. An empty list of matchConditions matches all requests. There are a maximum of 64 match conditions allowed.  If a parameter object is provided, it can be accessed via the `params` handle in the same manner as validation expressions.  The exact matching logic is (in order):   1. If ANY matchCondition evaluates to FALSE, the policy is skipped.   2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.   3. If any matchCondition evaluates to an error (but none are FALSE):      - If failurePolicy=Fail, reject the request      - If failurePolicy=Ignore, the policy is skipped  # noqa: E501

        :return: The match_conditions of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :rtype: list[V1alpha1MatchCondition]
        """
        return self._match_conditions

    @match_conditions.setter
    def match_conditions(self, match_conditions):
        """Sets the match_conditions of this V1alpha1MutatingAdmissionPolicySpec.

        matchConditions is a list of conditions that must be met for a request to be validated. Match conditions filter requests that have already been matched by the matchConstraints. An empty list of matchConditions matches all requests. There are a maximum of 64 match conditions allowed.  If a parameter object is provided, it can be accessed via the `params` handle in the same manner as validation expressions.  The exact matching logic is (in order):   1. If ANY matchCondition evaluates to FALSE, the policy is skipped.   2. If ALL matchConditions evaluate to TRUE, the policy is evaluated.   3. If any matchCondition evaluates to an error (but none are FALSE):      - If failurePolicy=Fail, reject the request      - If failurePolicy=Ignore, the policy is skipped  # noqa: E501

        :param match_conditions: The match_conditions of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :type: list[V1alpha1MatchCondition]
        """

        self._match_conditions = match_conditions

    @property
    def match_constraints(self):
        """Gets the match_constraints of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501


        :return: The match_constraints of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :rtype: V1alpha1MatchResources
        """
        return self._match_constraints

    @match_constraints.setter
    def match_constraints(self, match_constraints):
        """Sets the match_constraints of this V1alpha1MutatingAdmissionPolicySpec.


        :param match_constraints: The match_constraints of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :type: V1alpha1MatchResources
        """

        self._match_constraints = match_constraints

    @property
    def mutations(self):
        """Gets the mutations of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501

        mutations contain operations to perform on matching objects. mutations may not be empty; a minimum of one mutation is required. mutations are evaluated in order, and are reinvoked according to the reinvocationPolicy. The mutations of a policy are invoked for each binding of this policy and reinvocation of mutations occurs on a per binding basis.  # noqa: E501

        :return: The mutations of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :rtype: list[V1alpha1Mutation]
        """
        return self._mutations

    @mutations.setter
    def mutations(self, mutations):
        """Sets the mutations of this V1alpha1MutatingAdmissionPolicySpec.

        mutations contain operations to perform on matching objects. mutations may not be empty; a minimum of one mutation is required. mutations are evaluated in order, and are reinvoked according to the reinvocationPolicy. The mutations of a policy are invoked for each binding of this policy and reinvocation of mutations occurs on a per binding basis.  # noqa: E501

        :param mutations: The mutations of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :type: list[V1alpha1Mutation]
        """

        self._mutations = mutations

    @property
    def param_kind(self):
        """Gets the param_kind of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501


        :return: The param_kind of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :rtype: V1alpha1ParamKind
        """
        return self._param_kind

    @param_kind.setter
    def param_kind(self, param_kind):
        """Sets the param_kind of this V1alpha1MutatingAdmissionPolicySpec.


        :param param_kind: The param_kind of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :type: V1alpha1ParamKind
        """

        self._param_kind = param_kind

    @property
    def reinvocation_policy(self):
        """Gets the reinvocation_policy of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501

        reinvocationPolicy indicates whether mutations may be called multiple times per MutatingAdmissionPolicyBinding as part of a single admission evaluation. Allowed values are \"Never\" and \"IfNeeded\".  Never: These mutations will not be called more than once per binding in a single admission evaluation.  IfNeeded: These mutations may be invoked more than once per binding for a single admission request and there is no guarantee of order with respect to other admission plugins, admission webhooks, bindings of this policy and admission policies.  Mutations are only reinvoked when mutations change the object after this mutation is invoked. Required.  # noqa: E501

        :return: The reinvocation_policy of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :rtype: str
        """
        return self._reinvocation_policy

    @reinvocation_policy.setter
    def reinvocation_policy(self, reinvocation_policy):
        """Sets the reinvocation_policy of this V1alpha1MutatingAdmissionPolicySpec.

        reinvocationPolicy indicates whether mutations may be called multiple times per MutatingAdmissionPolicyBinding as part of a single admission evaluation. Allowed values are \"Never\" and \"IfNeeded\".  Never: These mutations will not be called more than once per binding in a single admission evaluation.  IfNeeded: These mutations may be invoked more than once per binding for a single admission request and there is no guarantee of order with respect to other admission plugins, admission webhooks, bindings of this policy and admission policies.  Mutations are only reinvoked when mutations change the object after this mutation is invoked. Required.  # noqa: E501

        :param reinvocation_policy: The reinvocation_policy of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :type: str
        """

        self._reinvocation_policy = reinvocation_policy

    @property
    def variables(self):
        """Gets the variables of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501

        variables contain definitions of variables that can be used in composition of other expressions. Each variable is defined as a named CEL expression. The variables defined here will be available under `variables` in other expressions of the policy except matchConditions because matchConditions are evaluated before the rest of the policy.  The expression of a variable can refer to other variables defined earlier in the list but not those after. Thus, variables must be sorted by the order of first appearance and acyclic.  # noqa: E501

        :return: The variables of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :rtype: list[V1alpha1Variable]
        """
        return self._variables

    @variables.setter
    def variables(self, variables):
        """Sets the variables of this V1alpha1MutatingAdmissionPolicySpec.

        variables contain definitions of variables that can be used in composition of other expressions. Each variable is defined as a named CEL expression. The variables defined here will be available under `variables` in other expressions of the policy except matchConditions because matchConditions are evaluated before the rest of the policy.  The expression of a variable can refer to other variables defined earlier in the list but not those after. Thus, variables must be sorted by the order of first appearance and acyclic.  # noqa: E501

        :param variables: The variables of this V1alpha1MutatingAdmissionPolicySpec.  # noqa: E501
        :type: list[V1alpha1Variable]
        """

        self._variables = variables

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha1MutatingAdmissionPolicySpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha1MutatingAdmissionPolicySpec):
            return True

        return self.to_dict() != other.to_dict()
