# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.32
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1JSONSchemaProps(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'ref': 'str',
        'schema': 'str',
        'additional_items': 'object',
        'additional_properties': 'object',
        'all_of': 'list[V1JSONSchemaProps]',
        'any_of': 'list[V1JSONSchemaProps]',
        'default': 'object',
        'definitions': 'dict(str, V1JSONSchemaProps)',
        'dependencies': 'dict(str, object)',
        'description': 'str',
        'enum': 'list[object]',
        'example': 'object',
        'exclusive_maximum': 'bool',
        'exclusive_minimum': 'bool',
        'external_docs': 'V1ExternalDocumentation',
        'format': 'str',
        'id': 'str',
        'items': 'object',
        'max_items': 'int',
        'max_length': 'int',
        'max_properties': 'int',
        'maximum': 'float',
        'min_items': 'int',
        'min_length': 'int',
        'min_properties': 'int',
        'minimum': 'float',
        'multiple_of': 'float',
        '_not': 'V1JSONSchemaProps',
        'nullable': 'bool',
        'one_of': 'list[V1JSONSchemaProps]',
        'pattern': 'str',
        'pattern_properties': 'dict(str, V1JSONSchemaProps)',
        'properties': 'dict(str, V1JSONSchemaProps)',
        'required': 'list[str]',
        'title': 'str',
        'type': 'str',
        'unique_items': 'bool',
        'x_kubernetes_embedded_resource': 'bool',
        'x_kubernetes_int_or_string': 'bool',
        'x_kubernetes_list_map_keys': 'list[str]',
        'x_kubernetes_list_type': 'str',
        'x_kubernetes_map_type': 'str',
        'x_kubernetes_preserve_unknown_fields': 'bool',
        'x_kubernetes_validations': 'list[V1ValidationRule]'
    }

    attribute_map = {
        'ref': '$ref',
        'schema': '$schema',
        'additional_items': 'additionalItems',
        'additional_properties': 'additionalProperties',
        'all_of': 'allOf',
        'any_of': 'anyOf',
        'default': 'default',
        'definitions': 'definitions',
        'dependencies': 'dependencies',
        'description': 'description',
        'enum': 'enum',
        'example': 'example',
        'exclusive_maximum': 'exclusiveMaximum',
        'exclusive_minimum': 'exclusiveMinimum',
        'external_docs': 'externalDocs',
        'format': 'format',
        'id': 'id',
        'items': 'items',
        'max_items': 'maxItems',
        'max_length': 'maxLength',
        'max_properties': 'maxProperties',
        'maximum': 'maximum',
        'min_items': 'minItems',
        'min_length': 'minLength',
        'min_properties': 'minProperties',
        'minimum': 'minimum',
        'multiple_of': 'multipleOf',
        '_not': 'not',
        'nullable': 'nullable',
        'one_of': 'oneOf',
        'pattern': 'pattern',
        'pattern_properties': 'patternProperties',
        'properties': 'properties',
        'required': 'required',
        'title': 'title',
        'type': 'type',
        'unique_items': 'uniqueItems',
        'x_kubernetes_embedded_resource': 'x-kubernetes-embedded-resource',
        'x_kubernetes_int_or_string': 'x-kubernetes-int-or-string',
        'x_kubernetes_list_map_keys': 'x-kubernetes-list-map-keys',
        'x_kubernetes_list_type': 'x-kubernetes-list-type',
        'x_kubernetes_map_type': 'x-kubernetes-map-type',
        'x_kubernetes_preserve_unknown_fields': 'x-kubernetes-preserve-unknown-fields',
        'x_kubernetes_validations': 'x-kubernetes-validations'
    }

    def __init__(self, ref=None, schema=None, additional_items=None, additional_properties=None, all_of=None, any_of=None, default=None, definitions=None, dependencies=None, description=None, enum=None, example=None, exclusive_maximum=None, exclusive_minimum=None, external_docs=None, format=None, id=None, items=None, max_items=None, max_length=None, max_properties=None, maximum=None, min_items=None, min_length=None, min_properties=None, minimum=None, multiple_of=None, _not=None, nullable=None, one_of=None, pattern=None, pattern_properties=None, properties=None, required=None, title=None, type=None, unique_items=None, x_kubernetes_embedded_resource=None, x_kubernetes_int_or_string=None, x_kubernetes_list_map_keys=None, x_kubernetes_list_type=None, x_kubernetes_map_type=None, x_kubernetes_preserve_unknown_fields=None, x_kubernetes_validations=None, local_vars_configuration=None):  # noqa: E501
        """V1JSONSchemaProps - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._ref = None
        self._schema = None
        self._additional_items = None
        self._additional_properties = None
        self._all_of = None
        self._any_of = None
        self._default = None
        self._definitions = None
        self._dependencies = None
        self._description = None
        self._enum = None
        self._example = None
        self._exclusive_maximum = None
        self._exclusive_minimum = None
        self._external_docs = None
        self._format = None
        self._id = None
        self._items = None
        self._max_items = None
        self._max_length = None
        self._max_properties = None
        self._maximum = None
        self._min_items = None
        self._min_length = None
        self._min_properties = None
        self._minimum = None
        self._multiple_of = None
        self.__not = None
        self._nullable = None
        self._one_of = None
        self._pattern = None
        self._pattern_properties = None
        self._properties = None
        self._required = None
        self._title = None
        self._type = None
        self._unique_items = None
        self._x_kubernetes_embedded_resource = None
        self._x_kubernetes_int_or_string = None
        self._x_kubernetes_list_map_keys = None
        self._x_kubernetes_list_type = None
        self._x_kubernetes_map_type = None
        self._x_kubernetes_preserve_unknown_fields = None
        self._x_kubernetes_validations = None
        self.discriminator = None

        if ref is not None:
            self.ref = ref
        if schema is not None:
            self.schema = schema
        if additional_items is not None:
            self.additional_items = additional_items
        if additional_properties is not None:
            self.additional_properties = additional_properties
        if all_of is not None:
            self.all_of = all_of
        if any_of is not None:
            self.any_of = any_of
        if default is not None:
            self.default = default
        if definitions is not None:
            self.definitions = definitions
        if dependencies is not None:
            self.dependencies = dependencies
        if description is not None:
            self.description = description
        if enum is not None:
            self.enum = enum
        if example is not None:
            self.example = example
        if exclusive_maximum is not None:
            self.exclusive_maximum = exclusive_maximum
        if exclusive_minimum is not None:
            self.exclusive_minimum = exclusive_minimum
        if external_docs is not None:
            self.external_docs = external_docs
        if format is not None:
            self.format = format
        if id is not None:
            self.id = id
        if items is not None:
            self.items = items
        if max_items is not None:
            self.max_items = max_items
        if max_length is not None:
            self.max_length = max_length
        if max_properties is not None:
            self.max_properties = max_properties
        if maximum is not None:
            self.maximum = maximum
        if min_items is not None:
            self.min_items = min_items
        if min_length is not None:
            self.min_length = min_length
        if min_properties is not None:
            self.min_properties = min_properties
        if minimum is not None:
            self.minimum = minimum
        if multiple_of is not None:
            self.multiple_of = multiple_of
        if _not is not None:
            self._not = _not
        if nullable is not None:
            self.nullable = nullable
        if one_of is not None:
            self.one_of = one_of
        if pattern is not None:
            self.pattern = pattern
        if pattern_properties is not None:
            self.pattern_properties = pattern_properties
        if properties is not None:
            self.properties = properties
        if required is not None:
            self.required = required
        if title is not None:
            self.title = title
        if type is not None:
            self.type = type
        if unique_items is not None:
            self.unique_items = unique_items
        if x_kubernetes_embedded_resource is not None:
            self.x_kubernetes_embedded_resource = x_kubernetes_embedded_resource
        if x_kubernetes_int_or_string is not None:
            self.x_kubernetes_int_or_string = x_kubernetes_int_or_string
        if x_kubernetes_list_map_keys is not None:
            self.x_kubernetes_list_map_keys = x_kubernetes_list_map_keys
        if x_kubernetes_list_type is not None:
            self.x_kubernetes_list_type = x_kubernetes_list_type
        if x_kubernetes_map_type is not None:
            self.x_kubernetes_map_type = x_kubernetes_map_type
        if x_kubernetes_preserve_unknown_fields is not None:
            self.x_kubernetes_preserve_unknown_fields = x_kubernetes_preserve_unknown_fields
        if x_kubernetes_validations is not None:
            self.x_kubernetes_validations = x_kubernetes_validations

    @property
    def ref(self):
        """Gets the ref of this V1JSONSchemaProps.  # noqa: E501


        :return: The ref of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._ref

    @ref.setter
    def ref(self, ref):
        """Sets the ref of this V1JSONSchemaProps.


        :param ref: The ref of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._ref = ref

    @property
    def schema(self):
        """Gets the schema of this V1JSONSchemaProps.  # noqa: E501


        :return: The schema of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._schema

    @schema.setter
    def schema(self, schema):
        """Sets the schema of this V1JSONSchemaProps.


        :param schema: The schema of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._schema = schema

    @property
    def additional_items(self):
        """Gets the additional_items of this V1JSONSchemaProps.  # noqa: E501

        JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value. Defaults to true for the boolean property.  # noqa: E501

        :return: The additional_items of this V1JSONSchemaProps.  # noqa: E501
        :rtype: object
        """
        return self._additional_items

    @additional_items.setter
    def additional_items(self, additional_items):
        """Sets the additional_items of this V1JSONSchemaProps.

        JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value. Defaults to true for the boolean property.  # noqa: E501

        :param additional_items: The additional_items of this V1JSONSchemaProps.  # noqa: E501
        :type: object
        """

        self._additional_items = additional_items

    @property
    def additional_properties(self):
        """Gets the additional_properties of this V1JSONSchemaProps.  # noqa: E501

        JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value. Defaults to true for the boolean property.  # noqa: E501

        :return: The additional_properties of this V1JSONSchemaProps.  # noqa: E501
        :rtype: object
        """
        return self._additional_properties

    @additional_properties.setter
    def additional_properties(self, additional_properties):
        """Sets the additional_properties of this V1JSONSchemaProps.

        JSONSchemaPropsOrBool represents JSONSchemaProps or a boolean value. Defaults to true for the boolean property.  # noqa: E501

        :param additional_properties: The additional_properties of this V1JSONSchemaProps.  # noqa: E501
        :type: object
        """

        self._additional_properties = additional_properties

    @property
    def all_of(self):
        """Gets the all_of of this V1JSONSchemaProps.  # noqa: E501


        :return: The all_of of this V1JSONSchemaProps.  # noqa: E501
        :rtype: list[V1JSONSchemaProps]
        """
        return self._all_of

    @all_of.setter
    def all_of(self, all_of):
        """Sets the all_of of this V1JSONSchemaProps.


        :param all_of: The all_of of this V1JSONSchemaProps.  # noqa: E501
        :type: list[V1JSONSchemaProps]
        """

        self._all_of = all_of

    @property
    def any_of(self):
        """Gets the any_of of this V1JSONSchemaProps.  # noqa: E501


        :return: The any_of of this V1JSONSchemaProps.  # noqa: E501
        :rtype: list[V1JSONSchemaProps]
        """
        return self._any_of

    @any_of.setter
    def any_of(self, any_of):
        """Sets the any_of of this V1JSONSchemaProps.


        :param any_of: The any_of of this V1JSONSchemaProps.  # noqa: E501
        :type: list[V1JSONSchemaProps]
        """

        self._any_of = any_of

    @property
    def default(self):
        """Gets the default of this V1JSONSchemaProps.  # noqa: E501

        default is a default value for undefined object fields. Defaulting is a beta feature under the CustomResourceDefaulting feature gate. Defaulting requires spec.preserveUnknownFields to be false.  # noqa: E501

        :return: The default of this V1JSONSchemaProps.  # noqa: E501
        :rtype: object
        """
        return self._default

    @default.setter
    def default(self, default):
        """Sets the default of this V1JSONSchemaProps.

        default is a default value for undefined object fields. Defaulting is a beta feature under the CustomResourceDefaulting feature gate. Defaulting requires spec.preserveUnknownFields to be false.  # noqa: E501

        :param default: The default of this V1JSONSchemaProps.  # noqa: E501
        :type: object
        """

        self._default = default

    @property
    def definitions(self):
        """Gets the definitions of this V1JSONSchemaProps.  # noqa: E501


        :return: The definitions of this V1JSONSchemaProps.  # noqa: E501
        :rtype: dict(str, V1JSONSchemaProps)
        """
        return self._definitions

    @definitions.setter
    def definitions(self, definitions):
        """Sets the definitions of this V1JSONSchemaProps.


        :param definitions: The definitions of this V1JSONSchemaProps.  # noqa: E501
        :type: dict(str, V1JSONSchemaProps)
        """

        self._definitions = definitions

    @property
    def dependencies(self):
        """Gets the dependencies of this V1JSONSchemaProps.  # noqa: E501


        :return: The dependencies of this V1JSONSchemaProps.  # noqa: E501
        :rtype: dict(str, object)
        """
        return self._dependencies

    @dependencies.setter
    def dependencies(self, dependencies):
        """Sets the dependencies of this V1JSONSchemaProps.


        :param dependencies: The dependencies of this V1JSONSchemaProps.  # noqa: E501
        :type: dict(str, object)
        """

        self._dependencies = dependencies

    @property
    def description(self):
        """Gets the description of this V1JSONSchemaProps.  # noqa: E501


        :return: The description of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this V1JSONSchemaProps.


        :param description: The description of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enum(self):
        """Gets the enum of this V1JSONSchemaProps.  # noqa: E501


        :return: The enum of this V1JSONSchemaProps.  # noqa: E501
        :rtype: list[object]
        """
        return self._enum

    @enum.setter
    def enum(self, enum):
        """Sets the enum of this V1JSONSchemaProps.


        :param enum: The enum of this V1JSONSchemaProps.  # noqa: E501
        :type: list[object]
        """

        self._enum = enum

    @property
    def example(self):
        """Gets the example of this V1JSONSchemaProps.  # noqa: E501

        JSON represents any valid JSON value. These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil.  # noqa: E501

        :return: The example of this V1JSONSchemaProps.  # noqa: E501
        :rtype: object
        """
        return self._example

    @example.setter
    def example(self, example):
        """Sets the example of this V1JSONSchemaProps.

        JSON represents any valid JSON value. These types are supported: bool, int64, float64, string, []interface{}, map[string]interface{} and nil.  # noqa: E501

        :param example: The example of this V1JSONSchemaProps.  # noqa: E501
        :type: object
        """

        self._example = example

    @property
    def exclusive_maximum(self):
        """Gets the exclusive_maximum of this V1JSONSchemaProps.  # noqa: E501


        :return: The exclusive_maximum of this V1JSONSchemaProps.  # noqa: E501
        :rtype: bool
        """
        return self._exclusive_maximum

    @exclusive_maximum.setter
    def exclusive_maximum(self, exclusive_maximum):
        """Sets the exclusive_maximum of this V1JSONSchemaProps.


        :param exclusive_maximum: The exclusive_maximum of this V1JSONSchemaProps.  # noqa: E501
        :type: bool
        """

        self._exclusive_maximum = exclusive_maximum

    @property
    def exclusive_minimum(self):
        """Gets the exclusive_minimum of this V1JSONSchemaProps.  # noqa: E501


        :return: The exclusive_minimum of this V1JSONSchemaProps.  # noqa: E501
        :rtype: bool
        """
        return self._exclusive_minimum

    @exclusive_minimum.setter
    def exclusive_minimum(self, exclusive_minimum):
        """Sets the exclusive_minimum of this V1JSONSchemaProps.


        :param exclusive_minimum: The exclusive_minimum of this V1JSONSchemaProps.  # noqa: E501
        :type: bool
        """

        self._exclusive_minimum = exclusive_minimum

    @property
    def external_docs(self):
        """Gets the external_docs of this V1JSONSchemaProps.  # noqa: E501


        :return: The external_docs of this V1JSONSchemaProps.  # noqa: E501
        :rtype: V1ExternalDocumentation
        """
        return self._external_docs

    @external_docs.setter
    def external_docs(self, external_docs):
        """Sets the external_docs of this V1JSONSchemaProps.


        :param external_docs: The external_docs of this V1JSONSchemaProps.  # noqa: E501
        :type: V1ExternalDocumentation
        """

        self._external_docs = external_docs

    @property
    def format(self):
        """Gets the format of this V1JSONSchemaProps.  # noqa: E501

        format is an OpenAPI v3 format string. Unknown formats are ignored. The following formats are validated:  - bsonobjectid: a bson object ID, i.e. a 24 characters hex string - uri: an URI as parsed by Golang net/url.ParseRequestURI - email: an email address as parsed by Golang net/mail.ParseAddress - hostname: a valid representation for an Internet host name, as defined by RFC 1034, section 3.1 [RFC1034]. - ipv4: an IPv4 IP as parsed by Golang net.ParseIP - ipv6: an IPv6 IP as parsed by Golang net.ParseIP - cidr: a CIDR as parsed by Golang net.ParseCIDR - mac: a MAC address as parsed by Golang net.ParseMAC - uuid: an UUID that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12}$ - uuid3: an UUID3 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?3[0-9a-f]{3}-?[0-9a-f]{4}-?[0-9a-f]{12}$ - uuid4: an UUID4 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?4[0-9a-f]{3}-?[89ab][0-9a-f]{3}-?[0-9a-f]{12}$ - uuid5: an UUID5 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?5[0-9a-f]{3}-?[89ab][0-9a-f]{3}-?[0-9a-f]{12}$ - isbn: an ISBN10 or ISBN13 number string like \"0321751043\" or \"978-0321751041\" - isbn10: an ISBN10 number string like \"0321751043\" - isbn13: an ISBN13 number string like \"978-0321751041\" - creditcard: a credit card number defined by the regex ^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\\\\d{3})\\\\d{11})$ with any non digit characters mixed in - ssn: a U.S. social security number following the regex ^\\\\d{3}[- ]?\\\\d{2}[- ]?\\\\d{4}$ - hexcolor: an hexadecimal color code like \"#FFFFFF: following the regex ^#?([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$ - rgbcolor: an RGB color code like rgb like \"rgb(255,255,2559\" - byte: base64 encoded binary data - password: any kind of string - date: a date string like \"2006-01-02\" as defined by full-date in RFC3339 - duration: a duration string like \"22 ns\" as parsed by Golang time.ParseDuration or compatible with Scala duration format - datetime: a date time string like \"2014-12-15T19:30:20.000Z\" as defined by date-time in RFC3339.  # noqa: E501

        :return: The format of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._format

    @format.setter
    def format(self, format):
        """Sets the format of this V1JSONSchemaProps.

        format is an OpenAPI v3 format string. Unknown formats are ignored. The following formats are validated:  - bsonobjectid: a bson object ID, i.e. a 24 characters hex string - uri: an URI as parsed by Golang net/url.ParseRequestURI - email: an email address as parsed by Golang net/mail.ParseAddress - hostname: a valid representation for an Internet host name, as defined by RFC 1034, section 3.1 [RFC1034]. - ipv4: an IPv4 IP as parsed by Golang net.ParseIP - ipv6: an IPv6 IP as parsed by Golang net.ParseIP - cidr: a CIDR as parsed by Golang net.ParseCIDR - mac: a MAC address as parsed by Golang net.ParseMAC - uuid: an UUID that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12}$ - uuid3: an UUID3 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?3[0-9a-f]{3}-?[0-9a-f]{4}-?[0-9a-f]{12}$ - uuid4: an UUID4 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?4[0-9a-f]{3}-?[89ab][0-9a-f]{3}-?[0-9a-f]{12}$ - uuid5: an UUID5 that allows uppercase defined by the regex (?i)^[0-9a-f]{8}-?[0-9a-f]{4}-?5[0-9a-f]{3}-?[89ab][0-9a-f]{3}-?[0-9a-f]{12}$ - isbn: an ISBN10 or ISBN13 number string like \"0321751043\" or \"978-0321751041\" - isbn10: an ISBN10 number string like \"0321751043\" - isbn13: an ISBN13 number string like \"978-0321751041\" - creditcard: a credit card number defined by the regex ^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\\\\d{3})\\\\d{11})$ with any non digit characters mixed in - ssn: a U.S. social security number following the regex ^\\\\d{3}[- ]?\\\\d{2}[- ]?\\\\d{4}$ - hexcolor: an hexadecimal color code like \"#FFFFFF: following the regex ^#?([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$ - rgbcolor: an RGB color code like rgb like \"rgb(255,255,2559\" - byte: base64 encoded binary data - password: any kind of string - date: a date string like \"2006-01-02\" as defined by full-date in RFC3339 - duration: a duration string like \"22 ns\" as parsed by Golang time.ParseDuration or compatible with Scala duration format - datetime: a date time string like \"2014-12-15T19:30:20.000Z\" as defined by date-time in RFC3339.  # noqa: E501

        :param format: The format of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._format = format

    @property
    def id(self):
        """Gets the id of this V1JSONSchemaProps.  # noqa: E501


        :return: The id of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this V1JSONSchemaProps.


        :param id: The id of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def items(self):
        """Gets the items of this V1JSONSchemaProps.  # noqa: E501

        JSONSchemaPropsOrArray represents a value that can either be a JSONSchemaProps or an array of JSONSchemaProps. Mainly here for serialization purposes.  # noqa: E501

        :return: The items of this V1JSONSchemaProps.  # noqa: E501
        :rtype: object
        """
        return self._items

    @items.setter
    def items(self, items):
        """Sets the items of this V1JSONSchemaProps.

        JSONSchemaPropsOrArray represents a value that can either be a JSONSchemaProps or an array of JSONSchemaProps. Mainly here for serialization purposes.  # noqa: E501

        :param items: The items of this V1JSONSchemaProps.  # noqa: E501
        :type: object
        """

        self._items = items

    @property
    def max_items(self):
        """Gets the max_items of this V1JSONSchemaProps.  # noqa: E501


        :return: The max_items of this V1JSONSchemaProps.  # noqa: E501
        :rtype: int
        """
        return self._max_items

    @max_items.setter
    def max_items(self, max_items):
        """Sets the max_items of this V1JSONSchemaProps.


        :param max_items: The max_items of this V1JSONSchemaProps.  # noqa: E501
        :type: int
        """

        self._max_items = max_items

    @property
    def max_length(self):
        """Gets the max_length of this V1JSONSchemaProps.  # noqa: E501


        :return: The max_length of this V1JSONSchemaProps.  # noqa: E501
        :rtype: int
        """
        return self._max_length

    @max_length.setter
    def max_length(self, max_length):
        """Sets the max_length of this V1JSONSchemaProps.


        :param max_length: The max_length of this V1JSONSchemaProps.  # noqa: E501
        :type: int
        """

        self._max_length = max_length

    @property
    def max_properties(self):
        """Gets the max_properties of this V1JSONSchemaProps.  # noqa: E501


        :return: The max_properties of this V1JSONSchemaProps.  # noqa: E501
        :rtype: int
        """
        return self._max_properties

    @max_properties.setter
    def max_properties(self, max_properties):
        """Sets the max_properties of this V1JSONSchemaProps.


        :param max_properties: The max_properties of this V1JSONSchemaProps.  # noqa: E501
        :type: int
        """

        self._max_properties = max_properties

    @property
    def maximum(self):
        """Gets the maximum of this V1JSONSchemaProps.  # noqa: E501


        :return: The maximum of this V1JSONSchemaProps.  # noqa: E501
        :rtype: float
        """
        return self._maximum

    @maximum.setter
    def maximum(self, maximum):
        """Sets the maximum of this V1JSONSchemaProps.


        :param maximum: The maximum of this V1JSONSchemaProps.  # noqa: E501
        :type: float
        """

        self._maximum = maximum

    @property
    def min_items(self):
        """Gets the min_items of this V1JSONSchemaProps.  # noqa: E501


        :return: The min_items of this V1JSONSchemaProps.  # noqa: E501
        :rtype: int
        """
        return self._min_items

    @min_items.setter
    def min_items(self, min_items):
        """Sets the min_items of this V1JSONSchemaProps.


        :param min_items: The min_items of this V1JSONSchemaProps.  # noqa: E501
        :type: int
        """

        self._min_items = min_items

    @property
    def min_length(self):
        """Gets the min_length of this V1JSONSchemaProps.  # noqa: E501


        :return: The min_length of this V1JSONSchemaProps.  # noqa: E501
        :rtype: int
        """
        return self._min_length

    @min_length.setter
    def min_length(self, min_length):
        """Sets the min_length of this V1JSONSchemaProps.


        :param min_length: The min_length of this V1JSONSchemaProps.  # noqa: E501
        :type: int
        """

        self._min_length = min_length

    @property
    def min_properties(self):
        """Gets the min_properties of this V1JSONSchemaProps.  # noqa: E501


        :return: The min_properties of this V1JSONSchemaProps.  # noqa: E501
        :rtype: int
        """
        return self._min_properties

    @min_properties.setter
    def min_properties(self, min_properties):
        """Sets the min_properties of this V1JSONSchemaProps.


        :param min_properties: The min_properties of this V1JSONSchemaProps.  # noqa: E501
        :type: int
        """

        self._min_properties = min_properties

    @property
    def minimum(self):
        """Gets the minimum of this V1JSONSchemaProps.  # noqa: E501


        :return: The minimum of this V1JSONSchemaProps.  # noqa: E501
        :rtype: float
        """
        return self._minimum

    @minimum.setter
    def minimum(self, minimum):
        """Sets the minimum of this V1JSONSchemaProps.


        :param minimum: The minimum of this V1JSONSchemaProps.  # noqa: E501
        :type: float
        """

        self._minimum = minimum

    @property
    def multiple_of(self):
        """Gets the multiple_of of this V1JSONSchemaProps.  # noqa: E501


        :return: The multiple_of of this V1JSONSchemaProps.  # noqa: E501
        :rtype: float
        """
        return self._multiple_of

    @multiple_of.setter
    def multiple_of(self, multiple_of):
        """Sets the multiple_of of this V1JSONSchemaProps.


        :param multiple_of: The multiple_of of this V1JSONSchemaProps.  # noqa: E501
        :type: float
        """

        self._multiple_of = multiple_of

    @property
    def _not(self):
        """Gets the _not of this V1JSONSchemaProps.  # noqa: E501


        :return: The _not of this V1JSONSchemaProps.  # noqa: E501
        :rtype: V1JSONSchemaProps
        """
        return self.__not

    @_not.setter
    def _not(self, _not):
        """Sets the _not of this V1JSONSchemaProps.


        :param _not: The _not of this V1JSONSchemaProps.  # noqa: E501
        :type: V1JSONSchemaProps
        """

        self.__not = _not

    @property
    def nullable(self):
        """Gets the nullable of this V1JSONSchemaProps.  # noqa: E501


        :return: The nullable of this V1JSONSchemaProps.  # noqa: E501
        :rtype: bool
        """
        return self._nullable

    @nullable.setter
    def nullable(self, nullable):
        """Sets the nullable of this V1JSONSchemaProps.


        :param nullable: The nullable of this V1JSONSchemaProps.  # noqa: E501
        :type: bool
        """

        self._nullable = nullable

    @property
    def one_of(self):
        """Gets the one_of of this V1JSONSchemaProps.  # noqa: E501


        :return: The one_of of this V1JSONSchemaProps.  # noqa: E501
        :rtype: list[V1JSONSchemaProps]
        """
        return self._one_of

    @one_of.setter
    def one_of(self, one_of):
        """Sets the one_of of this V1JSONSchemaProps.


        :param one_of: The one_of of this V1JSONSchemaProps.  # noqa: E501
        :type: list[V1JSONSchemaProps]
        """

        self._one_of = one_of

    @property
    def pattern(self):
        """Gets the pattern of this V1JSONSchemaProps.  # noqa: E501


        :return: The pattern of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._pattern

    @pattern.setter
    def pattern(self, pattern):
        """Sets the pattern of this V1JSONSchemaProps.


        :param pattern: The pattern of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._pattern = pattern

    @property
    def pattern_properties(self):
        """Gets the pattern_properties of this V1JSONSchemaProps.  # noqa: E501


        :return: The pattern_properties of this V1JSONSchemaProps.  # noqa: E501
        :rtype: dict(str, V1JSONSchemaProps)
        """
        return self._pattern_properties

    @pattern_properties.setter
    def pattern_properties(self, pattern_properties):
        """Sets the pattern_properties of this V1JSONSchemaProps.


        :param pattern_properties: The pattern_properties of this V1JSONSchemaProps.  # noqa: E501
        :type: dict(str, V1JSONSchemaProps)
        """

        self._pattern_properties = pattern_properties

    @property
    def properties(self):
        """Gets the properties of this V1JSONSchemaProps.  # noqa: E501


        :return: The properties of this V1JSONSchemaProps.  # noqa: E501
        :rtype: dict(str, V1JSONSchemaProps)
        """
        return self._properties

    @properties.setter
    def properties(self, properties):
        """Sets the properties of this V1JSONSchemaProps.


        :param properties: The properties of this V1JSONSchemaProps.  # noqa: E501
        :type: dict(str, V1JSONSchemaProps)
        """

        self._properties = properties

    @property
    def required(self):
        """Gets the required of this V1JSONSchemaProps.  # noqa: E501


        :return: The required of this V1JSONSchemaProps.  # noqa: E501
        :rtype: list[str]
        """
        return self._required

    @required.setter
    def required(self, required):
        """Sets the required of this V1JSONSchemaProps.


        :param required: The required of this V1JSONSchemaProps.  # noqa: E501
        :type: list[str]
        """

        self._required = required

    @property
    def title(self):
        """Gets the title of this V1JSONSchemaProps.  # noqa: E501


        :return: The title of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this V1JSONSchemaProps.


        :param title: The title of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def type(self):
        """Gets the type of this V1JSONSchemaProps.  # noqa: E501


        :return: The type of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this V1JSONSchemaProps.


        :param type: The type of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def unique_items(self):
        """Gets the unique_items of this V1JSONSchemaProps.  # noqa: E501


        :return: The unique_items of this V1JSONSchemaProps.  # noqa: E501
        :rtype: bool
        """
        return self._unique_items

    @unique_items.setter
    def unique_items(self, unique_items):
        """Sets the unique_items of this V1JSONSchemaProps.


        :param unique_items: The unique_items of this V1JSONSchemaProps.  # noqa: E501
        :type: bool
        """

        self._unique_items = unique_items

    @property
    def x_kubernetes_embedded_resource(self):
        """Gets the x_kubernetes_embedded_resource of this V1JSONSchemaProps.  # noqa: E501

        x-kubernetes-embedded-resource defines that the value is an embedded Kubernetes runtime.Object, with TypeMeta and ObjectMeta. The type must be object. It is allowed to further restrict the embedded object. kind, apiVersion and metadata are validated automatically. x-kubernetes-preserve-unknown-fields is allowed to be true, but does not have to be if the object is fully specified (up to kind, apiVersion, metadata).  # noqa: E501

        :return: The x_kubernetes_embedded_resource of this V1JSONSchemaProps.  # noqa: E501
        :rtype: bool
        """
        return self._x_kubernetes_embedded_resource

    @x_kubernetes_embedded_resource.setter
    def x_kubernetes_embedded_resource(self, x_kubernetes_embedded_resource):
        """Sets the x_kubernetes_embedded_resource of this V1JSONSchemaProps.

        x-kubernetes-embedded-resource defines that the value is an embedded Kubernetes runtime.Object, with TypeMeta and ObjectMeta. The type must be object. It is allowed to further restrict the embedded object. kind, apiVersion and metadata are validated automatically. x-kubernetes-preserve-unknown-fields is allowed to be true, but does not have to be if the object is fully specified (up to kind, apiVersion, metadata).  # noqa: E501

        :param x_kubernetes_embedded_resource: The x_kubernetes_embedded_resource of this V1JSONSchemaProps.  # noqa: E501
        :type: bool
        """

        self._x_kubernetes_embedded_resource = x_kubernetes_embedded_resource

    @property
    def x_kubernetes_int_or_string(self):
        """Gets the x_kubernetes_int_or_string of this V1JSONSchemaProps.  # noqa: E501

        x-kubernetes-int-or-string specifies that this value is either an integer or a string. If this is true, an empty type is allowed and type as child of anyOf is permitted if following one of the following patterns:  1) anyOf:    - type: integer    - type: string 2) allOf:    - anyOf:      - type: integer      - type: string    - ... zero or more  # noqa: E501

        :return: The x_kubernetes_int_or_string of this V1JSONSchemaProps.  # noqa: E501
        :rtype: bool
        """
        return self._x_kubernetes_int_or_string

    @x_kubernetes_int_or_string.setter
    def x_kubernetes_int_or_string(self, x_kubernetes_int_or_string):
        """Sets the x_kubernetes_int_or_string of this V1JSONSchemaProps.

        x-kubernetes-int-or-string specifies that this value is either an integer or a string. If this is true, an empty type is allowed and type as child of anyOf is permitted if following one of the following patterns:  1) anyOf:    - type: integer    - type: string 2) allOf:    - anyOf:      - type: integer      - type: string    - ... zero or more  # noqa: E501

        :param x_kubernetes_int_or_string: The x_kubernetes_int_or_string of this V1JSONSchemaProps.  # noqa: E501
        :type: bool
        """

        self._x_kubernetes_int_or_string = x_kubernetes_int_or_string

    @property
    def x_kubernetes_list_map_keys(self):
        """Gets the x_kubernetes_list_map_keys of this V1JSONSchemaProps.  # noqa: E501

        x-kubernetes-list-map-keys annotates an array with the x-kubernetes-list-type `map` by specifying the keys used as the index of the map.  This tag MUST only be used on lists that have the \"x-kubernetes-list-type\" extension set to \"map\". Also, the values specified for this attribute must be a scalar typed field of the child structure (no nesting is supported).  The properties specified must either be required or have a default value, to ensure those properties are present for all list items.  # noqa: E501

        :return: The x_kubernetes_list_map_keys of this V1JSONSchemaProps.  # noqa: E501
        :rtype: list[str]
        """
        return self._x_kubernetes_list_map_keys

    @x_kubernetes_list_map_keys.setter
    def x_kubernetes_list_map_keys(self, x_kubernetes_list_map_keys):
        """Sets the x_kubernetes_list_map_keys of this V1JSONSchemaProps.

        x-kubernetes-list-map-keys annotates an array with the x-kubernetes-list-type `map` by specifying the keys used as the index of the map.  This tag MUST only be used on lists that have the \"x-kubernetes-list-type\" extension set to \"map\". Also, the values specified for this attribute must be a scalar typed field of the child structure (no nesting is supported).  The properties specified must either be required or have a default value, to ensure those properties are present for all list items.  # noqa: E501

        :param x_kubernetes_list_map_keys: The x_kubernetes_list_map_keys of this V1JSONSchemaProps.  # noqa: E501
        :type: list[str]
        """

        self._x_kubernetes_list_map_keys = x_kubernetes_list_map_keys

    @property
    def x_kubernetes_list_type(self):
        """Gets the x_kubernetes_list_type of this V1JSONSchemaProps.  # noqa: E501

        x-kubernetes-list-type annotates an array to further describe its topology. This extension must only be used on lists and may have 3 possible values:  1) `atomic`: the list is treated as a single entity, like a scalar.      Atomic lists will be entirely replaced when updated. This extension      may be used on any type of list (struct, scalar, ...). 2) `set`:      Sets are lists that must not have multiple items with the same value. Each      value must be a scalar, an object with x-kubernetes-map-type `atomic` or an      array with x-kubernetes-list-type `atomic`. 3) `map`:      These lists are like maps in that their elements have a non-index key      used to identify them. Order is preserved upon merge. The map tag      must only be used on a list with elements of type object. Defaults to atomic for arrays.  # noqa: E501

        :return: The x_kubernetes_list_type of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._x_kubernetes_list_type

    @x_kubernetes_list_type.setter
    def x_kubernetes_list_type(self, x_kubernetes_list_type):
        """Sets the x_kubernetes_list_type of this V1JSONSchemaProps.

        x-kubernetes-list-type annotates an array to further describe its topology. This extension must only be used on lists and may have 3 possible values:  1) `atomic`: the list is treated as a single entity, like a scalar.      Atomic lists will be entirely replaced when updated. This extension      may be used on any type of list (struct, scalar, ...). 2) `set`:      Sets are lists that must not have multiple items with the same value. Each      value must be a scalar, an object with x-kubernetes-map-type `atomic` or an      array with x-kubernetes-list-type `atomic`. 3) `map`:      These lists are like maps in that their elements have a non-index key      used to identify them. Order is preserved upon merge. The map tag      must only be used on a list with elements of type object. Defaults to atomic for arrays.  # noqa: E501

        :param x_kubernetes_list_type: The x_kubernetes_list_type of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._x_kubernetes_list_type = x_kubernetes_list_type

    @property
    def x_kubernetes_map_type(self):
        """Gets the x_kubernetes_map_type of this V1JSONSchemaProps.  # noqa: E501

        x-kubernetes-map-type annotates an object to further describe its topology. This extension must only be used when type is object and may have 2 possible values:  1) `granular`:      These maps are actual maps (key-value pairs) and each fields are independent      from each other (they can each be manipulated by separate actors). This is      the default behaviour for all maps. 2) `atomic`: the list is treated as a single entity, like a scalar.      Atomic maps will be entirely replaced when updated.  # noqa: E501

        :return: The x_kubernetes_map_type of this V1JSONSchemaProps.  # noqa: E501
        :rtype: str
        """
        return self._x_kubernetes_map_type

    @x_kubernetes_map_type.setter
    def x_kubernetes_map_type(self, x_kubernetes_map_type):
        """Sets the x_kubernetes_map_type of this V1JSONSchemaProps.

        x-kubernetes-map-type annotates an object to further describe its topology. This extension must only be used when type is object and may have 2 possible values:  1) `granular`:      These maps are actual maps (key-value pairs) and each fields are independent      from each other (they can each be manipulated by separate actors). This is      the default behaviour for all maps. 2) `atomic`: the list is treated as a single entity, like a scalar.      Atomic maps will be entirely replaced when updated.  # noqa: E501

        :param x_kubernetes_map_type: The x_kubernetes_map_type of this V1JSONSchemaProps.  # noqa: E501
        :type: str
        """

        self._x_kubernetes_map_type = x_kubernetes_map_type

    @property
    def x_kubernetes_preserve_unknown_fields(self):
        """Gets the x_kubernetes_preserve_unknown_fields of this V1JSONSchemaProps.  # noqa: E501

        x-kubernetes-preserve-unknown-fields stops the API server decoding step from pruning fields which are not specified in the validation schema. This affects fields recursively, but switches back to normal pruning behaviour if nested properties or additionalProperties are specified in the schema. This can either be true or undefined. False is forbidden.  # noqa: E501

        :return: The x_kubernetes_preserve_unknown_fields of this V1JSONSchemaProps.  # noqa: E501
        :rtype: bool
        """
        return self._x_kubernetes_preserve_unknown_fields

    @x_kubernetes_preserve_unknown_fields.setter
    def x_kubernetes_preserve_unknown_fields(self, x_kubernetes_preserve_unknown_fields):
        """Sets the x_kubernetes_preserve_unknown_fields of this V1JSONSchemaProps.

        x-kubernetes-preserve-unknown-fields stops the API server decoding step from pruning fields which are not specified in the validation schema. This affects fields recursively, but switches back to normal pruning behaviour if nested properties or additionalProperties are specified in the schema. This can either be true or undefined. False is forbidden.  # noqa: E501

        :param x_kubernetes_preserve_unknown_fields: The x_kubernetes_preserve_unknown_fields of this V1JSONSchemaProps.  # noqa: E501
        :type: bool
        """

        self._x_kubernetes_preserve_unknown_fields = x_kubernetes_preserve_unknown_fields

    @property
    def x_kubernetes_validations(self):
        """Gets the x_kubernetes_validations of this V1JSONSchemaProps.  # noqa: E501

        x-kubernetes-validations describes a list of validation rules written in the CEL expression language.  # noqa: E501

        :return: The x_kubernetes_validations of this V1JSONSchemaProps.  # noqa: E501
        :rtype: list[V1ValidationRule]
        """
        return self._x_kubernetes_validations

    @x_kubernetes_validations.setter
    def x_kubernetes_validations(self, x_kubernetes_validations):
        """Sets the x_kubernetes_validations of this V1JSONSchemaProps.

        x-kubernetes-validations describes a list of validation rules written in the CEL expression language.  # noqa: E501

        :param x_kubernetes_validations: The x_kubernetes_validations of this V1JSONSchemaProps.  # noqa: E501
        :type: list[V1ValidationRule]
        """

        self._x_kubernetes_validations = x_kubernetes_validations

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1JSONSchemaProps):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1JSONSchemaProps):
            return True

        return self.to_dict() != other.to_dict()
