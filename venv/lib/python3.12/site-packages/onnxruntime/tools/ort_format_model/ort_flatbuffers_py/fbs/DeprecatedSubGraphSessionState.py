# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

# deprecated: no longer using kernel def hashes
class DeprecatedSubGraphSessionState(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = DeprecatedSubGraphSessionState()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsDeprecatedSubGraphSessionState(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def DeprecatedSubGraphSessionStateBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x52\x54\x4D", size_prefixed=size_prefixed)

    # DeprecatedSubGraphSessionState
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # DeprecatedSubGraphSessionState
    def GraphId(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # DeprecatedSubGraphSessionState
    def SessionState(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            from ort_flatbuffers_py.fbs.DeprecatedSessionState import DeprecatedSessionState
            obj = DeprecatedSessionState()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

def DeprecatedSubGraphSessionStateStart(builder):
    builder.StartObject(2)

def Start(builder):
    DeprecatedSubGraphSessionStateStart(builder)

def DeprecatedSubGraphSessionStateAddGraphId(builder, graphId):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(graphId), 0)

def AddGraphId(builder, graphId):
    DeprecatedSubGraphSessionStateAddGraphId(builder, graphId)

def DeprecatedSubGraphSessionStateAddSessionState(builder, sessionState):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(sessionState), 0)

def AddSessionState(builder, sessionState):
    DeprecatedSubGraphSessionStateAddSessionState(builder, sessionState)

def DeprecatedSubGraphSessionStateEnd(builder):
    return builder.EndObject()

def End(builder):
    return DeprecatedSubGraphSessionStateEnd(builder)
