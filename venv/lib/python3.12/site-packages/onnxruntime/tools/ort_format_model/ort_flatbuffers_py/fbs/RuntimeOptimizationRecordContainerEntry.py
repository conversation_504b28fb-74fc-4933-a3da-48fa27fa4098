# automatically generated by the FlatBuffers compiler, do not modify

# namespace: fbs

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class RuntimeOptimizationRecordContainerEntry(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = RuntimeOptimizationRecordContainerEntry()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsRuntimeOptimizationRecordContainerEntry(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    @classmethod
    def RuntimeOptimizationRecordContainerEntryBufferHasIdentifier(cls, buf, offset, size_prefixed=False):
        return flatbuffers.util.BufferHasIdentifier(buf, offset, b"\x4F\x52\x54\x4D", size_prefixed=size_prefixed)

    # RuntimeOptimizationRecordContainerEntry
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # RuntimeOptimizationRecordContainerEntry
    def OptimizerName(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # RuntimeOptimizationRecordContainerEntry
    def RuntimeOptimizationRecords(self, j):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            x = self._tab.Vector(o)
            x += flatbuffers.number_types.UOffsetTFlags.py_type(j) * 4
            x = self._tab.Indirect(x)
            from ort_flatbuffers_py.fbs.RuntimeOptimizationRecord import RuntimeOptimizationRecord
            obj = RuntimeOptimizationRecord()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # RuntimeOptimizationRecordContainerEntry
    def RuntimeOptimizationRecordsLength(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.VectorLen(o)
        return 0

    # RuntimeOptimizationRecordContainerEntry
    def RuntimeOptimizationRecordsIsNone(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        return o == 0

def RuntimeOptimizationRecordContainerEntryStart(builder):
    builder.StartObject(2)

def Start(builder):
    RuntimeOptimizationRecordContainerEntryStart(builder)

def RuntimeOptimizationRecordContainerEntryAddOptimizerName(builder, optimizerName):
    builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(optimizerName), 0)

def AddOptimizerName(builder, optimizerName):
    RuntimeOptimizationRecordContainerEntryAddOptimizerName(builder, optimizerName)

def RuntimeOptimizationRecordContainerEntryAddRuntimeOptimizationRecords(builder, runtimeOptimizationRecords):
    builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(runtimeOptimizationRecords), 0)

def AddRuntimeOptimizationRecords(builder, runtimeOptimizationRecords):
    RuntimeOptimizationRecordContainerEntryAddRuntimeOptimizationRecords(builder, runtimeOptimizationRecords)

def RuntimeOptimizationRecordContainerEntryStartRuntimeOptimizationRecordsVector(builder, numElems):
    return builder.StartVector(4, numElems, 4)

def StartRuntimeOptimizationRecordsVector(builder, numElems: int) -> int:
    return RuntimeOptimizationRecordContainerEntryStartRuntimeOptimizationRecordsVector(builder, numElems)

def RuntimeOptimizationRecordContainerEntryEnd(builder):
    return builder.EndObject()

def End(builder):
    return RuntimeOptimizationRecordContainerEntryEnd(builder)
