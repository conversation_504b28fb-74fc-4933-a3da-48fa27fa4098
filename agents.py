from crewai import Agent, Task, Crew
from azure_service import AzureDocumentService
from models import ExtractionResult, TabularData
from chunking_service import DocumentChunkingService, ChunkingStrategy
import json
import asyncio
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentExtractionAgents:
    def __init__(self):
        self.azure_service = AzureDocumentService()
    
    def create_preprocessing_agent(self):
        return Agent(
            role="Document Preprocessor",
            goal="Validate and prepare financial documents for extraction",
            backstory="You are an expert in document validation and preprocessing for financial data extraction.",
            verbose=True,
            allow_delegation=False
        )
    
    def create_extraction_agent(self):
        return Agent(
            role="Data Extractor", 
            goal="Extract raw data from financial documents using Azure Document Intelligence",
            backstory="You specialize in extracting structured data from financial documents using OCR and layout analysis.",
            verbose=True,
            allow_delegation=False
        )
    
    def create_analysis_agent(self):
        return Agent(
            role="Financial Data Analyst",
            goal="Analyze and refine extracted data using AI to identify financial information",
            backstory="You are an expert financial analyst who can intelligently identify and structure portfolio data.",
            verbose=True,
            allow_delegation=False
        )
    
    def create_verification_agent(self):
        return Agent(
            role="Data Verifier",
            goal="Verify that required fields are present and data quality is acceptable",
            backstory="You ensure data quality and validate that all required financial data points are captured.",
            verbose=True,
            allow_delegation=False
        )
    
    def create_output_agent(self):
        return Agent(
            role="Output Formatter",
            goal="Format the final JSON output according to specifications",
            backstory="You specialize in formatting financial data into the required JSON structure.",
            verbose=True,
            allow_delegation=False
        )

class DocumentExtractionTasks:
    def __init__(self, azure_service):
        self.azure_service = azure_service
    
    def preprocess_task(self, file_path, document_name):
        return Task(
            description=f"Validate and prepare the document {document_name} at {file_path} for processing",
            expected_output="Document validation status and preprocessing results",
            agent=None  # Will be assigned when creating crew
        )
    
    def extraction_task(self, file_path, document_name):
        return Task(
            description=f"Extract raw data from {document_name} using Azure Document Intelligence",
            expected_output="Raw extracted data including tables, text, and key-value pairs",
            agent=None
        )
    
    def analysis_task(self, document_name):
        return Task(
            description=f"Analyze extracted data from {document_name} and identify financial information using GPT-4o",
            expected_output="Structured financial data with identified fields",
            agent=None
        )
    
    def verification_task(self):
        return Task(
            description="Verify that required fields (ticker_symbol, shares_quantity) are present and validate data quality",
            expected_output="Validation results and quality assessment",
            agent=None
        )
    
    def output_task(self):
        return Task(
            description="Format the final output as JSON according to the specified schema",
            expected_output="Final JSON formatted extraction result",
            agent=None
        )

class FinancialExtractionCrew:
    def __init__(self):
        self.azure_service = AzureDocumentService()
        self.agents = DocumentExtractionAgents()
        self.tasks = DocumentExtractionTasks(self.azure_service)
        self.chunking_service = DocumentChunkingService(self.azure_service)
    
    def process_document(self, file_path, document_name, use_markdown=True, enable_cross_page=True, use_chunking=None):
        """Process a financial document with optional chunking and parallel processing"""
        
        # Analyze document to determine if chunking is beneficial
        chunking_analysis = self.chunking_service.analyze_document_for_chunking(file_path)
        
        # Determine chunking strategy
        should_use_chunking = (
            use_chunking if use_chunking is not None 
            else chunking_analysis.get('needs_chunking', False)
        )
        
        logger.info(f"Processing document: {document_name}")
        logger.info(f"File size: {chunking_analysis.get('file_size_mb', 0):.2f} MB")
        logger.info(f"Estimated pages: {chunking_analysis.get('estimated_pages', 0)}")
        logger.info(f"Complexity score: {chunking_analysis.get('complexity_score', 0):.2f}")
        logger.info(f"Using chunking: {should_use_chunking}")
        
        if should_use_chunking:
            return self.process_document_with_chunking(
                file_path, document_name, use_markdown, enable_cross_page, chunking_analysis
            )
        else:
            return self.process_document_standard(
                file_path, document_name, use_markdown, enable_cross_page
            )
    
    def process_document_with_chunking(self, file_path, document_name, use_markdown, enable_cross_page, chunking_analysis):
        """Process document using chunking and parallel processing"""
        
        start_time = time.time()
        
        # Get recommended chunking strategy
        strategy = chunking_analysis.get('recommended_strategy')
        if not strategy:
            complexity_score = chunking_analysis.get('complexity_score', 0.5)
            file_size_mb = chunking_analysis.get('file_size_mb', 0)
            estimated_pages = chunking_analysis.get('estimated_pages', 1)
            strategy = self.chunking_service._recommend_chunking_strategy(
                file_size_mb, estimated_pages, complexity_score
            )
        
        logger.info(f"📊 Using chunking strategy: {strategy.max_pages_per_chunk} pages/chunk, {strategy.parallel_processing_limit} workers")
        
        # Create document chunks
        chunks = self.chunking_service.create_document_chunks(file_path, strategy)
        
        if len(chunks) <= 1:
            logger.info("Document too small for chunking, using standard processing")
            return self.process_document_standard(file_path, document_name, use_markdown, enable_cross_page)
        
        logger.info(f"📋 Created {len(chunks)} chunks for parallel processing")
        
        # Process chunks in parallel
        processing_options = {
            'use_markdown': use_markdown,
            'enable_cross_page': enable_cross_page,
            'parallel_limit': strategy.parallel_processing_limit
        }
        
        # Run parallel processing (using asyncio event loop)
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            parallel_result = loop.run_until_complete(
                self.chunking_service.process_chunks_parallel(chunks, processing_options)
            )
        except Exception as e:
            logger.error(f"Error in parallel processing: {e}")
            # Fallback to standard processing
            return self.process_document_standard(file_path, document_name, use_markdown, enable_cross_page)
        finally:
            loop.close()
        
        if not parallel_result.get('success', False):
            logger.warning("Parallel processing failed, falling back to standard processing")
            return self.process_document_standard(file_path, document_name, use_markdown, enable_cross_page)
        
        # Combine and analyze results from all chunks
        combined_data = parallel_result['combined_result']
        
        logger.info("🤖 Analyzing combined chunk results with LLM...")
        
        # Prepare combined data for LLM analysis
        enhanced_extracted_data = self._prepare_combined_data_for_llm(combined_data, use_markdown, enable_cross_page)
        
        # Analyze with GPT-4o using the most appropriate method
        if use_markdown and enhanced_extracted_data.get('filtered_tables'):
            if enable_cross_page and any(t.get('is_cross_page', False) for t in enhanced_extracted_data.get('filtered_tables', [])):
                logger.info("🚀 Using enhanced cross-page analysis for combined results...")
                analysis_result = self.azure_service.analyze_with_gpt4o_enhanced_markdown(enhanced_extracted_data, document_name)
            else:
                analysis_result = self.azure_service.analyze_with_gpt4o_markdown(enhanced_extracted_data, document_name)
        else:
            # Convert combined data to standard format for original analysis method
            standard_data = self._convert_combined_to_standard_format(combined_data)
            analysis_result = self.azure_service.analyze_with_gpt4o(standard_data, document_name)
        
        if not analysis_result:
            return {"error": "Failed to analyze combined chunk results with GPT-4o"}
        
        # Calculate total processing time
        total_processing_time = time.time() - start_time
        
        # Validate and format with enhanced reporting
        logger.info("✅ Validating chunked processing results...")
        try:
            extraction_result = ExtractionResult(**analysis_result)
            
            processing_method = "chunked_enhanced_markdown" if (use_markdown and enable_cross_page) else ("chunked_markdown" if use_markdown else "chunked_standard")
            
            # Enhanced success metrics
            processing_stats = parallel_result.get('processing_stats', {})
            chunk_summaries = combined_data.get('chunk_summaries', [])
            
            if extraction_result.validate_required_fields():
                return {
                    "success": True,
                    "result": extraction_result.dict(),
                    "raw_extracted_data": enhanced_extracted_data,
                    "combined_chunk_data": combined_data,
                    "processing_method": processing_method,
                    "chunking_enabled": True,
                    "chunking_stats": {
                        "total_chunks": len(chunks),
                        "successful_chunks": processing_stats.get('processed_chunks', 0),
                        "failed_chunks": processing_stats.get('failed_chunks', 0),
                        "parallel_efficiency": processing_stats.get('parallel_efficiency', 0),
                        "total_processing_time": total_processing_time,
                        "chunk_processing_time": processing_stats.get('processing_time', 0),
                        "chunk_summaries": chunk_summaries
                    },
                    "cross_page_enabled": enable_cross_page,
                    "cross_page_tables_found": len([t for t in enhanced_extracted_data.get('filtered_tables', []) if t.get('is_cross_page', False)])
                }
            else:
                return {
                    "error": "Required fields (ticker_symbol, shares_quantity) not found in chunked processing",
                    "partial_result": extraction_result.dict(),
                    "raw_extracted_data": enhanced_extracted_data,
                    "combined_chunk_data": combined_data,
                    "processing_method": processing_method,
                    "chunking_enabled": True,
                    "chunking_stats": {
                        "total_chunks": len(chunks),
                        "successful_chunks": processing_stats.get('processed_chunks', 0),
                        "failed_chunks": processing_stats.get('failed_chunks', 0),
                        "total_processing_time": total_processing_time
                    }
                }
        except Exception as e:
            return {
                "error": f"Validation failed for chunked processing: {str(e)}",
                "raw_result": analysis_result,
                "combined_chunk_data": combined_data,
                "processing_method": processing_method,
                "chunking_enabled": True,
                "chunking_stats": {
                    "total_chunks": len(chunks),
                    "total_processing_time": total_processing_time
                }
            }
    
    def _prepare_combined_data_for_llm(self, combined_data, use_markdown, enable_cross_page):
        """Prepare combined chunk data for LLM analysis"""
        
        enhanced_data = {
            "filtered_tables": combined_data.get('combined_tables', []),
            "table_contexts": [],
            "markdown_content": combined_data.get('combined_markdown', ''),
            "chunk_summaries": combined_data.get('chunk_summaries', []),
            "processing_metadata": combined_data.get('processing_metadata', {})
        }
        
        # Add chunk context information to tables
        for table in enhanced_data["filtered_tables"]:
            if not table.get('source_chunk'):
                continue
                
            # Enhance table with chunk context
            table['chunk_context'] = {
                'source_chunk': table.get('source_chunk'),
                'source_pages': table.get('source_pages'),
                'processing_method': 'chunked_parallel'
            }
        
        # Create synthetic table contexts from chunk information
        for chunk_summary in enhanced_data["chunk_summaries"]:
            enhanced_data["table_contexts"].append({
                'headings': [
                    {'level': 2, 'text': f"Chunk {chunk_summary['chunk_id']} (Pages {chunk_summary['page_range']})"}
                ],
                'recent_context': f"Processing chunk covering pages {chunk_summary['page_range']} with {chunk_summary['tables_found']} tables found",
                'chunk_id': chunk_summary['chunk_id']
            })
        
        return enhanced_data
    
    def _convert_combined_to_standard_format(self, combined_data):
        """Convert combined chunk data to standard extraction format"""
        
        standard_data = {
            "tables": [],
            "paragraphs": [],
            "key_value_pairs": []
        }
        
        # Convert combined tables to standard format
        for table_info in combined_data.get('combined_tables', []):
            if 'table_data' in table_info:
                standard_data["tables"].append(table_info['table_data'])
        
        # Combine text from all chunks
        for text_info in combined_data.get('combined_text', []):
            if 'text' in text_info:
                standard_data["paragraphs"].extend(text_info['text'].split('\n'))
        
        return standard_data
    
    def process_document_standard(self, file_path, document_name, use_markdown=True, enable_cross_page=True):
        """Standard document processing without chunking (original method)"""
        
        # Step 1: Extract using Azure Document Intelligence
        print("🔍 Extracting document layout...")
        output_format = "markdown" if use_markdown else "json"
        result = self.azure_service.extract_document_layout(file_path, output_format)
        if not result:
            return {"error": "Failed to extract document layout"}
        
        # Step 2: Process extracted data with enhanced capabilities
        print("Processing extracted data...")
        if use_markdown:
            if enable_cross_page:
                print("🔄 Enabling cross-page table detection...")
                extracted_data = self.azure_service.extract_markdown_content_with_cross_page(result)
                cross_page_count = len(extracted_data.get('cross_page_tables', []))
                print(f"🔍 Detected {cross_page_count} cross-page table structures")
            else:
                extracted_data = self.azure_service.extract_markdown_content(result)
            
            filtered_count = len(extracted_data.get('filtered_tables', []))
            cross_page_filtered = len([t for t in extracted_data.get('filtered_tables', []) if t.get('is_cross_page', False)])
            print(f"📊 Found {filtered_count} relevant financial tables ({cross_page_filtered} cross-page)")
        else:
            extracted_data = self.azure_service.extract_tables_and_text(result)
        
        # Step 3: Analyze with GPT-4o using enhanced processing
        print("🤖 Analyzing with LLM...")
        if use_markdown and extracted_data.get('filtered_tables'):
            if enable_cross_page and any(t.get('is_cross_page', False) for t in extracted_data.get('filtered_tables', [])):
                print("🚀 Using enhanced cross-page analysis...")
                analysis_result = self.azure_service.analyze_with_gpt4o_enhanced_markdown(extracted_data, document_name)
            else:
                analysis_result = self.azure_service.analyze_with_gpt4o_markdown(extracted_data, document_name)
        else:
            # Fallback to original method if no filtered tables found
            print("⚠️ No relevant tables found in markdown, falling back to standard extraction...")
            extracted_data = self.azure_service.extract_tables_and_text(result)
            analysis_result = self.azure_service.analyze_with_gpt4o(extracted_data, document_name)
            
        if not analysis_result:
            return {"error": "Failed to analyze document with GPT-4o"}
        
        # Step 4: Validate and format with enhanced reporting
        print("✅ Validating results...")
        try:
            extraction_result = ExtractionResult(**analysis_result)
            processing_method = "enhanced_markdown" if (use_markdown and enable_cross_page) else ("markdown" if use_markdown else "standard")
            
            if extraction_result.validate_required_fields():
                return {
                    "success": True,
                    "result": extraction_result.dict(),
                    "raw_extracted_data": extracted_data,
                    "azure_result": result,
                    "processing_method": processing_method,
                    "chunking_enabled": False,
                    "cross_page_enabled": enable_cross_page,
                    "cross_page_tables_found": len([t for t in extracted_data.get('filtered_tables', []) if t.get('is_cross_page', False)])
                }
            else:
                return {
                    "error": "Required fields (ticker_symbol, shares_quantity) not found",
                    "partial_result": extraction_result.dict(),
                    "raw_extracted_data": extracted_data,
                    "processing_method": processing_method,
                    "chunking_enabled": False,
                    "cross_page_enabled": enable_cross_page
                }
        except Exception as e:
            return {
                "error": f"Validation failed: {str(e)}",
                "raw_result": analysis_result,
                "raw_extracted_data": extracted_data,
                "processing_method": processing_method,
                "chunking_enabled": False,
                "cross_page_enabled": enable_cross_page
            }
