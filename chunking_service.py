import os
import asyncio
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
from queue import Queue
import logging
from performance_monitor import PerformanceMonitor, ProcessingOptimizer, PerformanceMetrics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DocumentChunk:
    """Represents a chunk of a document for processing"""
    chunk_id: str
    file_path: str
    start_page: int
    end_page: int
    total_pages: int
    chunk_size_mb: float
    processing_priority: int = 1  # 1=high, 2=medium, 3=low
    estimated_processing_time: float = 0.0
    
    def __post_init__(self):
        self.page_range = f"{self.start_page}-{self.end_page}" if self.start_page != self.end_page else str(self.start_page)

@dataclass
class ChunkingStrategy:
    """Configuration for document chunking strategy"""
    max_pages_per_chunk: int = 10
    max_chunk_size_mb: float = 10.0
    overlap_pages: int = 1
    enable_intelligent_splitting: bool = True
    prioritize_table_dense_chunks: bool = True
    parallel_processing_limit: int = 4
    
class DocumentChunkingService:
    """Service for intelligent document chunking and parallel processing"""
    
    def __init__(self, azure_service=None):
        self.azure_service = azure_service
        self.processing_stats = {
            'total_chunks': 0,
            'processed_chunks': 0,
            'failed_chunks': 0,
            'processing_time': 0.0,
            'parallel_efficiency': 0.0
        }
        self._progress_queue = Queue()
        self._chunk_results = {}
        self.performance_monitor = PerformanceMonitor()
        self.optimizer = ProcessingOptimizer()
        
    def analyze_document_for_chunking(self, file_path: str) -> Dict[str, Any]:
        """Analyze document to determine optimal chunking strategy"""
        try:
            file_stats = os.stat(file_path)
            file_size_mb = file_stats.st_size / (1024 * 1024)
            
            # Estimate page count based on file type and size
            file_extension = Path(file_path).suffix.lower()
            estimated_pages = self._estimate_page_count(file_path, file_size_mb, file_extension)
            
            # Determine if chunking is needed
            needs_chunking = (
                file_size_mb > 5.0 or  # Files larger than 5MB
                estimated_pages > 15 or  # Documents with more than 15 pages
                file_extension == '.pdf'  # PDFs are often complex
            )
            
            complexity_score = self._calculate_complexity_score(file_size_mb, estimated_pages, file_extension)
            
            # Get optimized processing recommendations
            optimization_info = self.optimizer.recommend_processing_strategy(
                file_size_mb, estimated_pages, complexity_score
            )
            
            return {
                'file_path': file_path,
                'file_size_mb': file_size_mb,
                'estimated_pages': estimated_pages,
                'needs_chunking': needs_chunking,
                'complexity_score': complexity_score,
                'recommended_strategy': self._recommend_chunking_strategy(file_size_mb, estimated_pages, complexity_score),
                'optimization_info': optimization_info,
                'analysis_timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing document for chunking: {e}")
            return {
                'file_path': file_path,
                'error': str(e),
                'needs_chunking': False,
                'analysis_timestamp': time.time()
            }
    
    def _estimate_page_count(self, file_path: str, file_size_mb: float, file_extension: str) -> int:
        """Estimate number of pages in document"""
        if file_extension == '.pdf':
            try:
                import PyPDF2
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    return len(pdf_reader.pages)
            except:
                # Fallback estimation for PDFs
                return max(1, int(file_size_mb * 2))  # Rough estimate: 2 pages per MB
        
        elif file_extension in ['.png', '.jpg', '.jpeg']:
            return 1  # Image files are single page
            
        elif file_extension in ['.docx', '.doc']:
            # Estimate based on file size (typical Word doc: ~0.5MB per page)
            return max(1, int(file_size_mb * 2))
            
        else:
            # Default estimation
            return max(1, int(file_size_mb * 1.5))
    
    def _calculate_complexity_score(self, file_size_mb: float, estimated_pages: int, file_extension: str) -> float:
        """Calculate document complexity score (0.0 to 1.0)"""
        score = 0.0
        
        # Size factor (larger files are more complex)
        if file_size_mb > 20:
            score += 0.4
        elif file_size_mb > 10:
            score += 0.3
        elif file_size_mb > 5:
            score += 0.2
        
        # Page count factor
        if estimated_pages > 50:
            score += 0.3
        elif estimated_pages > 20:
            score += 0.2
        elif estimated_pages > 10:
            score += 0.1
        
        # File type factor
        if file_extension == '.pdf':
            score += 0.3  # PDFs often have complex layouts
        elif file_extension in ['.docx', '.doc']:
            score += 0.2  # Word docs can be complex
        
        return min(score, 1.0)
    
    def _recommend_chunking_strategy(self, file_size_mb: float, estimated_pages: int, complexity_score: float) -> ChunkingStrategy:
        """Recommend optimal chunking strategy based on document characteristics"""
        
        if complexity_score > 0.7:
            # High complexity documents
            return ChunkingStrategy(
                max_pages_per_chunk=5,
                max_chunk_size_mb=5.0,
                overlap_pages=2,
                enable_intelligent_splitting=True,
                prioritize_table_dense_chunks=True,
                parallel_processing_limit=6
            )
        elif complexity_score > 0.4:
            # Medium complexity documents
            return ChunkingStrategy(
                max_pages_per_chunk=8,
                max_chunk_size_mb=8.0,
                overlap_pages=1,
                enable_intelligent_splitting=True,
                prioritize_table_dense_chunks=True,
                parallel_processing_limit=4
            )
        else:
            # Simple documents
            return ChunkingStrategy(
                max_pages_per_chunk=12,
                max_chunk_size_mb=12.0,
                overlap_pages=1,
                enable_intelligent_splitting=False,
                prioritize_table_dense_chunks=False,
                parallel_processing_limit=3
            )
    
    def create_document_chunks(self, file_path: str, strategy: ChunkingStrategy) -> List[DocumentChunk]:
        """Create document chunks based on the specified strategy"""
        
        analysis = self.analyze_document_for_chunking(file_path)
        estimated_pages = analysis.get('estimated_pages', 1)
        file_size_mb = analysis.get('file_size_mb', 0)
        
        chunks = []
        
        if not analysis.get('needs_chunking', False):
            # Single chunk for small documents
            chunks.append(DocumentChunk(
                chunk_id="chunk_0",
                file_path=file_path,
                start_page=1,
                end_page=estimated_pages,
                total_pages=estimated_pages,
                chunk_size_mb=file_size_mb,
                processing_priority=1
            ))
            return chunks
        
        # Create multiple chunks for large documents
        chunk_count = 0
        current_page = 1
        
        while current_page <= estimated_pages:
            end_page = min(current_page + strategy.max_pages_per_chunk - 1, estimated_pages)
            
            # Estimate chunk size (proportional to pages)
            chunk_size_mb = (file_size_mb * (end_page - current_page + 1)) / estimated_pages
            
            # Determine processing priority
            priority = self._calculate_chunk_priority(
                current_page, end_page, estimated_pages, strategy
            )
            
            chunk = DocumentChunk(
                chunk_id=f"chunk_{chunk_count}",
                file_path=file_path,
                start_page=current_page,
                end_page=end_page,
                total_pages=estimated_pages,
                chunk_size_mb=chunk_size_mb,
                processing_priority=priority,
                estimated_processing_time=self._estimate_chunk_processing_time(chunk_size_mb, end_page - current_page + 1)
            )
            
            chunks.append(chunk)
            chunk_count += 1
            
            # Move to next chunk (with overlap if specified)
            current_page = end_page + 1 - strategy.overlap_pages
            if current_page <= end_page:  # Prevent infinite loop
                current_page = end_page + 1
        
        logger.info(f"Created {len(chunks)} chunks for document {Path(file_path).name}")
        return chunks
    
    def _calculate_chunk_priority(self, start_page: int, end_page: int, total_pages: int, strategy: ChunkingStrategy) -> int:
        """Calculate processing priority for a chunk (1=high, 2=medium, 3=low)"""
        
        # Early pages often contain summary information (high priority)
        if start_page <= 3:
            return 1
        
        # Middle sections often contain detailed data (medium priority)
        if start_page <= total_pages * 0.7:
            return 2
        
        # Later pages often contain appendices (lower priority)
        return 3
    
    def _estimate_chunk_processing_time(self, chunk_size_mb: float, page_count: int) -> float:
        """Estimate processing time for a chunk in seconds"""
        # Base time estimates (these can be calibrated based on actual performance)
        base_time = 5.0  # Base processing time
        size_factor = chunk_size_mb * 0.5  # Additional time per MB
        page_factor = page_count * 0.3  # Additional time per page
        
        return base_time + size_factor + page_factor
    
    async def process_chunks_parallel(self, chunks: List[DocumentChunk], processing_options: Dict[str, Any]) -> Dict[str, Any]:
        """Process document chunks in parallel with progress tracking"""
        
        # Start performance monitoring
        self.performance_monitor.start_monitoring()
        
        start_time = time.time()
        self.processing_stats['total_chunks'] = len(chunks)
        self.processing_stats['processed_chunks'] = 0
        self.processing_stats['failed_chunks'] = 0
        
        # Sort chunks by priority and estimated processing time
        sorted_chunks = sorted(chunks, key=lambda x: (x.processing_priority, x.estimated_processing_time))
        
        # Get optimized parallel processing limit
        optimization_info = processing_options.get('optimization_info', {})
        max_workers = optimization_info.get('optimal_workers', min(
            processing_options.get('parallel_limit', 4),
            len(chunks),
            os.cpu_count() or 4
        ))
        
        logger.info(f"Starting parallel processing of {len(chunks)} chunks with {max_workers} workers")
        
        # Process chunks in parallel
        chunk_results = {}
        failed_chunks = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all chunk processing tasks
            future_to_chunk = {
                executor.submit(self._process_single_chunk, chunk, processing_options): chunk
                for chunk in sorted_chunks
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    result = future.result()
                    chunk_results[chunk.chunk_id] = result
                    self.processing_stats['processed_chunks'] += 1
                    self.performance_monitor.record_chunk_processed()
                    
                    logger.info(f"Completed chunk {chunk.chunk_id} ({chunk.page_range})")
                    
                except Exception as e:
                    logger.error(f"Failed to process chunk {chunk.chunk_id}: {e}")
                    failed_chunks.append({'chunk': chunk, 'error': str(e)})
                    self.processing_stats['failed_chunks'] += 1
                    self.performance_monitor.record_error()
        
        # Stop performance monitoring
        self.performance_monitor.stop_monitoring()
        
        processing_time = time.time() - start_time
        self.processing_stats['processing_time'] = processing_time
        
        # Calculate efficiency metrics
        total_estimated_time = sum(chunk.estimated_processing_time for chunk in chunks)
        self.processing_stats['parallel_efficiency'] = (
            total_estimated_time / processing_time if processing_time > 0 else 0
        )
        
        # Update performance metrics
        self.performance_monitor.metrics.calculate_efficiency(total_estimated_time)
        
        # Combine chunk results
        combined_result = self._combine_chunk_results(chunk_results, chunks)
        
        # Create performance report
        performance_report = {
            'processing_stats': self.processing_stats,
            'performance_metrics': self.performance_monitor.metrics.to_dict(),
            'system_utilization': self.performance_monitor.get_current_metrics(),
            'optimization_effectiveness': {
                'predicted_workers': max_workers,
                'predicted_efficiency': optimization_info.get('expected_speedup', 1.0),
                'actual_efficiency': self.processing_stats['parallel_efficiency'],
                'resource_usage_optimal': self.performance_monitor.metrics.memory_usage_mb < 1000
            }
        }
        
        return {
            'success': len(chunk_results) > 0,
            'combined_result': combined_result,
            'chunk_results': chunk_results,
            'failed_chunks': failed_chunks,
            'processing_stats': self.processing_stats,
            'performance_report': performance_report,
            'processing_time': processing_time,
            'parallel_efficiency': self.processing_stats['parallel_efficiency']
        }
    
    def _process_single_chunk(self, chunk: DocumentChunk, processing_options: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single document chunk"""
        
        try:
            chunk_start_time = time.time()
            
            # For now, we'll process the entire document but focus on the chunk's page range
            # In a production system, you might extract specific pages or sections
            
            if self.azure_service:
                # Use the existing Azure service to process the chunk
                output_format = "markdown" if processing_options.get('use_markdown', True) else "json"
                result = self.azure_service.extract_document_layout(chunk.file_path, output_format)
                
                if result:
                    # Process the extracted data
                    if processing_options.get('use_markdown', True):
                        if processing_options.get('enable_cross_page', True):
                            extracted_data = self.azure_service.extract_markdown_content_with_cross_page(result)
                        else:
                            extracted_data = self.azure_service.extract_markdown_content(result)
                    else:
                        extracted_data = self.azure_service.extract_tables_and_text(result)
                    
                    # Filter data relevant to this chunk's page range
                    filtered_data = self._filter_data_for_chunk(extracted_data, chunk)
                    
                    processing_time = time.time() - chunk_start_time
                    
                    return {
                        'success': True,
                        'chunk_id': chunk.chunk_id,
                        'page_range': chunk.page_range,
                        'extracted_data': filtered_data,
                        'processing_time': processing_time,
                        'chunk_info': {
                            'start_page': chunk.start_page,
                            'end_page': chunk.end_page,
                            'priority': chunk.processing_priority,
                            'size_mb': chunk.chunk_size_mb
                        }
                    }
            
            return {
                'success': False,
                'chunk_id': chunk.chunk_id,
                'error': 'Azure service not available',
                'processing_time': time.time() - chunk_start_time
            }
            
        except Exception as e:
            return {
                'success': False,
                'chunk_id': chunk.chunk_id,
                'error': str(e),
                'processing_time': time.time() - chunk_start_time
            }
    
    def _filter_data_for_chunk(self, extracted_data: Dict[str, Any], chunk: DocumentChunk) -> Dict[str, Any]:
        """Filter extracted data to be relevant to the specific chunk's page range"""
        
        # For now, return all data since Azure Document Intelligence doesn't provide 
        # granular page-level extraction. In a production system, you might:
        # 1. Extract specific page ranges from PDFs before processing
        # 2. Use page coordinates to filter tables and text
        # 3. Implement page-aware filtering logic
        
        filtered_data = extracted_data.copy()
        
        # Add chunk context to the data
        filtered_data['chunk_context'] = {
            'chunk_id': chunk.chunk_id,
            'page_range': chunk.page_range,
            'start_page': chunk.start_page,
            'end_page': chunk.end_page,
            'priority': chunk.processing_priority
        }
        
        # If we have page-specific data, we could filter it here
        # For example, filter tables that fall within the page range
        
        return filtered_data
    
    def _combine_chunk_results(self, chunk_results: Dict[str, Any], chunks: List[DocumentChunk]) -> Dict[str, Any]:
        """Combine results from multiple chunks into a unified result"""
        
        combined_data = {
            'combined_tables': [],
            'combined_text': [],
            'combined_markdown': '',
            'chunk_summaries': [],
            'processing_metadata': {
                'total_chunks': len(chunks),
                'successful_chunks': len([r for r in chunk_results.values() if r.get('success', False)]),
                'chunk_priorities': {},
                'page_coverage': {}
            }
        }
        
        # Sort results by chunk priority and page order
        sorted_results = []
        for chunk in sorted(chunks, key=lambda x: (x.processing_priority, x.start_page)):
            if chunk.chunk_id in chunk_results:
                sorted_results.append(chunk_results[chunk.chunk_id])
        
        # Combine data from all chunks
        for result in sorted_results:
            if not result.get('success', False):
                continue
                
            extracted_data = result.get('extracted_data', {})
            chunk_info = result.get('chunk_info', {})
            
            # Combine tables
            if 'filtered_tables' in extracted_data:
                for table in extracted_data['filtered_tables']:
                    table['source_chunk'] = result['chunk_id']
                    table['source_pages'] = result['page_range']
                    combined_data['combined_tables'].append(table)
            elif 'tables' in extracted_data:
                for table in extracted_data['tables']:
                    combined_data['combined_tables'].append({
                        'table_data': table,
                        'source_chunk': result['chunk_id'],
                        'source_pages': result['page_range']
                    })
            
            # Combine text/markdown
            if 'markdown_content' in extracted_data:
                combined_data['combined_markdown'] += f"\n\n--- Chunk {result['chunk_id']} (Pages {result['page_range']}) ---\n"
                combined_data['combined_markdown'] += extracted_data['markdown_content']
            
            if 'paragraphs' in extracted_data:
                chunk_text = '\n'.join(extracted_data['paragraphs'])
                combined_data['combined_text'].append({
                    'text': chunk_text,
                    'source_chunk': result['chunk_id'],
                    'source_pages': result['page_range']
                })
            
            # Track metadata
            priority = chunk_info.get('priority', 1)
            if priority not in combined_data['processing_metadata']['chunk_priorities']:
                combined_data['processing_metadata']['chunk_priorities'][priority] = 0
            combined_data['processing_metadata']['chunk_priorities'][priority] += 1
            
            combined_data['processing_metadata']['page_coverage'][result['page_range']] = {
                'chunk_id': result['chunk_id'],
                'processing_time': result.get('processing_time', 0),
                'priority': priority
            }
            
            # Create chunk summary
            combined_data['chunk_summaries'].append({
                'chunk_id': result['chunk_id'],
                'page_range': result['page_range'],
                'priority': priority,
                'processing_time': result.get('processing_time', 0),
                'tables_found': len(extracted_data.get('filtered_tables', extracted_data.get('tables', []))),
                'has_cross_page_tables': bool(extracted_data.get('cross_page_tables')),
                'success': True
            })
        
        return combined_data
    
    def get_processing_progress(self) -> Dict[str, Any]:
        """Get current processing progress and statistics"""
        total = self.processing_stats['total_chunks']
        processed = self.processing_stats['processed_chunks']
        failed = self.processing_stats['failed_chunks']
        
        progress_percentage = (processed + failed) / total * 100 if total > 0 else 0
        success_rate = processed / (processed + failed) * 100 if (processed + failed) > 0 else 0
        
        return {
            'total_chunks': total,
            'processed_chunks': processed,
            'failed_chunks': failed,
            'progress_percentage': progress_percentage,
            'success_rate': success_rate,
            'processing_time': self.processing_stats['processing_time'],
            'parallel_efficiency': self.processing_stats['parallel_efficiency']
        }
