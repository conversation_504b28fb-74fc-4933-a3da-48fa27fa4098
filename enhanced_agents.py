import asyncio
from typing import List, Dict, Any
from enhanced_azure_service import EnhancedAzureDocumentService
from models import (
    ExtractionResult, 
    EnhancedTableFilterCriteria, 
    EnhancedFilteredTableData
)
from chunking_service import DocumentChunk
import os

class EnhancedFinancialExtractionCrew:
    def __init__(self):
        self.azure_service = EnhancedAzureDocumentService()
        self.filter_criteria = EnhancedTableFilterCriteria(
            min_financial_keywords=2,
            enable_cross_page_detection=True,
            prefer_cross_page_tables=True,
            cross_page_similarity_threshold=0.7
        )
    
    async def process_document_enhanced(self, file_path: str, document_name: str) -> Dict[str, Any]:
        """Enhanced document processing with chunking, filtering, and parallel processing"""
        
        print("🔍 Starting enhanced document processing...")
        
        # Step 1: Check if document needs chunking
        should_chunk = self.azure_service.should_chunk_document(file_path)
        processing_method = "chunked_parallel" if should_chunk else "single_document"
        
        print(f"📊 Processing method: {processing_method}")
        
        if should_chunk:
            return await self._process_large_document(file_path, document_name)
        else:
            return await self._process_single_document(file_path, document_name)
    
    async def _process_single_document(self, file_path: str, document_name: str) -> Dict[str, Any]:
        """Process single document with enhanced filtering"""
        
        # Step 1: Extract with markdown support
        print("🔍 Extracting document with markdown support...")
        extraction_result = self.azure_service.extract_with_markdown(file_path)
        
        if not extraction_result["success"]:
            return {"error": f"Document extraction failed: {extraction_result['error']}"}
        
        result = extraction_result["result"]
        markdown_content = extraction_result.get("markdown_content")
        
        # Step 2: Detect cross-page tables
        print("🔗 Detecting cross-page tables...")
        cross_page_tables = self.azure_service.detect_cross_page_tables(result)
        print(f"Found {len(cross_page_tables)} cross-page table groups")
        
        # Step 3: Filter relevant tables using Pydantic logic
        print("🎯 Filtering relevant tables...")
        filtered_tables = self.azure_service.filter_relevant_tables(result, self.filter_criteria)
        print(f"Filtered to {len(filtered_tables)} relevant tables from {len(result.tables) if result.tables else 0} total")
        
        # Step 4: Analyze only relevant tables with GPT-4o
        print("🤖 Analyzing filtered tables with GPT-4o...")
        analysis_result = self.azure_service.analyze_filtered_tables_with_gpt4o(
            filtered_tables, document_name, markdown_content
        )
        
        if not analysis_result:
            return {"error": "GPT-4o analysis failed"}
        
        # Step 5: Validate and format results
        print("✅ Validating results...")
        try:
            extraction_result = ExtractionResult(**analysis_result)
            extraction_result.relevant_tables_count = len(filtered_tables)
            extraction_result.total_tables_found = len(result.tables) if result.tables else 0
            extraction_result.processing_method = "single_document"
            
            if extraction_result.validate_required_fields():
                return {
                    "success": True,
                    "result": extraction_result.model_dump(),
                    "filtered_tables": [table.model_dump() for table in filtered_tables],
                    "cross_page_tables": [cp.model_dump() for cp in cross_page_tables],
                    "markdown_content": markdown_content,
                    "processing_stats": {
                        "total_tables": len(result.tables) if result.tables else 0,
                        "relevant_tables": len(filtered_tables),
                        "cross_page_groups": len(cross_page_tables),
                        "processing_method": "single_document"
                    }
                }
            else:
                return {
                    "error": "Required fields (ticker_symbol, shares_quantity) not found",
                    "partial_result": extraction_result.model_dump(),
                    "filtered_tables": [table.model_dump() for table in filtered_tables],
                    "processing_stats": {
                        "total_tables": len(result.tables) if result.tables else 0,
                        "relevant_tables": len(filtered_tables),
                        "cross_page_groups": len(cross_page_tables)
                    }
                }
        except Exception as e:
            return {
                "error": f"Validation failed: {str(e)}",
                "raw_result": analysis_result,
                "filtered_tables": [table.model_dump() for table in filtered_tables]
            }
    
    async def _process_large_document(self, file_path: str, document_name: str) -> Dict[str, Any]:
        """Process large document with chunking and parallel processing"""
        
        print("📄 Creating document chunks...")
        chunks = self.azure_service.create_document_chunks(file_path)
        print(f"Created {len(chunks)} chunks for parallel processing")
        
        # Step 1: Process chunks in parallel
        print("⚡ Processing chunks in parallel...")
        chunk_results = await self.azure_service.process_chunks_parallel(chunks)
        
        # Step 2: Merge results from all chunks
        print("🔗 Merging chunk results...")
        merged_result = self._merge_chunk_results(chunk_results)
        
        if not merged_result["success"]:
            return {"error": "Failed to merge chunk results"}
        
        # Step 3: Apply filtering to merged tables
        print("🎯 Filtering merged tables...")
        all_filtered_tables = []
        all_cross_page_tables = []
        
        for chunk_data in chunk_results:
            if chunk_data["result"]["success"]:
                result = chunk_data["result"]["result"]
                
                # Detect cross-page tables in this chunk
                cross_page_tables = self.azure_service.detect_cross_page_tables(result)
                all_cross_page_tables.extend(cross_page_tables)
                
                # Filter relevant tables
                filtered_tables = self.azure_service.filter_relevant_tables(result, self.filter_criteria)
                all_filtered_tables.extend(filtered_tables)
        
        print(f"Total relevant tables from all chunks: {len(all_filtered_tables)}")
        
        # Step 4: Analyze merged relevant tables
        print("🤖 Analyzing merged relevant tables...")
        analysis_result = self.azure_service.analyze_filtered_tables_with_gpt4o(
            all_filtered_tables, document_name, merged_result.get("merged_markdown")
        )
        
        if not analysis_result:
            return {"error": "GPT-4o analysis of merged data failed"}
        
        # Step 5: Validate and format final results
        print("✅ Validating merged results...")
        try:
            extraction_result = ExtractionResult(**analysis_result)
            extraction_result.relevant_tables_count = len(all_filtered_tables)
            extraction_result.total_tables_found = merged_result.get("total_tables", 0)
            extraction_result.processing_method = "chunked_parallel"
            
            if extraction_result.validate_required_fields():
                return {
                    "success": True,
                    "result": extraction_result.model_dump(),
                    "filtered_tables": [table.model_dump() for table in all_filtered_tables],
                    "cross_page_tables": [cp.model_dump() for cp in all_cross_page_tables],
                    "processing_stats": {
                        "total_chunks": len(chunks),
                        "total_tables": merged_result.get("total_tables", 0),
                        "relevant_tables": len(all_filtered_tables),
                        "cross_page_groups": len(all_cross_page_tables),
                        "processing_method": "chunked_parallel"
                    }
                }
            else:
                return {
                    "error": "Required fields not found in merged results",
                    "partial_result": extraction_result.model_dump(),
                    "filtered_tables": [table.model_dump() for table in all_filtered_tables],
                    "processing_stats": {
                        "total_chunks": len(chunks),
                        "relevant_tables": len(all_filtered_tables)
                    }
                }
        except Exception as e:
            return {
                "error": f"Merged validation failed: {str(e)}",
                "raw_result": analysis_result,
                "filtered_tables": [table.model_dump() for table in all_filtered_tables]
            }
    
    def _merge_chunk_results(self, chunk_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Merge results from multiple chunks"""
        merged_tables = []
        merged_markdown = []
        total_tables = 0
        
        for chunk_data in chunk_results:
            if chunk_data["result"]["success"]:
                result = chunk_data["result"]["result"]
                
                if hasattr(result, 'tables') and result.tables:
                    merged_tables.extend(result.tables)
                    total_tables += len(result.tables)
                
                if chunk_data["result"].get("markdown_content"):
                    merged_markdown.append(chunk_data["result"]["markdown_content"])
        
        return {
            "success": True,
            "merged_tables": merged_tables,
            "merged_markdown": "\n\n".join(merged_markdown),
            "total_tables": total_tables
        }
    
    def process_document_sync(self, file_path: str, document_name: str) -> Dict[str, Any]:
        """Synchronous wrapper for async processing"""
        return asyncio.run(self.process_document_enhanced(file_path, document_name))
