import os
from dotenv import load_dotenv
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
import requests

load_dotenv()

def test_azure_document_intelligence():
    """Test Azure Document Intelligence API with different approaches"""
    
    endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
    key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
    
    print(f"Endpoint: {endpoint}")
    print(f"Key: {key[:10]}..." if key else "No key found")
    
    # Test 1: Check if we can create the client
    try:
        client = DocumentIntelligenceClient(
            endpoint=endpoint,
            credential=AzureKeyCredential(key)
        )
        print("✅ Client created successfully")
    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        return
    
    # Test 2: Try with a sample file
    sample_files = [
        "./Financial Statements - Sample Files (2)/Account Capture - American Funds - Client, Value - 2024-9-12.png",
        "./Financial Statements - Sample Files (2)/Allspring - 1.jpg"
    ]
    
    test_file = None
    for file_path in sample_files:
        if os.path.exists(file_path):
            test_file = file_path
            break
    
    if not test_file:
        print("❌ No test file found")
        return
    
    print(f"Testing with file: {test_file}")
    
    # Test 3: Try different API call formats
    try:
        with open(test_file, "rb") as f:
            # Method 1: Using body parameter
            print("Testing Method 1: body parameter")
            poller = client.begin_analyze_document(
                model_id="prebuilt-layout",
                body=f,
                content_type="application/octet-stream"
            )
            result = poller.result()
            print("✅ Method 1 successful!")
            return result
    except Exception as e:
        print(f"❌ Method 1 failed: {e}")
    
    try:
        with open(test_file, "rb") as f:
            # Method 2: Using analyze_request parameter
            print("Testing Method 2: analyze_request parameter")
            poller = client.begin_analyze_document(
                "prebuilt-layout",
                analyze_request=f,
                content_type="application/octet-stream"
            )
            result = poller.result()
            print("✅ Method 2 successful!")
            return result
    except Exception as e:
        print(f"❌ Method 2 failed: {e}")
    
    # Test 4: Try with URL-based approach
    try:
        print("Testing Method 3: URL-based approach")
        # This would require uploading to a URL first
        print("Skipping URL method for now...")
    except Exception as e:
        print(f"❌ Method 3 failed: {e}")
    
    # Test 5: Direct REST API call
    try:
        print("Testing Method 4: Direct REST API")
        url = f"{endpoint}/documentintelligence/documentModels/prebuilt-layout:analyze?api-version=2024-11-30"
        headers = {
            "Ocp-Apim-Subscription-Key": key,
            "Content-Type": "application/octet-stream"
        }
        
        with open(test_file, "rb") as f:
            response = requests.post(url, headers=headers, data=f.read())
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 202:
            print("✅ REST API call successful!")
            operation_location = response.headers.get('Operation-Location')
            print(f"Operation Location: {operation_location}")
            return "REST_API_SUCCESS"
        else:
            print(f"❌ REST API failed: {response.text}")
    except Exception as e:
        print(f"❌ REST API failed: {e}")
    
    return None

if __name__ == "__main__":
    result = test_azure_document_intelligence()
    if result:
        print("\n🎉 API test successful!")
        if hasattr(result, 'tables'):
            print(f"Found {len(result.tables)} tables")
        if hasattr(result, 'paragraphs'):
            print(f"Found {len(result.paragraphs)} paragraphs")
    else:
        print("\n❌ All API test methods failed")
