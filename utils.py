import os
from pathlib import Path

def get_file_type(file_path):
    """Determine file type based on extension"""
    extension = Path(file_path).suffix.lower()
    
    if extension in ['.pdf']:
        return 'pdf'
    elif extension in ['.png', '.jpg', '.jpeg']:
        return 'image'
    elif extension in ['.docx', '.doc']:
        return 'word'
    elif extension in ['.odt']:
        return 'odt'
    else:
        return 'unknown'

def validate_file(file_path):
    """Validate if file exists and is supported"""
    if not os.path.exists(file_path):
        return False, "File does not exist"
    
    file_type = get_file_type(file_path)
    if file_type == 'unknown':
        return False, "Unsupported file type"
    
    return True, "File is valid"

def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names)-1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.1f}{size_names[i]}"

def get_file_info(file_path):
    """Get basic file information"""
    if not os.path.exists(file_path):
        return None
    
    stat = os.stat(file_path)
    return {
        'name': os.path.basename(file_path),
        'size': format_file_size(stat.st_size),
        'type': get_file_type(file_path),
        'path': file_path
    }
