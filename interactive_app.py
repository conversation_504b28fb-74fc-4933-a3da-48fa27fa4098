import streamlit as st
import os
import json
from pathlib import Path
from enhanced_agents import EnhancedFinancialExtractionCrew
from interactive_visualization import add_interactive_visualization
from PIL import Image
import pandas as pd

# Page config
st.set_page_config(
    page_title="Interactive Financial Document Extraction",
    page_icon="📊",
    layout="wide"
)

st.title("📊 Interactive Financial Document Extraction")
st.markdown("**Multi-agent system with Azure-like interactive visualization**")

# Initialize the enhanced extraction crew
@st.cache_resource
def get_enhanced_crew():
    return EnhancedFinancialExtractionCrew()

crew = get_enhanced_crew()

# Sidebar for file selection and settings
st.sidebar.header("📁 Document Selection")

# Get sample files
sample_files_dir = "Financial Statements - Sample Files (2)"
sample_files = []

if os.path.exists(sample_files_dir):
    for file in os.listdir(sample_files_dir):
        file_path = os.path.join(sample_files_dir, file)
        if os.path.isfile(file_path):
            sample_files.append(file)

# File selection options
file_source = st.sidebar.radio(
    "Choose file source:",
    ["Sample Files", "Upload New File"]
)

selected_file_path = None
selected_file_name = None

if file_source == "Sample Files":
    if sample_files:
        selected_file = st.sidebar.selectbox(
            "Select a sample file:",
            sample_files
        )
        if selected_file:
            selected_file_path = os.path.join(sample_files_dir, selected_file)
            selected_file_name = selected_file
    else:
        st.sidebar.error("No sample files found in the directory")

else:
    uploaded_file = st.sidebar.file_uploader(
        "Upload a financial document",
        type=['pdf', 'png', 'jpg', 'jpeg', 'docx', 'odt']
    )
    if uploaded_file:
        # Save uploaded file temporarily
        temp_dir = "temp"
        os.makedirs(temp_dir, exist_ok=True)
        selected_file_path = os.path.join(temp_dir, uploaded_file.name)
        with open(selected_file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        selected_file_name = uploaded_file.name

# Enhanced settings
st.sidebar.header("⚙️ Processing Settings")
enable_chunking = st.sidebar.checkbox("Enable chunking for large files (>2MB)", value=True)
enable_cross_page = st.sidebar.checkbox("Enable cross-page table detection", value=True)
min_keywords = st.sidebar.slider("Minimum financial keywords for relevance", 1, 5, 2)

# Visualization settings
st.sidebar.header("🎨 Visualization Settings")
show_interactive = st.sidebar.checkbox("Enable Interactive Visualization", value=True)
show_bounding_boxes = st.sidebar.checkbox("Show Bounding Boxes", value=True)

# Update crew settings
if hasattr(crew, 'filter_criteria'):
    crew.filter_criteria.min_financial_keywords = min_keywords
    crew.filter_criteria.enable_cross_page_detection = enable_cross_page

def show_standard_view(result, selected_file_path):
    """Show standard three-column view as fallback"""
    st.subheader("📊 Standard View")

    # Create three columns for standard previews
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 Original Document")
        try:
            if selected_file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
                image = Image.open(selected_file_path)
                st.image(image, use_container_width=True)
            elif selected_file_path.lower().endswith('.pdf'):
                st.info("PDF preview - showing first page")
                st.text("PDF file selected")
            else:
                st.info(f"Document type: {Path(selected_file_path).suffix}")
        except Exception as e:
            st.error(f"Could not preview file: {e}")

    with col2:
        st.subheader("🎯 Filtered Relevant Tables")
        if 'filtered_tables' in result:
            filtered_tables = result['filtered_tables']

            st.write(f"**{len(filtered_tables)} relevant tables identified:**")

            for i, table in enumerate(filtered_tables[:3]):  # Show first 3 tables
                with st.expander(f"Table {table['table_index']} (Score: {table['relevance_score']:.2f})"):
                    st.write(f"**Keywords found:** {', '.join(table.get('financial_context', []))}")
                    st.write(f"**Dimensions:** {table['estimated_rows']} rows × {table['estimated_cols']} cols")
                    st.write(f"**Cross-page:** {'Yes' if table.get('is_cross_page', False) else 'No'}")

    with col3:
        st.subheader("💎 Extracted Financial Data")
        if result.get('success'):
            extraction_data = result['result']

            # Show key information
            st.write("**Document Information:**")
            info_data = {
                "Document": extraction_data.get('document_name', 'N/A'),
                "Client": extraction_data.get('client_name', 'N/A'),
                "Advisor": extraction_data.get('advisor_name', 'N/A'),
                "Portfolio ID": extraction_data.get('portfolio_id', 'N/A'),
                "Total Value": extraction_data.get('total_account_value', 'N/A'),
                "Date": extraction_data.get('date_of_analysis', 'N/A')
            }

            for key, value in info_data.items():
                st.write(f"**{key}:** {value}")

            # Show tabular data
            if extraction_data.get('tabular_data'):
                st.write("**Portfolio Holdings:**")
                holdings_df = pd.DataFrame(extraction_data['tabular_data'])
                st.dataframe(holdings_df, use_container_width=True)

# Main content area
if selected_file_path and os.path.exists(selected_file_path):
    st.header(f"📄 Processing: {selected_file_name}")
    
    # Show file info
    file_size = os.path.getsize(selected_file_path)
    file_size_mb = file_size / (1024 * 1024)
    will_chunk = file_size > (2 * 1024 * 1024) and enable_chunking
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("File Size", f"{file_size_mb:.2f} MB")
    with col2:
        st.metric("Processing Mode", "Chunked" if will_chunk else "Single")
    with col3:
        st.metric("Cross-page Detection", "Enabled" if enable_cross_page else "Disabled")
    with col4:
        st.metric("Interactive View", "Enabled" if show_interactive else "Disabled")
    
    # Process button
    if st.button("🚀 Extract & Visualize", type="primary"):
        with st.spinner("Processing document through enhanced multi-agent system..."):
            # Use synchronous wrapper for Streamlit
            result = crew.process_document_sync(selected_file_path, selected_file_name)
        
        # Store result in session state
        st.session_state.interactive_result = result
        st.session_state.file_path = selected_file_path
        st.session_state.file_name = selected_file_name
    
    # Display results if available
    if hasattr(st.session_state, 'interactive_result'):
        result = st.session_state.interactive_result
        
        # Processing Statistics
        if 'processing_stats' in result:
            st.subheader("📊 Processing Statistics")
            stats = result['processing_stats']
            
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Total Tables Found", stats.get('total_tables', 0))
            with col2:
                st.metric("Relevant Tables", stats.get('relevant_tables', 0))
            with col3:
                st.metric("Cross-page Groups", stats.get('cross_page_groups', 0))
            with col4:
                st.metric("Processing Method", stats.get('processing_method', 'Unknown'))
        
        # Interactive Visualization Section
        if show_interactive and 'azure_result' in result:
            st.markdown("---")
            
            # Add the interactive visualization
            try:
                add_interactive_visualization(
                    selected_file_path, 
                    result.get('azure_result'),
                    result.get('filtered_tables', [])
                )
            except Exception as e:
                st.error(f"Interactive visualization error: {e}")
                st.info("Falling back to standard view...")
                
                # Fallback to standard three-column view
                show_standard_view(result, selected_file_path)
        else:
            # Standard three-column view
            show_standard_view(result, selected_file_path)
        
        # Detailed Results Section
        st.markdown("---")
        st.header("📋 Detailed Results")
        
        if result.get('success'):
            st.success("✅ Enhanced extraction completed successfully!")
            
            # Tabs for different views
            tab1, tab2, tab3, tab4, tab5 = st.tabs([
                "Final JSON", "Table Analysis", "Cross-page Detection", 
                "Markdown Content", "Raw Azure Result"
            ])
            
            with tab1:
                st.subheader("📋 Final JSON Output")
                st.json(result['result'])
                
                # Download button
                json_str = json.dumps(result['result'], indent=2)
                st.download_button(
                    label="💾 Download JSON Result",
                    data=json_str,
                    file_name=f"{selected_file_name}_interactive_extraction.json",
                    mime="application/json"
                )
            
            with tab2:
                st.subheader("🎯 Table Filtering Analysis")
                if 'filtered_tables' in result:
                    for table in result['filtered_tables']:
                        with st.expander(f"Table {table['table_index']} Analysis"):
                            st.json(table)
            
            with tab3:
                st.subheader("🔗 Cross-page Table Detection")
                if 'cross_page_tables' in result:
                    if result['cross_page_tables']:
                        for cp_table in result['cross_page_tables']:
                            st.json(cp_table)
                    else:
                        st.info("No cross-page tables detected")
            
            with tab4:
                st.subheader("📝 Markdown Content")
                if 'markdown_content' in result and result['markdown_content']:
                    st.text_area("Extracted markdown", result['markdown_content'], height=300)
                else:
                    st.info("No markdown content available")
            
            with tab5:
                st.subheader("🔍 Raw Azure Result")
                if 'azure_result' in result:
                    st.info("Raw Azure Document Intelligence result available for visualization")
                    # Don't display the full raw result as it's very large
                    if st.checkbox("Show raw result (large data)"):
                        st.write("Azure result object loaded for visualization")
                else:
                    st.info("No raw Azure result available")
            
        else:
            st.error("❌ Enhanced extraction failed")
            st.error(result.get('error', 'Unknown error'))
            
            # Show partial results if available
            if 'partial_result' in result:
                st.subheader("📋 Partial Results")
                st.json(result['partial_result'])

else:
    st.info("👆 Please select a file from the sidebar to begin interactive extraction")

    # Show available sample files
    if sample_files:
        st.subheader("📁 Available Sample Files")
        for file in sample_files:
            file_path = os.path.join(sample_files_dir, file)
            file_size = os.path.getsize(file_path) / (1024 * 1024)
            st.write(f"• {file} ({file_size:.2f} MB)")


