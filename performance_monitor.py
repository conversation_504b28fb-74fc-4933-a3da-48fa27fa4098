import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for document processing"""
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    processing_time: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    chunks_processed: int = 0
    parallel_efficiency: float = 0.0
    token_usage: Dict[str, int] = field(default_factory=dict)
    error_count: int = 0
    
    def finish(self):
        """Mark the end of processing and calculate final metrics"""
        self.end_time = time.time()
        self.processing_time = self.end_time - self.start_time
        
    def calculate_efficiency(self, estimated_sequential_time: float):
        """Calculate parallel processing efficiency"""
        if self.processing_time > 0:
            self.parallel_efficiency = estimated_sequential_time / self.processing_time
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary for serialization"""
        return {
            'processing_time_seconds': self.processing_time,
            'memory_usage_mb': self.memory_usage_mb,
            'cpu_usage_percent': self.cpu_usage_percent,
            'chunks_processed': self.chunks_processed,
            'parallel_efficiency': self.parallel_efficiency,
            'token_usage': self.token_usage,
            'error_count': self.error_count,
            'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
            'end_time': datetime.fromtimestamp(self.end_time).isoformat() if self.end_time else None
        }

class PerformanceMonitor:
    """Monitor system performance during document processing"""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.monitoring = False
        self.monitor_thread = None
        self.system_stats = []
        
    def start_monitoring(self):
        """Start performance monitoring in a separate thread"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_system, daemon=True)
        self.monitor_thread.start()
        logger.info("Performance monitoring started")
        
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        
        # Calculate average metrics
        if self.system_stats:
            avg_memory = sum(stat['memory_mb'] for stat in self.system_stats) / len(self.system_stats)
            avg_cpu = sum(stat['cpu_percent'] for stat in self.system_stats) / len(self.system_stats)
            
            self.metrics.memory_usage_mb = avg_memory
            self.metrics.cpu_usage_percent = avg_cpu
        
        self.metrics.finish()
        logger.info(f"Performance monitoring stopped. Processing time: {self.metrics.processing_time:.2f}s")
        
    def _monitor_system(self):
        """Monitor system resources in background thread"""
        while self.monitoring:
            try:
                # Get memory usage
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                
                # Get CPU usage
                cpu_percent = psutil.cpu_percent(interval=0.1)
                
                self.system_stats.append({
                    'timestamp': time.time(),
                    'memory_mb': memory_mb,
                    'cpu_percent': cpu_percent
                })
                
                time.sleep(0.5)  # Monitor every 0.5 seconds
                
            except Exception as e:
                logger.warning(f"Error monitoring system resources: {e}")
                break
    
    def record_chunk_processed(self):
        """Record that a chunk has been processed"""
        self.metrics.chunks_processed += 1
        
    def record_error(self):
        """Record that an error occurred"""
        self.metrics.error_count += 1
        
    def record_token_usage(self, prompt_tokens: int, completion_tokens: int):
        """Record token usage for LLM calls"""
        if 'prompt_tokens' not in self.metrics.token_usage:
            self.metrics.token_usage['prompt_tokens'] = 0
        if 'completion_tokens' not in self.metrics.token_usage:
            self.metrics.token_usage['completion_tokens'] = 0
            
        self.metrics.token_usage['prompt_tokens'] += prompt_tokens
        self.metrics.token_usage['completion_tokens'] += completion_tokens
        self.metrics.token_usage['total_tokens'] = (
            self.metrics.token_usage['prompt_tokens'] + 
            self.metrics.token_usage['completion_tokens']
        )
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        current_time = time.time()
        elapsed_time = current_time - self.metrics.start_time
        
        # Get current system stats if monitoring
        current_memory = 0.0
        current_cpu = 0.0
        
        if self.system_stats:
            current_memory = self.system_stats[-1]['memory_mb']
            current_cpu = self.system_stats[-1]['cpu_percent']
        
        return {
            'elapsed_time_seconds': elapsed_time,
            'current_memory_mb': current_memory,
            'current_cpu_percent': current_cpu,
            'chunks_processed': self.metrics.chunks_processed,
            'errors_occurred': self.metrics.error_count,
            'token_usage': self.metrics.token_usage
        }

class ProcessingOptimizer:
    """Optimize processing parameters based on system capabilities and document characteristics"""
    
    @staticmethod
    def calculate_optimal_chunk_size(file_size_mb: float, available_memory_gb: float, estimated_pages: int) -> int:
        """Calculate optimal chunk size based on system resources and document characteristics"""
        
        # Base chunk size calculation
        if file_size_mb <= 5:
            return max(estimated_pages, 5)  # Process small docs as single chunk
        
        # Calculate based on available memory
        memory_factor = min(available_memory_gb / 4.0, 2.0)  # Scale with memory up to 8GB
        
        # Calculate based on file size
        if file_size_mb <= 20:
            base_chunk_size = 10
        elif file_size_mb <= 50:
            base_chunk_size = 8
        else:
            base_chunk_size = 6
        
        # Adjust based on memory
        optimal_size = int(base_chunk_size * memory_factor)
        
        # Ensure reasonable bounds
        return max(5, min(optimal_size, 20))
    
    @staticmethod
    def calculate_optimal_workers(cpu_count: int, available_memory_gb: float, chunk_count: int) -> int:
        """Calculate optimal number of parallel workers"""
        
        # Base on CPU cores
        cpu_workers = max(1, cpu_count - 1)  # Leave one core free
        
        # Base on memory (assume ~1GB per worker for safety)
        memory_workers = int(available_memory_gb * 0.8)
        
        # Base on chunk count (no point having more workers than chunks)
        chunk_workers = chunk_count
        
        # Take minimum to avoid resource contention
        optimal_workers = min(cpu_workers, memory_workers, chunk_workers, 8)  # Cap at 8
        
        return max(1, optimal_workers)
    
    @staticmethod
    def get_system_info() -> Dict[str, Any]:
        """Get current system information for optimization"""
        try:
            # Get memory info
            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024 ** 3)
            total_memory_gb = memory.total / (1024 ** 3)
            
            # Get CPU info
            cpu_count = psutil.cpu_count(logical=True)
            cpu_freq = psutil.cpu_freq()
            
            return {
                'cpu_count': cpu_count,
                'cpu_frequency_mhz': cpu_freq.current if cpu_freq else 0,
                'total_memory_gb': total_memory_gb,
                'available_memory_gb': available_memory_gb,
                'memory_usage_percent': memory.percent,
                'system_load': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0.0
            }
        except Exception as e:
            logger.warning(f"Error getting system info: {e}")
            return {
                'cpu_count': 4,
                'available_memory_gb': 4.0,
                'total_memory_gb': 8.0,
                'memory_usage_percent': 50.0
            }
    
    @staticmethod
    def recommend_processing_strategy(file_size_mb: float, estimated_pages: int, 
                                    complexity_score: float) -> Dict[str, Any]:
        """Recommend optimal processing strategy"""
        
        system_info = ProcessingOptimizer.get_system_info()
        
        # Calculate optimal parameters
        optimal_chunk_size = ProcessingOptimizer.calculate_optimal_chunk_size(
            file_size_mb, system_info['available_memory_gb'], estimated_pages
        )
        
        # Estimate chunk count
        estimated_chunks = max(1, estimated_pages // optimal_chunk_size)
        
        optimal_workers = ProcessingOptimizer.calculate_optimal_workers(
            system_info['cpu_count'], system_info['available_memory_gb'], estimated_chunks
        )
        
        # Determine if chunking is recommended
        use_chunking = (
            file_size_mb > 5.0 or 
            estimated_pages > 15 or 
            complexity_score > 0.4
        )
        
        # Calculate expected processing time
        base_time = 10.0  # Base processing time in seconds
        size_time = file_size_mb * 0.5
        complexity_time = complexity_score * 20.0
        
        sequential_time = base_time + size_time + complexity_time
        estimated_parallel_time = sequential_time / max(1, optimal_workers * 0.7)  # 70% efficiency
        
        return {
            'use_chunking': use_chunking,
            'optimal_chunk_size': optimal_chunk_size,
            'optimal_workers': optimal_workers,
            'estimated_chunks': estimated_chunks,
            'estimated_sequential_time': sequential_time,
            'estimated_parallel_time': estimated_parallel_time,
            'expected_speedup': sequential_time / estimated_parallel_time,
            'system_info': system_info,
            'recommendations': {
                'enable_parallel_processing': use_chunking and optimal_workers > 1,
                'use_cross_page_detection': complexity_score > 0.3,
                'use_enhanced_filtering': True,
                'priority_processing': estimated_chunks > 3
            }
        }

def create_performance_report(metrics: PerformanceMetrics, 
                            optimization_info: Dict[str, Any]) -> Dict[str, Any]:
    """Create a comprehensive performance report"""
    
    return {
        'processing_summary': {
            'total_time_seconds': metrics.processing_time,
            'chunks_processed': metrics.chunks_processed,
            'parallel_efficiency': metrics.parallel_efficiency,
            'errors_encountered': metrics.error_count
        },
        'resource_usage': {
            'average_memory_mb': metrics.memory_usage_mb,
            'average_cpu_percent': metrics.cpu_usage_percent,
            'peak_memory_mb': max([stat['memory_mb'] for stat in getattr(metrics, 'system_stats', [])], default=0)
        },
        'token_usage': metrics.token_usage,
        'optimization_analysis': {
            'predicted_time': optimization_info.get('estimated_parallel_time', 0),
            'actual_time': metrics.processing_time,
            'prediction_accuracy': abs(optimization_info.get('estimated_parallel_time', 0) - metrics.processing_time),
            'expected_speedup': optimization_info.get('expected_speedup', 1.0),
            'actual_speedup': metrics.parallel_efficiency
        },
        'recommendations': {
            'performance_rating': 'Excellent' if metrics.parallel_efficiency > 2.0 else 
                                 'Good' if metrics.parallel_efficiency > 1.5 else 
                                 'Average' if metrics.parallel_efficiency > 1.0 else 'Poor',
            'suggestions': _generate_performance_suggestions(metrics, optimization_info)
        }
    }

def _generate_performance_suggestions(metrics: PerformanceMetrics, 
                                    optimization_info: Dict[str, Any]) -> List[str]:
    """Generate performance improvement suggestions"""
    suggestions = []
    
    if metrics.parallel_efficiency < 1.2:
        suggestions.append("Consider using larger chunk sizes to reduce overhead")
    
    if metrics.parallel_efficiency > 3.0:
        suggestions.append("Excellent parallel efficiency achieved!")
    
    if metrics.memory_usage_mb > 2000:
        suggestions.append("High memory usage detected - consider smaller chunks")
    
    if metrics.cpu_usage_percent < 50:
        suggestions.append("CPU underutilized - could increase parallel workers")
    
    if metrics.error_count > 0:
        suggestions.append(f"Encountered {metrics.error_count} errors - check document quality")
    
    if len(suggestions) == 0:
        suggestions.append("Processing performance is optimal")
    
    return suggestions
