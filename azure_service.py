import os
import json
from dotenv import load_dotenv
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from openai import AzureOpenAI

load_dotenv()

class AzureDocumentService:
    def __init__(self):
        self.endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
        self.key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
        self.client = DocumentIntelligenceClient(
            endpoint=self.endpoint,
            credential=AzureKeyCredential(self.key)
        )

        # OpenAI setup
        self.openai_client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
        )

    def extract_document_layout(self, file_path):
        """Extract document layout using Azure Document Intelligence"""
        try:
            with open(file_path, "rb") as f:
                # Updated API call format
                poller = self.client.begin_analyze_document(
                    model_id="prebuilt-layout",
                    body=f,
                    content_type="application/octet-stream"
                )
            result = poller.result()
            return result
        except Exception as e:
            print(f"Error in document extraction: {e}")
            return None

    def extract_tables_and_text(self, result):
        """Extract tables and relevant text from document intelligence result"""
        extracted_data = {
            "tables": [],
            "paragraphs": [],
            "key_value_pairs": []
        }

        if result.tables:
            for table in result.tables:
                table_data = []
                for cell in table.cells:
                    table_data.append({
                        "content": cell.content,
                        "row": cell.row_index,
                        "column": cell.column_index
                    })
                extracted_data["tables"].append(table_data)

        if result.paragraphs:
            for paragraph in result.paragraphs:
                extracted_data["paragraphs"].append(paragraph.content)

        if hasattr(result, 'key_value_pairs') and result.key_value_pairs:
            for kv in result.key_value_pairs:
                extracted_data["key_value_pairs"].append({
                    "key": kv.key.content if kv.key else "",
                    "value": kv.value.content if kv.value else ""
                })

        return extracted_data

    def analyze_with_gpt4o(self, extracted_data, document_name):
        """Use GPT-4o to analyze and structure the extracted data"""
        prompt = f"""
        Analyze the following financial document data and extract the required information in JSON format.

        Document: {document_name}

        Extracted Data:
        Tables: {json.dumps(extracted_data.get('tables', []), indent=2)}
        Text: {' '.join(extracted_data.get('paragraphs', [])[:10])}  # First 10 paragraphs
        Key-Value Pairs: {json.dumps(extracted_data.get('key_value_pairs', []), indent=2)}

        Please extract and return ONLY a JSON object with this exact structure:
        {{
            "document_name": "string",
            "advisor_name": "string or null",
            "client_name": "string or null",
            "portfolio_id": "string or null",
            "total_account_value": number or null,
            "date_of_analysis": "string or null",
            "tabular_data": [
                {{
                    "account_number": "string or null",
                    "ticker_symbol": "string (REQUIRED)",
                    "shares_quantity": number (REQUIRED),
                    "current_value": number or null,
                    "cost_basis": number or null
                }}
            ]
        }}

        Important:
        - ticker_symbol and shares_quantity are REQUIRED fields
        - Look for variations like "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units"
        - Focus on tabular data for holdings/portfolio information
        - Return valid JSON only, no explanations
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                messages=[
                    {"role": "system", "content": "You are a financial document analysis expert. Extract data accurately and return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )

            result_text = response.choices[0].message.content.strip()
            # Clean up the response to ensure it's valid JSON
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]
            elif result_text.startswith("```"):
                result_text = result_text[3:-3]

            return json.loads(result_text)
        except Exception as e:
            print(f"Error in GPT-4o analysis: {e}")
            return None
