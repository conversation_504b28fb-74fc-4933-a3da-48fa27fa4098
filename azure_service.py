import os
import json
import re
import difflib
import asyncio
import time
import traceback
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from functools import lru_cache
from dotenv import load_dotenv
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import DocumentContentFormat
from openai import AzureOpenAI

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AzureDocumentService:
    def __init__(self):
        """Initialize Azure Document Service with enhanced error handling and caching"""
        try:
            # Validate environment variables
            required_env_vars = [
                "AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT",
                "AZURE_DOCUMENT_INTELLIGENCE_KEY",
                "AZURE_OPENAI_API_KEY",
                "AZURE_OPENAI_API_VERSION",
                "AZURE_OPENAI_ENDPOINT",
                "AZURE_OPENAI_DEPLOYMENT_NAME"
            ]
            
            missing_vars = [var for var in required_env_vars if not os.getenv(var)]
            if missing_vars:
                raise ValueError(f"Missing required environment variables: {missing_vars}")
            
            self.endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
            self.key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
            
            # Initialize Azure Document Intelligence client with error handling
            self.client = DocumentIntelligenceClient(
                endpoint=self.endpoint,
                credential=AzureKeyCredential(self.key)
            )

            # Initialize OpenAI client with error handling
            self.openai_client = AzureOpenAI(
                api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
                azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
            )
            
            # Cache for processed documents to avoid reprocessing
            self._document_cache = {}
            self._extraction_stats = {
                'documents_processed': 0,
                'tables_extracted': 0,
                'cross_page_tables_detected': 0,
                'errors_encountered': 0
            }
            
            logger.info("Azure Document Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Azure Document Service: {e}")
            raise

    @lru_cache(maxsize=128)
    def _get_file_hash(self, file_path: str) -> str:
        """Generate a hash for file content to enable caching"""
        import hashlib
        try:
            with open(file_path, "rb") as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            logger.warning(f"Could not generate hash for {file_path}: {e}")
            return str(time.time())  # Fallback to timestamp

    def get_extraction_stats(self) -> Dict[str, Any]:
        """Get extraction statistics for monitoring"""
        return self._extraction_stats.copy()

    def extract_document_layout(self, file_path: str, output_format: str = "json") -> Optional[Any]:
        """Extract document layout using Azure Document Intelligence with caching and enhanced error handling
        
        Args:
            file_path: Path to the document file
            output_format: "json" for standard output or "markdown" for markdown format
            
        Returns:
            Document analysis result or None if failed
        """
        start_time = time.time()
        
        try:
            # Validate inputs
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                raise ValueError(f"File is empty: {file_path}")
            
            if file_size > 500 * 1024 * 1024:  # 500MB limit
                raise ValueError(f"File too large ({file_size / 1024 / 1024:.1f}MB): {file_path}")
            
            # Check cache
            file_hash = self._get_file_hash(file_path)
            cache_key = f"{file_hash}_{output_format}"
            
            if cache_key in self._document_cache:
                logger.info(f"Using cached result for {file_path}")
                return self._document_cache[cache_key]
            
            logger.info(f"Processing document: {file_path} ({file_size / 1024:.1f}KB)")
            
            # Read file content
            with open(file_path, "rb") as f:
                file_content = f.read()

            # Configure analysis request
            analyze_request = {
                "body": file_content,
                "content_type": "application/octet-stream"
            }
            
            # Start analysis with appropriate format
            if output_format == "markdown":
                poller = self.client.begin_analyze_document(
                    model_id="prebuilt-layout",
                    output_content_format=DocumentContentFormat.MARKDOWN,
                    **analyze_request
                )
            else:
                poller = self.client.begin_analyze_document(
                    model_id="prebuilt-layout",
                    **analyze_request
                )
            
            result = poller.result()
            
            # Cache the result
            self._document_cache[cache_key] = result
            
            # Update stats
            self._extraction_stats['documents_processed'] += 1
            
            processing_time = time.time() - start_time
            logger.info(f"Document processed successfully in {processing_time:.2f}s")
            
            return result
            
        except Exception as e:
            self._extraction_stats['errors_encountered'] += 1
            error_msg = f"Error in document extraction for {file_path}: {e}"
            logger.error(error_msg)
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Return None instead of raising to allow graceful degradation
            return None

    def extract_tables_and_text(self, result) -> Dict[str, List[Any]]:
        """Extract tables and relevant text from document intelligence result with enhanced error handling"""
        extracted_data = {
            "tables": [],
            "paragraphs": [],
            "key_value_pairs": []
        }

        if not result:
            logger.warning("No result provided to extract_tables_and_text")
            return extracted_data

        try:
            # Extract tables with validation
            if hasattr(result, 'tables') and result.tables:
                for i, table in enumerate(result.tables):
                    try:
                        table_data = []
                        for cell in table.cells:
                            if hasattr(cell, 'content') and hasattr(cell, 'row_index') and hasattr(cell, 'column_index'):
                                table_data.append({
                                    "content": cell.content or "",
                                    "row": cell.row_index,
                                    "column": cell.column_index,
                                    "confidence": getattr(cell, 'confidence', 0.0)
                                })
                        
                        if table_data:  # Only add non-empty tables
                            extracted_data["tables"].append(table_data)
                            self._extraction_stats['tables_extracted'] += 1
                            
                    except Exception as e:
                        logger.warning(f"Error processing table {i}: {e}")
                        continue

            # Extract paragraphs with validation
            if hasattr(result, 'paragraphs') and result.paragraphs:
                for paragraph in result.paragraphs:
                    try:
                        if hasattr(paragraph, 'content') and paragraph.content:
                            content = paragraph.content.strip()
                            if content:  # Only add non-empty paragraphs
                                extracted_data["paragraphs"].append(content)
                    except Exception as e:
                        logger.warning(f"Error processing paragraph: {e}")
                        continue

            # Extract key-value pairs with validation
            if hasattr(result, 'key_value_pairs') and result.key_value_pairs:
                for kv in result.key_value_pairs:
                    try:
                        key_content = ""
                        value_content = ""
                        
                        if hasattr(kv, 'key') and kv.key and hasattr(kv.key, 'content'):
                            key_content = kv.key.content or ""
                        
                        if hasattr(kv, 'value') and kv.value and hasattr(kv.value, 'content'):
                            value_content = kv.value.content or ""
                        
                        if key_content or value_content:  # Only add if at least one is present
                            extracted_data["key_value_pairs"].append({
                                "key": key_content,
                                "value": value_content,
                                "confidence": getattr(kv, 'confidence', 0.0)
                            })
                            
                    except Exception as e:
                        logger.warning(f"Error processing key-value pair: {e}")
                        continue

            logger.info(f"Extracted {len(extracted_data['tables'])} tables, "
                       f"{len(extracted_data['paragraphs'])} paragraphs, "
                       f"{len(extracted_data['key_value_pairs'])} key-value pairs")

        except Exception as e:
            logger.error(f"Error in extract_tables_and_text: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")

        return extracted_data

    def analyze_with_gpt4o_markdown(self, markdown_data: Dict[str, Any], document_name: str) -> Optional[Dict[str, Any]]:
        """Use GPT-4o to analyze filtered markdown content with enhanced error handling and retry logic"""
        
        if not markdown_data:
            logger.warning("No markdown data provided for GPT-4o analysis")
            return None
        
        max_retries = 3
        retry_delay = 1  # Start with 1 second delay
        
        for attempt in range(max_retries):
            try:
                # Prepare focused content for LLM with validation
                filtered_content = {
                    "filtered_tables": markdown_data.get('filtered_tables', []),
                    "table_contexts": markdown_data.get('table_contexts', []),
                    "document_structure": markdown_data.get('markdown_content', '')[:2000]
                }
                
                # Validate that we have some content to analyze
                if not any([
                    filtered_content['filtered_tables'],
                    filtered_content['table_contexts'],
                    filtered_content['document_structure'].strip()
                ]):
                    logger.warning("No meaningful content found for GPT-4o analysis")
                    return None
                
                # Create the prompt with content validation
                tables_json = json.dumps([table.get('table_html', '') for table in filtered_content['filtered_tables']], indent=2)
                contexts_json = json.dumps(filtered_content['table_contexts'], indent=2)
                
                prompt = f"""
                Analyze the following financial document data extracted in structured markdown format and extract the required information in JSON format.

                Document: {document_name}

                FILTERED FINANCIAL TABLES (Pre-screened for relevance):
                {tables_json}

                TABLE CONTEXTS (Headings and context before each table):
                {contexts_json}

                DOCUMENT STRUCTURE (First 2000 characters for context):
                {filtered_content['document_structure']}

                Please extract and return ONLY a JSON object with this exact structure:
                {{
                    "document_name": "string",
                    "advisor_name": "string or null",
                    "client_name": "string or null", 
                    "portfolio_id": "string or null",
                    "total_account_value": number or null,
                    "date_of_analysis": "string or null",
                    "tabular_data": [
                        {{
                            "account_number": "string or null",
                            "ticker_symbol": "string (REQUIRED)",
                            "shares_quantity": number (REQUIRED)",
                            "current_value": number or null,
                            "cost_basis": number or null
                        }}
                    ]
                }}

                IMPORTANT INSTRUCTIONS:
                - ticker_symbol and shares_quantity are REQUIRED fields
                - Focus ONLY on the pre-filtered tables above - these contain the most relevant financial data
                - Use table contexts to understand what each table represents
                - Look for variations like "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units"
                - Extract holdings/portfolio information from the HTML tables
                - The HTML table format preserves cell relationships - use this structure
                - Return valid JSON only, no explanations
                """

                response = self.openai_client.chat.completions.create(
                    model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                    messages=[
                        {"role": "system", "content": "You are a financial document analysis expert. You receive pre-filtered, structured data and extract portfolio information with high accuracy. Focus on the filtered tables provided."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1,
                    max_tokens=2000,
                    timeout=30  # 30 second timeout
                )

                result_text = response.choices[0].message.content.strip()
                
                # Clean up the response to ensure it's valid JSON
                if result_text.startswith("```json"):
                    result_text = result_text[7:-3]
                elif result_text.startswith("```"):
                    result_text = result_text[3:-3]

                # Validate JSON before returning
                parsed_result = json.loads(result_text)
                
                # Basic validation of required structure
                if not isinstance(parsed_result, dict):
                    raise ValueError("GPT-4o response is not a valid JSON object")
                
                logger.info(f"Successfully analyzed document with GPT-4o on attempt {attempt + 1}")
                return parsed_result

            except json.JSONDecodeError as e:
                logger.warning(f"GPT-4o returned invalid JSON on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                
            except Exception as e:
                logger.error(f"Error in GPT-4o markdown analysis attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    logger.error(f"Traceback: {traceback.format_exc()}")
        
        logger.error(f"Failed to analyze with GPT-4o after {max_retries} attempts")
        return None

    def analyze_with_gpt4o(self, extracted_data: Dict[str, Any], document_name: str) -> Optional[Dict[str, Any]]:
        """Use GPT-4o to analyze and structure the extracted data with enhanced error handling"""
        
        if not extracted_data:
            logger.warning("No extracted data provided for GPT-4o analysis")
            return None
        
        max_retries = 3
        retry_delay = 1
        
        for attempt in range(max_retries):
            try:
                # Validate extracted data
                tables = extracted_data.get('tables', [])
                paragraphs = extracted_data.get('paragraphs', [])[:10]  # First 10 paragraphs
                key_value_pairs = extracted_data.get('key_value_pairs', [])
                
                if not any([tables, paragraphs, key_value_pairs]):
                    logger.warning("No meaningful content found in extracted data")
                    return None
                
                # Create the prompt
                prompt = f"""
                Analyze the following financial document data and extract the required information in JSON format.

                Document: {document_name}

                Extracted Data:
                Tables: {json.dumps(tables, indent=2)}
                Text: {' '.join(paragraphs)}
                Key-Value Pairs: {json.dumps(key_value_pairs, indent=2)}

                Please extract and return ONLY a JSON object with this exact structure:
                {{
                    "document_name": "string",
                    "advisor_name": "string or null",
                    "client_name": "string or null",
                    "portfolio_id": "string or null",
                    "total_account_value": number or null,
                    "date_of_analysis": "string or null",
                    "tabular_data": [
                        {{
                            "account_number": "string or null",
                            "ticker_symbol": "string (REQUIRED)",
                            "shares_quantity": number (REQUIRED)",
                            "current_value": number or null,
                            "cost_basis": number or null
                        }}
                    ]
                }}

                Important:
                - ticker_symbol and shares_quantity are REQUIRED fields
                - Look for variations like "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units"
                - Focus on tabular data for holdings/portfolio information
                - Return valid JSON only, no explanations
                """

                response = self.openai_client.chat.completions.create(
                    model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                    messages=[
                        {"role": "system", "content": "You are a financial document analysis expert. Extract data accurately and return only valid JSON."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1,
                    max_tokens=2000,
                    timeout=30
                )

                result_text = response.choices[0].message.content.strip()
                
                # Clean up the response to ensure it's valid JSON
                if result_text.startswith("```json"):
                    result_text = result_text[7:-3]
                elif result_text.startswith("```"):
                    result_text = result_text[3:-3]

                # Validate JSON before returning
                parsed_result = json.loads(result_text)
                
                if not isinstance(parsed_result, dict):
                    raise ValueError("GPT-4o response is not a valid JSON object")
                
                logger.info(f"Successfully analyzed document with GPT-4o (original method) on attempt {attempt + 1}")
                return parsed_result

            except json.JSONDecodeError as e:
                logger.warning(f"GPT-4o returned invalid JSON on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                
            except Exception as e:
                logger.error(f"Error in GPT-4o analysis attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    logger.error(f"Traceback: {traceback.format_exc()}")
        
        logger.error(f"Failed to analyze with GPT-4o (original method) after {max_retries} attempts")
        return None

    async def extract_document_layout_async(self, file_path: str, output_format: str = "json") -> Optional[Any]:
        """Async version of document layout extraction for better performance"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.extract_document_layout, file_path, output_format)

    def clear_cache(self) -> None:
        """Clear the document cache to free memory"""
        cache_size = len(self._document_cache)
        self._document_cache.clear()
        logger.info(f"Cleared document cache ({cache_size} items)")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cache_size': len(self._document_cache),
            'cache_keys': list(self._document_cache.keys())
        }

    def optimize_for_memory(self) -> None:
        """Optimize memory usage by clearing caches and triggering garbage collection"""
        import gc
        
        initial_cache_size = len(self._document_cache)
        
        # Clear old cache entries (keep only 10 most recent)
        if len(self._document_cache) > 10:
            sorted_items = sorted(self._document_cache.items(), key=lambda x: id(x[1]))
            self._document_cache = dict(sorted_items[-10:])
        
        # Force garbage collection
        gc.collect()
        
        final_cache_size = len(self._document_cache)
        logger.info(f"Memory optimization: cache reduced from {initial_cache_size} to {final_cache_size} items")

    def validate_document_structure(self, result) -> Dict[str, Any]:
        """Validate document structure and return analysis metrics"""
        metrics = {
            'has_tables': False,
            'table_count': 0,
            'has_paragraphs': False,
            'paragraph_count': 0,
            'has_key_value_pairs': False,
            'kv_pair_count': 0,
            'has_content': False,
            'content_length': 0,
            'validation_score': 0.0
        }
        
        if not result:
            return metrics
        
        try:
            # Check tables
            if hasattr(result, 'tables') and result.tables:
                metrics['has_tables'] = True
                metrics['table_count'] = len(result.tables)
            
            # Check paragraphs
            if hasattr(result, 'paragraphs') and result.paragraphs:
                metrics['has_paragraphs'] = True
                metrics['paragraph_count'] = len(result.paragraphs)
            
            # Check key-value pairs
            if hasattr(result, 'key_value_pairs') and result.key_value_pairs:
                metrics['has_key_value_pairs'] = True
                metrics['kv_pair_count'] = len(result.key_value_pairs)
            
            # Check content
            if hasattr(result, 'content') and result.content:
                metrics['has_content'] = True
                metrics['content_length'] = len(result.content)
            
            # Calculate validation score
            score = 0
            if metrics['has_tables']: score += 0.4
            if metrics['has_paragraphs']: score += 0.3
            if metrics['has_content']: score += 0.2
            if metrics['has_key_value_pairs']: score += 0.1
            
            metrics['validation_score'] = score
            
        except Exception as e:
            logger.warning(f"Error validating document structure: {e}")
        
        return metrics

    def extract_markdown_content(self, result) -> Dict[str, Any]:
        """Extract and filter tables and text from markdown with enhanced error handling and performance monitoring"""
        start_time = time.time()
        
        try:
            extracted_data = {
                "markdown_content": "",
                "filtered_tables": [],
                "structured_sections": [],
                "table_contexts": [],
                "extraction_metadata": {
                    "total_tables_found": 0,
                    "financial_tables_detected": 0,
                    "processing_time": 0,
                    "content_length": 0
                }
            }

            # Validate input
            if not result:
                logger.warning("No result provided for markdown extraction")
                return extracted_data
            
            # Enhanced content extraction with validation
            markdown_content = ""
            if hasattr(result, 'content'):
                markdown_content = result.content or ""
            elif isinstance(result, str):
                markdown_content = result
            else:
                logger.warning(f"Unexpected result type: {type(result)}")
                return extracted_data
            
            if not markdown_content.strip():
                logger.warning("Empty markdown content provided")
                return extracted_data
            
            extracted_data["markdown_content"] = markdown_content
            extracted_data["extraction_metadata"]["content_length"] = len(markdown_content)
            
            logger.info(f"Processing markdown content: {len(markdown_content)} characters")
            
            # Enhanced table filtering with comprehensive error handling
            try:
                filtered_tables = self._filter_financial_tables_from_markdown(markdown_content)
                extracted_data["filtered_tables"] = filtered_tables
                extracted_data["extraction_metadata"]["total_tables_found"] = len(filtered_tables)
                extracted_data["extraction_metadata"]["financial_tables_detected"] = len([
                    t for t in filtered_tables if t.get('keyword_count', 0) >= 2
                ])
                
                logger.info(f"Found {len(filtered_tables)} filtered tables")
                
            except Exception as e:
                logger.error(f"Error filtering financial tables: {e}")
                extracted_data["filtered_tables"] = []
            
            # Enhanced table context extraction
            try:
                table_contexts = self._extract_table_contexts(markdown_content)
                extracted_data["table_contexts"] = table_contexts
                
                logger.info(f"Extracted {len(table_contexts)} table contexts")
                
            except Exception as e:
                logger.error(f"Error extracting table contexts: {e}")
                extracted_data["table_contexts"] = []
            
            # Update statistics
            processing_time = time.time() - start_time
            extracted_data["extraction_metadata"]["processing_time"] = processing_time
            
            self._extraction_stats['markdown_extractions'] = self._extraction_stats.get('markdown_extractions', 0) + 1
            self._extraction_stats['total_processing_time'] = self._extraction_stats.get('total_processing_time', 0) + processing_time
            
            logger.info(f"Markdown extraction completed in {processing_time:.2f}s")
            
            return extracted_data

        except Exception as e:
            logger.error(f"Critical error in markdown content extraction: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Return basic structure on error
            processing_time = time.time() - start_time
            return {
                "markdown_content": "",
                "filtered_tables": [],
                "structured_sections": [],
                "table_contexts": [],
                "extraction_metadata": {
                    "total_tables_found": 0,
                    "financial_tables_detected": 0,
                    "processing_time": processing_time,
                    "content_length": 0,
                    "error": str(e)
                }
            }

    def _filter_financial_tables_from_markdown(self, markdown_content) -> List[Dict[str, Any]]:
        """Filter tables that likely contain financial data from markdown content with enhanced validation"""
        try:
            if not markdown_content or not markdown_content.strip():
                logger.warning("Empty markdown content provided for table filtering")
                return []
            
            import re
            
            # Enhanced table pattern with comprehensive matching
            table_pattern = r'<table[^>]*>.*?</table>'
            tables = re.findall(table_pattern, markdown_content, re.DOTALL | re.IGNORECASE)
            
            if not tables:
                logger.info("No HTML tables found in markdown content")
                return []
            
            logger.info(f"Found {len(tables)} HTML tables in markdown content")
            
            financial_tables = []
            
            # Enhanced financial keywords with broader coverage
            financial_keywords = [
                'ticker', 'symbol', 'shares', 'quantity', 'units', 'holdings',
                'portfolio', 'value', 'price', 'cost', 'basis', 'account',
                'balance', 'equity', 'bond', 'stock', 'fund', 'asset',
                'investment', 'mutual', 'etf', 'reit', 'dividend', 'cash',
                'market', 'position', 'allocation', 'weight', 'yield',
                'return', 'performance', 'total', 'amount', 'accrued'
            ]
            
            for i, table in enumerate(tables):
                try:
                    table_text = table.lower()
                    
                    # Enhanced keyword counting with context awareness
                    keyword_count = 0
                    found_keywords = []
                    
                    for keyword in financial_keywords:
                        if keyword in table_text:
                            # Check if keyword appears in meaningful context (not just partial matches)
                            pattern = rf'\b{re.escape(keyword)}\b'
                            matches = len(re.findall(pattern, table_text))
                            if matches > 0:
                                keyword_count += matches
                                found_keywords.append(keyword)
                    
                    # Enhanced filtering criteria with multiple validation layers
                    has_core_financial_indicators = any(
                        keyword in table_text for keyword in ['ticker', 'symbol', 'shares', 'quantity']
                    )
                    
                    has_monetary_indicators = '$' in table or '€' in table or '£' in table or any(
                        keyword in table_text for keyword in ['price', 'value', 'cost', 'amount']
                    )
                    
                    has_portfolio_indicators = any(
                        keyword in table_text for keyword in ['portfolio', 'holdings', 'position', 'allocation']
                    )
                    
                    # Multi-criteria financial detection
                    is_financial = (
                        keyword_count >= 3 or  # Has multiple financial keywords
                        has_core_financial_indicators or  # Core financial indicators
                        (has_monetary_indicators and keyword_count >= 2) or  # Money + keywords
                        (has_portfolio_indicators and keyword_count >= 1)  # Portfolio context
                    )
                    
                    # Enhanced table size validation
                    try:
                        row_count = max(1, table_text.count('<tr>'))
                        header_count = table_text.count('<th>')
                        cell_count = table_text.count('<td>') + header_count
                        estimated_cols = max(1, cell_count // row_count) if row_count > 0 else 1
                        
                        # More flexible size constraints
                        valid_size = (
                            2 <= row_count <= 200 and  # Reasonable row count
                            2 <= estimated_cols <= 20 and  # Reasonable column count
                            cell_count >= 4  # Minimum meaningful content
                        )
                        
                    except Exception as e:
                        logger.debug(f"Error calculating table {i} dimensions: {e}")
                        # Fallback to basic validation
                        valid_size = len(table.strip()) > 50  # At least some content
                        row_count = estimated_cols = 1
                    
                    # Apply filtering criteria
                    if is_financial and valid_size:
                        # Calculate confidence score
                        confidence_factors = {
                            'keyword_count': min(1.0, keyword_count / 5),  # Normalize to 0-1
                            'core_indicators': 0.3 if has_core_financial_indicators else 0,
                            'monetary_indicators': 0.2 if has_monetary_indicators else 0,
                            'portfolio_indicators': 0.2 if has_portfolio_indicators else 0,
                            'size_validity': 0.1 if valid_size else 0
                        }
                        
                        confidence_score = sum(confidence_factors.values())
                        
                        financial_tables.append({
                            'table_index': i,
                            'table_html': table,
                            'keyword_count': keyword_count,
                            'found_keywords': found_keywords,
                            'estimated_rows': row_count,
                            'estimated_cols': estimated_cols,
                            'confidence_score': min(1.0, confidence_score),
                            'has_core_indicators': has_core_financial_indicators,
                            'has_monetary_indicators': has_monetary_indicators,
                            'has_portfolio_indicators': has_portfolio_indicators,
                            'filtering_metadata': {
                                'total_cells': cell_count,
                                'header_count': header_count,
                                'processing_timestamp': time.time()
                            }
                        })
                        
                        logger.debug(f"Table {i} identified as financial: "
                                   f"keywords={keyword_count}, confidence={confidence_score:.2f}")
                
                except Exception as e:
                    logger.warning(f"Error processing table {i}: {e}")
                    continue
            
            # Sort by confidence score and keyword count
            financial_tables.sort(key=lambda x: (x['confidence_score'], x['keyword_count']), reverse=True)
            
            logger.info(f"Filtered to {len(financial_tables)} financial tables from {len(tables)} total tables")
            
            return financial_tables
            
        except Exception as e:
            logger.error(f"Critical error in financial table filtering: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def _extract_table_contexts(self, markdown_content) -> List[Dict[str, Any]]:
        """Extract headings and context that appear before tables with enhanced validation"""
        try:
            if not markdown_content or not markdown_content.strip():
                logger.warning("Empty markdown content provided for context extraction")
                return []
            
            import re
            
            # Enhanced table pattern with better splitting
            table_pattern = r'(<table[^>]*>.*?</table>)'
            sections = re.split(table_pattern, markdown_content, flags=re.DOTALL | re.IGNORECASE)
            
            if len(sections) < 2:
                logger.info("No table sections found for context extraction")
                return []
            
            contexts = []
            table_index = 0
            
            # Process sections with enhanced error handling
            for i in range(0, len(sections), 2):  # Even indices are content before tables
                try:
                    if i + 1 < len(sections):  # There's a table after this section
                        context_text = sections[i] if sections[i] else ""
                        table_html = sections[i + 1] if i + 1 < len(sections) else ""
                        
                        # Enhanced heading extraction with multiple patterns
                        headings = []
                        
                        # Standard markdown headings
                        heading_pattern = r'^(#{1,6})\s+(.+)$'
                        md_headings = re.findall(heading_pattern, context_text, re.MULTILINE)
                        
                        for level_chars, text in md_headings:
                            headings.append({
                                'level': len(level_chars),
                                'text': text.strip(),
                                'type': 'markdown'
                            })
                        
                        # HTML headings (h1-h6)
                        html_heading_pattern = r'<h([1-6])[^>]*>(.*?)</h[1-6]>'
                        html_headings = re.findall(html_heading_pattern, context_text, re.IGNORECASE | re.DOTALL)
                        
                        for level, text in html_headings:
                            # Clean HTML content
                            clean_text = re.sub(r'<[^>]+>', '', text).strip()
                            if clean_text:
                                headings.append({
                                    'level': int(level),
                                    'text': clean_text,
                                    'type': 'html'
                                })
                        
                        # Enhanced context extraction with multiple strategies
                        lines = context_text.strip().split('\n')
                        recent_context = ""
                        
                        if lines:
                            # Get last non-empty lines
                            non_empty_lines = [line.strip() for line in lines if line.strip()]
                            if non_empty_lines:
                                # Take last 3 meaningful lines
                                recent_lines = non_empty_lines[-3:]
                                recent_context = '\n'.join(recent_lines)
                            else:
                                recent_context = context_text.strip()
                        
                        # Extract meaningful keywords from context
                        context_keywords = []
                        if recent_context:
                            # Look for financial and document structure keywords
                            keyword_patterns = [
                                r'\b(portfolio|holdings|positions|investments)\b',
                                r'\b(account|statement|report|summary)\b',
                                r'\b(as\s+of|dated|period\s+ending)\b',
                                r'\b(total|subtotal|balance|value)\b'
                            ]
                            
                            for pattern in keyword_patterns:
                                matches = re.findall(pattern, recent_context.lower())
                                context_keywords.extend(matches)
                        
                        # Analyze table characteristics for better context
                        table_characteristics = {}
                        if table_html:
                            try:
                                table_lower = table_html.lower()
                                table_characteristics = {
                                    'has_headers': '<th>' in table_lower,
                                    'estimated_rows': table_lower.count('<tr>'),
                                    'estimated_cells': table_lower.count('<td>') + table_lower.count('<th>'),
                                    'has_financial_keywords': any(
                                        keyword in table_lower 
                                        for keyword in ['ticker', 'symbol', 'shares', 'value', 'price']
                                    )
                                }
                            except Exception as e:
                                logger.debug(f"Error analyzing table characteristics: {e}")
                                table_characteristics = {}
                        
                        # Calculate context relevance score
                        relevance_score = 0.0
                        if headings:
                            relevance_score += 0.4
                        if context_keywords:
                            relevance_score += 0.3
                        if recent_context and len(recent_context.strip()) > 10:
                            relevance_score += 0.2
                        if table_characteristics.get('has_financial_keywords', False):
                            relevance_score += 0.1
                        
                        context_entry = {
                            'headings': headings,
                            'recent_context': recent_context.strip(),
                            'context_keywords': context_keywords,
                            'table_index': table_index,
                            'table_characteristics': table_characteristics,
                            'relevance_score': min(1.0, relevance_score),
                            'context_length': len(context_text),
                            'extraction_metadata': {
                                'section_index': i,
                                'processing_timestamp': time.time()
                            }
                        }
                        
                        contexts.append(context_entry)
                        table_index += 1
                        
                        logger.debug(f"Extracted context for table {table_index-1}: "
                                   f"{len(headings)} headings, relevance={relevance_score:.2f}")
                
                except Exception as e:
                    logger.warning(f"Error processing context section {i}: {e}")
                    continue
            
            # Sort by relevance score
            contexts.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            logger.info(f"Extracted {len(contexts)} table contexts")
            
            return contexts
            
        except Exception as e:
            logger.error(f"Critical error in table context extraction: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def extract_cross_page_tables(self, result) -> List[Dict[str, Any]]:
        """Extract and reconstruct tables that span across multiple pages with enhanced error handling"""
        cross_page_tables = []
        
        try:
            if not result or not hasattr(result, 'tables') or not result.tables:
                logger.info("No tables found in document for cross-page analysis")
                return cross_page_tables
            
            logger.info(f"Analyzing {len(result.tables)} tables for cross-page relationships")
            
            # Group tables by their structural similarity and proximity
            potential_continuations = self._identify_table_continuations(result.tables)
            
            for group_idx, group in enumerate(potential_continuations):
                try:
                    if len(group) > 1:  # Multi-page table detected
                        logger.info(f"Cross-page table detected: {len(group)} tables spanning pages")
                        merged_table = self._merge_cross_page_tables(group)
                        
                        if merged_table and merged_table.get('content'):
                            cross_page_tables.append({
                                'type': 'cross_page',
                                'page_span': f"{group[0].bounding_regions[0].page_number}-{group[-1].bounding_regions[0].page_number}",
                                'total_rows': merged_table['total_rows'],
                                'total_columns': merged_table['total_columns'],
                                'merged_content': merged_table['content'],
                                'original_tables': len(group),
                                'group_index': group_idx
                            })
                            self._extraction_stats['cross_page_tables_detected'] += 1
                    else:
                        # Single page table
                        table = group[0]
                        if hasattr(table, 'bounding_regions') and table.bounding_regions:
                            table_content = self._extract_single_table_content(table)
                            if table_content:
                                cross_page_tables.append({
                                    'type': 'single_page',
                                    'page_span': str(table.bounding_regions[0].page_number),
                                    'total_rows': getattr(table, 'row_count', len(table_content)),
                                    'total_columns': getattr(table, 'column_count', 0),
                                    'merged_content': table_content,
                                    'original_tables': 1,
                                    'group_index': group_idx
                                })
                        
                except Exception as e:
                    logger.warning(f"Error processing table group {group_idx}: {e}")
                    continue
            
            logger.info(f"Cross-page analysis complete: {len(cross_page_tables)} tables processed")
            
        except Exception as e:
            logger.error(f"Error in cross-page table extraction: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
        
        return cross_page_tables
    
    def _identify_table_continuations(self, tables):
        """Identify tables that are likely continuations of each other across pages"""
        import difflib
        
        # Sort tables by page number
        sorted_tables = sorted(tables, key=lambda t: t.bounding_regions[0].page_number)
        
        groups = []
        current_group = [sorted_tables[0]] if sorted_tables else []
        
        for i in range(1, len(sorted_tables)):
            current_table = sorted_tables[i]
            previous_table = sorted_tables[i-1]
            
            # Check if tables are on consecutive pages
            current_page = current_table.bounding_regions[0].page_number
            previous_page = previous_table.bounding_regions[0].page_number
            
            if current_page == previous_page + 1:
                # Check structural similarity
                similarity = self._calculate_table_similarity(previous_table, current_table)
                
                if similarity > 0.7:  # 70% structural similarity threshold
                    current_group.append(current_table)
                else:
                    groups.append(current_group)
                    current_group = [current_table]
            else:
                groups.append(current_group)
                current_group = [current_table]
        
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _calculate_table_similarity(self, table1, table2):
        """Calculate structural similarity between two tables"""
        # Compare column count
        col_similarity = 1.0 if table1.column_count == table2.column_count else 0.5
        
        # Compare header structure if available
        header_similarity = self._compare_table_headers(table1, table2)
        
        # Compare column widths/positions (approximate)
        position_similarity = self._compare_column_positions(table1, table2)
        
        # Weighted average
        return (col_similarity * 0.4 + header_similarity * 0.4 + position_similarity * 0.2)
    
    def _compare_table_headers(self, table1, table2) -> float:
        """Compare header cells between two tables with enhanced validation"""
        try:
            if not (hasattr(table1, 'cells') and hasattr(table2, 'cells')):
                logger.warning("Tables missing cells attribute")
                return 0.5
            
            # Extract headers with enhanced validation
            table1_headers = []
            table2_headers = []
            
            for cell in table1.cells:
                try:
                    if (hasattr(cell, 'kind') and getattr(cell, 'kind', None) == 'columnHeader') or cell.row_index == 0:
                        content = getattr(cell, 'content', '').lower().strip()
                        if content:  # Only add non-empty headers
                            table1_headers.append(content)
                except Exception as e:
                    logger.debug(f"Error processing table1 cell: {e}")
                    continue
            
            for cell in table2.cells:
                try:
                    if (hasattr(cell, 'kind') and getattr(cell, 'kind', None) == 'columnHeader') or cell.row_index == 0:
                        content = getattr(cell, 'content', '').lower().strip()
                        if content:  # Only add non-empty headers
                            table2_headers.append(content)
                except Exception as e:
                    logger.debug(f"Error processing table2 cell: {e}")
                    continue
            
            # Enhanced comparison logic
            if not table1_headers and not table2_headers:
                return 1.0  # Both have no headers - perfect match
            elif not table1_headers or not table2_headers:
                return 0.3  # One has headers, one doesn't - low similarity
            
            # Use enhanced sequence matching with fuzzy comparison
            import difflib
            
            # Direct sequence comparison
            matcher = difflib.SequenceMatcher(None, table1_headers, table2_headers)
            direct_similarity = matcher.ratio()
            
            # Individual header matching (for reordered headers)
            matched_headers = 0
            total_headers = max(len(table1_headers), len(table2_headers))
            
            for h1 in table1_headers:
                best_match = 0
                for h2 in table2_headers:
                    # Calculate individual header similarity
                    header_matcher = difflib.SequenceMatcher(None, h1, h2)
                    similarity = header_matcher.ratio()
                    if similarity > 0.8:  # Strong match threshold
                        best_match = max(best_match, similarity)
                
                if best_match > 0.8:
                    matched_headers += 1
            
            individual_similarity = matched_headers / total_headers if total_headers > 0 else 0
            
            # Combine both approaches
            final_similarity = max(direct_similarity, individual_similarity * 0.8)
            
            logger.debug(f"Header comparison: direct={direct_similarity:.2f}, individual={individual_similarity:.2f}, final={final_similarity:.2f}")
            
            return final_similarity
            
        except Exception as e:
            logger.warning(f"Error comparing table headers: {e}")
            return 0.5  # Neutral score on error
    
    def _compare_column_positions(self, table1, table2) -> float:
        """Compare column positions between tables with enhanced error handling"""
        try:
            if not (hasattr(table1, 'cells') and hasattr(table2, 'cells')):
                logger.warning("Tables missing cells for position comparison")
                return 0.5
            
            # Enhanced position extraction with validation
            table1_positions = []
            table2_positions = []
            
            # Extract first row positions for table1
            for cell in table1.cells:
                try:
                    if (hasattr(cell, 'row_index') and cell.row_index == 0 and 
                        hasattr(cell, 'bounding_regions') and cell.bounding_regions and
                        hasattr(cell.bounding_regions[0], 'polygon')):
                        
                        polygon = cell.bounding_regions[0].polygon
                        if polygon and len(polygon) > 0:
                            x_coord = polygon[0].x if hasattr(polygon[0], 'x') else polygon[0]
                            table1_positions.append((getattr(cell, 'column_index', 0), x_coord))
                except Exception as e:
                    logger.debug(f"Error extracting position from table1 cell: {e}")
                    continue
            
            # Extract first row positions for table2
            for cell in table2.cells:
                try:
                    if (hasattr(cell, 'row_index') and cell.row_index == 0 and 
                        hasattr(cell, 'bounding_regions') and cell.bounding_regions and
                        hasattr(cell.bounding_regions[0], 'polygon')):
                        
                        polygon = cell.bounding_regions[0].polygon
                        if polygon and len(polygon) > 0:
                            x_coord = polygon[0].x if hasattr(polygon[0], 'x') else polygon[0]
                            table2_positions.append((getattr(cell, 'column_index', 0), x_coord))
                except Exception as e:
                    logger.debug(f"Error extracting position from table2 cell: {e}")
                    continue
            
            # Enhanced position comparison
            if not table1_positions or not table2_positions:
                logger.debug("No valid positions found for comparison")
                return 0.5
            
            if len(table1_positions) != len(table2_positions):
                # Allow slight column count differences
                ratio = min(len(table1_positions), len(table2_positions)) / max(len(table1_positions), len(table2_positions))
                if ratio < 0.8:  # Significant difference
                    return ratio * 0.5  # Penalize but don't completely dismiss
            
            # Sort positions by column index
            try:
                sorted_pos1 = sorted(table1_positions, key=lambda x: x[0])
                sorted_pos2 = sorted(table2_positions, key=lambda x: x[0])
            except Exception as e:
                logger.warning(f"Error sorting positions: {e}")
                return 0.5
            
            # Calculate relative position similarities
            position_similarities = []
            min_length = min(len(sorted_pos1), len(sorted_pos2))
            
            for i in range(min_length):
                try:
                    _, pos1 = sorted_pos1[i]
                    _, pos2 = sorted_pos2[i]
                    
                    # Normalize positions relative to first column
                    if i == 0:
                        baseline_pos1, baseline_pos2 = pos1, pos2
                        position_similarities.append(1.0)  # First column always matches
                    else:
                        # Calculate relative spacing
                        rel_pos1 = pos1 - baseline_pos1
                        rel_pos2 = pos2 - baseline_pos2
                        
                        if rel_pos1 == 0 and rel_pos2 == 0:
                            similarity = 1.0
                        elif rel_pos1 == 0 or rel_pos2 == 0:
                            similarity = 0.0
                        else:
                            # Calculate proportional similarity
                            ratio = min(rel_pos1, rel_pos2) / max(rel_pos1, rel_pos2)
                            similarity = ratio if ratio > 0.5 else 0.0  # Threshold for meaningful similarity
                        
                        position_similarities.append(similarity)
                        
                except Exception as e:
                    logger.debug(f"Error calculating position similarity for column {i}: {e}")
                    position_similarities.append(0.5)  # Neutral score
            
            # Calculate final similarity score
            if position_similarities:
                avg_similarity = sum(position_similarities) / len(position_similarities)
                logger.debug(f"Position similarity: {avg_similarity:.2f} from {len(position_similarities)} comparisons")
                return avg_similarity
            else:
                return 0.5
                
        except Exception as e:
            logger.warning(f"Error in column position comparison: {e}")
            return 0.5  # Neutral score on error
    
    def _merge_cross_page_tables(self, table_group) -> Optional[Dict[str, Any]]:
        """Merge multiple tables that span across pages with enhanced error handling"""
        try:
            if not table_group:
                logger.warning("Empty table group provided for merging")
                return None
            
            logger.info(f"Merging {len(table_group)} tables across pages")
            
            merged_content = []
            total_rows = 0
            
            # Enhanced column count determination
            column_counts = []
            for table in table_group:
                if hasattr(table, 'column_count') and table.column_count > 0:
                    column_counts.append(table.column_count)
            
            if not column_counts:
                logger.warning("No valid column counts found in table group")
                return None
            
            # Use most common column count or first table's count
            from collections import Counter
            most_common_count = Counter(column_counts).most_common(1)[0][0]
            total_columns = most_common_count
            
            # Extract and validate headers from first table
            first_table = table_group[0]
            headers = []
            
            try:
                if hasattr(first_table, 'cells'):
                    # Enhanced header extraction with multiple strategies
                    header_candidates = {}
                    
                    for cell in first_table.cells:
                        try:
                            col_idx = getattr(cell, 'column_index', 0)
                            row_idx = getattr(cell, 'row_index', 0)
                            content = getattr(cell, 'content', '').strip()
                            is_header = getattr(cell, 'kind', None) == 'columnHeader'
                            
                            # Multiple header detection strategies
                            if is_header or row_idx == 0:
                                if col_idx not in header_candidates or is_header:
                                    header_candidates[col_idx] = {
                                        'column_index': col_idx,
                                        'content': content,
                                        'is_header': True
                                    }
                        except Exception as e:
                            logger.debug(f"Error processing header cell: {e}")
                            continue
                    
                    # Sort headers by column index
                    if header_candidates:
                        headers = [header_candidates[col] for col in sorted(header_candidates.keys())]
                        merged_content.append(headers)
                        total_rows += 1
                        logger.info(f"Extracted {len(headers)} headers from first table")
                
            except Exception as e:
                logger.warning(f"Error extracting headers: {e}")
            
            # Process all tables in the group with enhanced error handling
            for table_index, table in enumerate(table_group):
                try:
                    if not hasattr(table, 'cells'):
                        logger.warning(f"Table {table_index} missing cells attribute")
                        continue
                    
                    # Group cells by row with validation
                    table_rows = {}
                    
                    for cell in table.cells:
                        try:
                            row_idx = getattr(cell, 'row_index', 0)
                            col_idx = getattr(cell, 'column_index', 0)
                            content = getattr(cell, 'content', '').strip()
                            is_header = getattr(cell, 'kind', None) == 'columnHeader'
                            
                            # Skip headers for continuation tables (not first table)
                            if table_index > 0 and (row_idx == 0 or is_header):
                                continue
                            
                            # Skip header row for first table if already processed
                            if table_index == 0 and row_idx == 0 and headers:
                                continue
                            
                            if row_idx not in table_rows:
                                table_rows[row_idx] = {}
                            
                            table_rows[row_idx][col_idx] = {
                                'column_index': col_idx,
                                'content': content,
                                'is_header': False
                            }
                            
                        except Exception as e:
                            logger.debug(f"Error processing cell in table {table_index}: {e}")
                            continue
                    
                    # Add rows to merged content in sorted order
                    for row_idx in sorted(table_rows.keys()):
                        try:
                            # Convert row dict to list format, filling missing columns
                            row_list = []
                            for col_idx in range(total_columns):
                                if col_idx in table_rows[row_idx]:
                                    row_list.append(table_rows[row_idx][col_idx])
                                else:
                                    # Fill missing columns with empty cells
                                    row_list.append({
                                        'column_index': col_idx,
                                        'content': '',
                                        'is_header': False
                                    })
                            
                            if any(cell['content'].strip() for cell in row_list):  # Only add non-empty rows
                                merged_content.append(row_list)
                                total_rows += 1
                                
                        except Exception as e:
                            logger.warning(f"Error processing row {row_idx} in table {table_index}: {e}")
                            continue
                
                except Exception as e:
                    logger.warning(f"Error processing table {table_index}: {e}")
                    continue
            
            # Validation and final result
            if not merged_content:
                logger.warning("No content extracted from table group")
                return None
            
            result = {
                'content': merged_content,
                'total_rows': total_rows,
                'total_columns': total_columns,
                'source_tables': len(table_group)
            }
            
            logger.info(f"Successfully merged {len(table_group)} tables: {total_rows} rows, {total_columns} columns")
            return result
            
        except Exception as e:
            logger.error(f"Critical error merging cross-page tables: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def _extract_single_table_content(self, table):
        """Extract content from a single table"""
        table_rows = {}
        
        # Group cells by row
        for cell in table.cells:
            row_idx = cell.row_index
            if row_idx not in table_rows:
                table_rows[row_idx] = []
            
            table_rows[row_idx].append({
                'column_index': cell.column_index,
                'content': cell.content,
                'is_header': getattr(cell, 'kind', None) == 'columnHeader'
            })
        
        # Convert to list format
        content = []
        for row_idx in sorted(table_rows.keys()):
            content.append(table_rows[row_idx])
        
        return content

    def extract_markdown_content_with_cross_page(self, result):
        """Enhanced markdown extraction with cross-page table detection"""
        extracted_data = {
            "markdown_content": "",
            "filtered_tables": [],
            "cross_page_tables": [],
            "structured_sections": [],
            "table_contexts": []
        }

        if hasattr(result, 'content') and result.content:
            extracted_data["markdown_content"] = result.content
            
            # Extract cross-page tables first
            cross_page_tables = self.extract_cross_page_tables(result)
            extracted_data["cross_page_tables"] = cross_page_tables
            
            # Convert cross-page tables to HTML for filtering
            cross_page_html_tables = self._convert_cross_page_to_html(cross_page_tables)
            
            # Extract regular markdown tables
            regular_tables = self._filter_financial_tables_from_markdown(result.content)
            
            # Combine and prioritize cross-page tables
            all_tables = cross_page_html_tables + regular_tables
            
            # Apply enhanced filtering with cross-page logic
            filtered_tables = self._apply_enhanced_filtering(all_tables)
            extracted_data["filtered_tables"] = filtered_tables
            
            # Extract table contexts
            table_contexts = self._extract_table_contexts(result.content)
            extracted_data["table_contexts"] = table_contexts

        return extracted_data

    def _convert_cross_page_to_html(self, cross_page_tables) -> List[Dict[str, Any]]:
        """Convert cross-page table data to HTML format for consistent processing with enhanced validation"""
        try:
            if not cross_page_tables:
                logger.info("No cross-page tables to convert")
                return []
            
            logger.info(f"Converting {len(cross_page_tables)} cross-page tables to HTML format")
            
            html_tables = []
            
            for i, table_info in enumerate(cross_page_tables):
                try:
                    merged_content = table_info.get('merged_content', [])
                    
                    if not merged_content:
                        logger.debug(f"Skipping cross-page table {i}: no merged content")
                        continue
                    
                    # Enhanced HTML table building with validation
                    html_parts = ['<table>']
                    processed_rows = 0
                    
                    for row_idx, row_cells in enumerate(merged_content):
                        try:
                            if not row_cells or not isinstance(row_cells, list):
                                logger.debug(f"Skipping invalid row {row_idx} in table {i}")
                                continue
                            
                            html_parts.append('<tr>')
                            
                            # Enhanced cell sorting and validation
                            valid_cells = []
                            for cell in row_cells:
                                if isinstance(cell, dict) and 'column_index' in cell:
                                    valid_cells.append(cell)
                                else:
                                    logger.debug(f"Skipping invalid cell in row {row_idx}")
                            
                            if not valid_cells:
                                html_parts.append('</tr>')
                                continue
                            
                            # Sort cells by column index for proper ordering
                            sorted_cells = sorted(valid_cells, key=lambda x: x.get('column_index', 0))
                            
                            for cell in sorted_cells:
                                try:
                                    # Enhanced header detection
                                    is_header = (
                                        row_idx == 0 or 
                                        cell.get('is_header', False) or
                                        cell.get('kind') == 'columnHeader'
                                    )
                                    
                                    tag = 'th' if is_header else 'td'
                                    content = str(cell.get('content', '')).strip()
                                    
                                    # Enhanced content cleaning with better preservation
                                    if content:
                                        # Clean content but preserve important financial characters
                                        content = re.sub(r'[^\w\s\.\$\%\-\+\(\),:/]', '', content)
                                        content = re.sub(r'\s+', ' ', content)  # Normalize whitespace
                                        content = content.strip()
                                    
                                    # Handle empty cells
                                    if not content:
                                        content = '&nbsp;'  # Proper HTML empty cell
                                    
                                    html_parts.append(f'<{tag}>{content}</{tag}>')
                                    
                                except Exception as e:
                                    logger.debug(f"Error processing cell in row {row_idx}: {e}")
                                    html_parts.append('<td>&nbsp;</td>')
                                    continue
                            
                            html_parts.append('</tr>')
                            processed_rows += 1
                            
                        except Exception as e:
                            logger.warning(f"Error processing row {row_idx} in table {i}: {e}")
                            continue
                    
                    html_parts.append('</table>')
                    
                    if processed_rows == 0:
                        logger.warning(f"No valid rows processed for cross-page table {i}")
                        continue
                    
                    html_table = ''.join(html_parts)
                    
                    # Enhanced financial keyword detection with scoring
                    table_text = html_table.lower()
                    financial_keywords = [
                        'ticker', 'symbol', 'shares', 'quantity', 'units', 'holdings',
                        'portfolio', 'value', 'price', 'cost', 'basis', 'account',
                        'balance', 'equity', 'bond', 'stock', 'fund', 'asset',
                        'investment', 'mutual', 'etf', 'reit', 'dividend', 'cash',
                        'market', 'position', 'allocation', 'weight', 'yield'
                    ]
                    
                    keyword_matches = {}
                    total_keyword_count = 0
                    
                    for keyword in financial_keywords:
                        pattern = rf'\b{re.escape(keyword)}\b'
                        matches = len(re.findall(pattern, table_text))
                        if matches > 0:
                            keyword_matches[keyword] = matches
                            total_keyword_count += matches
                    
                    # Enhanced cross-page info creation with error handling
                    try:
                        # Import here to avoid circular dependencies
                        from models import CrossPageTableInfo
                        
                        cross_page_info = CrossPageTableInfo(
                            table_type=table_info.get('type', 'cross_page'),
                            page_span=table_info.get('page_span', '1'),
                            total_rows=table_info.get('total_rows', len(merged_content)),
                            total_columns=table_info.get('total_columns', 0),
                            original_tables=table_info.get('original_tables', 1)
                        )
                        
                        cross_page_dict = cross_page_info.dict()
                        confidence_score = cross_page_info.confidence_score
                        
                    except Exception as e:
                        logger.warning(f"Error creating CrossPageTableInfo for table {i}: {e}")
                        # Fallback cross-page info
                        cross_page_dict = {
                            'table_type': table_info.get('type', 'cross_page'),
                            'page_span': table_info.get('page_span', '1'),
                            'total_rows': table_info.get('total_rows', len(merged_content)),
                            'total_columns': table_info.get('total_columns', 0),
                            'original_tables': table_info.get('original_tables', 1),
                            'confidence_score': 0.7  # Default confidence
                        }
                        confidence_score = 0.7
                    
                    # Calculate enhanced relevance score
                    relevance_factors = {
                        'keyword_density': min(1.0, total_keyword_count / 10),
                        'cross_page_bonus': 0.3 if table_info.get('type') == 'cross_page' else 0,
                        'size_factor': min(1.0, processed_rows / 20),  # Normalize by reasonable size
                        'diversity_factor': min(1.0, len(keyword_matches) / 5)  # Keyword variety
                    }
                    
                    relevance_score = sum(relevance_factors.values()) / len(relevance_factors)
                    
                    html_table_data = {
                        'table_index': f"cross_page_{i}",
                        'table_html': html_table,
                        'keyword_count': total_keyword_count,
                        'keyword_matches': keyword_matches,
                        'estimated_rows': processed_rows,
                        'estimated_cols': table_info.get('total_columns', 0),
                        'is_cross_page': table_info.get('type') == 'cross_page',
                        'cross_page_info': cross_page_dict,
                        'source': 'cross_page_detection',
                        'confidence_score': confidence_score,
                        'relevance_score': relevance_score,
                        'processing_metadata': {
                            'original_rows': len(merged_content),
                            'processed_rows': processed_rows,
                            'conversion_timestamp': time.time()
                        }
                    }
                    
                    html_tables.append(html_table_data)
                    
                    logger.debug(f"Converted cross-page table {i}: "
                               f"{processed_rows} rows, {total_keyword_count} keywords, "
                               f"relevance={relevance_score:.2f}")
                    
                except Exception as e:
                    logger.warning(f"Error processing cross-page table {i}: {e}")
                    continue
            
            # Sort by relevance score
            html_tables.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            
            logger.info(f"Successfully converted {len(html_tables)} cross-page tables to HTML")
            
            return html_tables
            
        except Exception as e:
            logger.error(f"Critical error converting cross-page tables to HTML: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def _apply_enhanced_filtering(self, all_tables) -> List[Dict[str, Any]]:
        """Apply enhanced Pydantic-based filtering with cross-page prioritization and comprehensive validation"""
        try:
            if not all_tables:
                logger.info("No tables provided for enhanced filtering")
                return []
            
            logger.info(f"Applying enhanced filtering to {len(all_tables)} tables")
            
            # Import models with error handling
            try:
                from models import EnhancedTableFilterCriteria, EnhancedFilteredTableData
            except ImportError as e:
                logger.warning(f"Could not import enhanced models: {e}, falling back to basic filtering")
                return self._apply_basic_filtering(all_tables)
            
            # Create filtering criteria with validation
            try:
                criteria = EnhancedTableFilterCriteria()
            except Exception as e:
                logger.warning(f"Could not create filter criteria: {e}, using defaults")
                criteria = None
            
            filtered_tables = []
            
            for table_data in all_tables:
                try:
                    # Validate table data structure
                    if not isinstance(table_data, dict):
                        logger.debug("Skipping invalid table data (not a dict)")
                        continue
                    
                    # Extract and validate cross-page info
                    cross_page_info = table_data.get('cross_page_info')
                    
                    # Enhanced table data validation
                    table_index = table_data.get('table_index', f"unknown_{len(filtered_tables)}")
                    table_html = table_data.get('table_html', '')
                    keyword_count = max(0, int(table_data.get('keyword_count', 0)))
                    estimated_rows = max(0, int(table_data.get('estimated_rows', 0)))
                    estimated_cols = max(0, int(table_data.get('estimated_cols', 0)))
                    is_cross_page = bool(table_data.get('is_cross_page', False))
                    
                    # Basic validation checks
                    if not table_html.strip():
                        logger.debug(f"Skipping table {table_index}: empty HTML content")
                        continue
                    
                    if estimated_rows == 0 and estimated_cols == 0:
                        logger.debug(f"Skipping table {table_index}: no valid dimensions")
                        continue
                    
                    # Create enhanced filtered table data with error handling
                    try:
                        enhanced_table = EnhancedFilteredTableData(
                            table_index=table_index,
                            table_html=table_html,
                            keyword_count=keyword_count,
                            estimated_rows=estimated_rows,
                            estimated_cols=estimated_cols,
                            is_cross_page=is_cross_page,
                            cross_page_info=cross_page_info
                        )
                        
                        # Apply Pydantic filtering logic with criteria
                        if criteria and enhanced_table.should_pass_to_llm(criteria):
                            relevance_score = enhanced_table.relevance_score
                            filtering_reason = 'enhanced_pydantic_logic'
                            pydantic_validated = True
                        else:
                            # Fallback filtering logic
                            relevance_score = self._calculate_fallback_relevance(table_data)
                            if relevance_score >= 0.3:  # Threshold for inclusion
                                filtering_reason = 'fallback_logic'
                                pydantic_validated = False
                            else:
                                logger.debug(f"Table {table_index} filtered out: low relevance ({relevance_score:.2f})")
                                continue
                        
                    except Exception as e:
                        logger.warning(f"Error creating enhanced table data for {table_index}: {e}")
                        # Fallback to basic validation
                        if keyword_count >= 2 or is_cross_page:
                            relevance_score = 0.5
                            filtering_reason = 'fallback_basic'
                            pydantic_validated = False
                        else:
                            continue
                    
                    # Create filtered result
                    filtered_result = {
                        **table_data,  # Include all original data
                        'relevance_score': relevance_score,
                        'pydantic_validated': pydantic_validated,
                        'filtering_reason': filtering_reason,
                        'filtering_metadata': {
                            'processing_timestamp': time.time(),
                            'original_keyword_count': keyword_count,
                            'enhanced_validation': pydantic_validated
                        }
                    }
                    
                    # Add cross-page prioritization
                    if is_cross_page:
                        filtered_result['relevance_score'] += 0.2  # Boost cross-page tables
                        filtered_result['cross_page_priority'] = True
                    
                    filtered_tables.append(filtered_result)
                    
                    logger.debug(f"Table {table_index} passed filtering: "
                               f"relevance={relevance_score:.2f}, "
                               f"cross_page={is_cross_page}, "
                               f"keywords={keyword_count}")
                    
                except Exception as e:
                    logger.warning(f"Error processing table in enhanced filtering: {e}")
                    # Try basic inclusion for problematic tables
                    if table_data.get('keyword_count', 0) >= 2:
                        filtered_tables.append({
                            **table_data,
                            'relevance_score': 0.3,
                            'pydantic_validated': False,
                            'filtering_reason': 'emergency_fallback'
                        })
                    continue
            
            # Enhanced sorting with multiple criteria
            def sort_key(table):
                return (
                    table.get('cross_page_priority', False),  # Cross-page first
                    table.get('relevance_score', 0),          # Then by relevance
                    table.get('keyword_count', 0),            # Then by keywords
                    table.get('estimated_rows', 0)            # Then by size
                )
            
            filtered_tables.sort(key=sort_key, reverse=True)
            
            # Log filtering results
            cross_page_count = sum(1 for t in filtered_tables if t.get('is_cross_page', False))
            pydantic_count = sum(1 for t in filtered_tables if t.get('pydantic_validated', False))
            
            logger.info(f"Enhanced filtering complete: {len(filtered_tables)} tables passed "
                       f"({cross_page_count} cross-page, {pydantic_count} Pydantic validated)")
            
            return filtered_tables
            
        except Exception as e:
            logger.error(f"Critical error in enhanced filtering: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            # Emergency fallback to basic filtering
            return self._apply_basic_filtering(all_tables)
    
    def _calculate_fallback_relevance(self, table_data: Dict[str, Any]) -> float:
        """Calculate relevance score using fallback logic when Pydantic models fail"""
        try:
            score = 0.0
            
            # Keyword count factor
            keyword_count = table_data.get('keyword_count', 0)
            score += min(0.4, keyword_count * 0.1)
            
            # Size factor
            rows = table_data.get('estimated_rows', 0)
            cols = table_data.get('estimated_cols', 0)
            if 3 <= rows <= 100 and 2 <= cols <= 15:
                score += 0.2
            
            # Cross-page bonus
            if table_data.get('is_cross_page', False):
                score += 0.3
            
            # Content quality factor
            html_content = table_data.get('table_html', '')
            if len(html_content) > 100:  # Substantial content
                score += 0.1
            
            return min(1.0, score)
            
        except Exception as e:
            logger.debug(f"Error calculating fallback relevance: {e}")
            return 0.0
    
    def _apply_basic_filtering(self, all_tables: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Basic filtering fallback when enhanced filtering fails"""
        try:
            filtered_tables = []
            
            for table_data in all_tables:
                keyword_count = table_data.get('keyword_count', 0)
                is_cross_page = table_data.get('is_cross_page', False)
                
                # Basic inclusion criteria
                if keyword_count >= 2 or is_cross_page:
                    filtered_tables.append({
                        **table_data,
                        'relevance_score': 0.5,
                        'pydantic_validated': False,
                        'filtering_reason': 'basic_fallback'
                    })
            
            # Sort by keyword count and cross-page status
            filtered_tables.sort(
                key=lambda x: (x.get('is_cross_page', False), x.get('keyword_count', 0)), 
                reverse=True
            )
            
            logger.info(f"Basic filtering applied: {len(filtered_tables)} tables passed")
            return filtered_tables
            
        except Exception as e:
            logger.error(f"Error in basic filtering fallback: {e}")
            return []

    def analyze_with_gpt4o_enhanced_markdown(self, markdown_data, document_name):
        """Enhanced GPT-4o analysis with cross-page table awareness"""
        
        # Prepare enhanced content for LLM
        filtered_content = {
            "filtered_tables": markdown_data.get('filtered_tables', []),
            "cross_page_tables": markdown_data.get('cross_page_tables', []),
            "table_contexts": markdown_data.get('table_contexts', []),
            "document_structure": markdown_data.get('markdown_content', '')[:2000]
        }
        
        # Separate cross-page and regular tables for focused processing
        cross_page_tables = [t for t in filtered_content['filtered_tables'] if t.get('is_cross_page', False)]
        regular_tables = [t for t in filtered_content['filtered_tables'] if not t.get('is_cross_page', False)]
        
        prompt = f"""
        Analyze the following financial document data with ENHANCED cross-page table detection and extract the required information in JSON format.

        Document: {document_name}

        🔄 CROSS-PAGE TABLES (High Priority - Likely contain complete portfolio data):
        {json.dumps([{'html': table['table_html'], 'info': table.get('cross_page_info', {}), 'score': table.get('relevance_score', 0)} for table in cross_page_tables], indent=2)}

        📊 REGULAR FILTERED TABLES (Pre-screened for relevance):
        {json.dumps([{'html': table['table_html'], 'keywords': table.get('keyword_count', 0), 'score': table.get('relevance_score', 0)} for table in regular_tables], indent=2)}

        📋 TABLE CONTEXTS (Headings and context):
        {json.dumps(filtered_content['table_contexts'], indent=2)}

        📄 DOCUMENT STRUCTURE (First 2000 characters for context):
        {filtered_content['document_structure']}

        Please extract and return ONLY a JSON object with this exact structure:
        {{
            "document_name": "string",
            "advisor_name": "string or null",
            "client_name": "string or null", 
            "portfolio_id": "string or null",
            "total_account_value": number or null,
            "date_of_analysis": "string or null",
            "tabular_data": [
                {{
                    "account_number": "string or null",
                    "ticker_symbol": "string (REQUIRED)",
                    "shares_quantity": number (REQUIRED),
                    "current_value": number or null,
                    "cost_basis": number or null
                }}
            ]
        }}

        ENHANCED PROCESSING INSTRUCTIONS:
        - PRIORITIZE CROSS-PAGE TABLES: These often contain the most complete portfolio data
        - Cross-page tables spanning multiple pages typically have comprehensive holdings information
        - Use the cross-page table info (page span, confidence) to understand data completeness
        - ticker_symbol and shares_quantity are REQUIRED fields
        - Look for variations like "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units"
        - Cross-page tables preserve complete relationships across page breaks
        - Regular tables supplement the cross-page data
        - Return valid JSON only, no explanations
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                messages=[
                    {"role": "system", "content": "You are a financial document analysis expert with advanced cross-page table reconstruction capabilities. Prioritize cross-page tables as they contain the most complete portfolio information."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=3000  # Increased for complex cross-page data
            )

            result_text = response.choices[0].message.content.strip()
            # Clean up the response to ensure it's valid JSON
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]
            elif result_text.startswith("```"):
                result_text = result_text[3:-3]

            return json.loads(result_text)
        except Exception as e:
            print(f"Error in enhanced GPT-4o markdown analysis: {e}")
            return None
