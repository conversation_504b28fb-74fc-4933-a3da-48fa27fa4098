import os
import json
import re
import difflib
from dotenv import load_dotenv
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import DocumentContentFormat
from openai import AzureOpenAI

load_dotenv()

class AzureDocumentService:
    def __init__(self):
        self.endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
        self.key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
        self.client = DocumentIntelligenceClient(
            endpoint=self.endpoint,
            credential=AzureKeyCredential(self.key)
        )

        # OpenAI setup
        self.openai_client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
        )

    def extract_document_layout(self, file_path, output_format="json"):
        """Extract document layout using Azure Document Intelligence
        
        Args:
            file_path: Path to the document file
            output_format: "json" for standard output or "markdown" for markdown format
        """
        try:
            # Read file content first
            with open(file_path, "rb") as f:
                file_content = f.read()

            # Configure analysis request with optional markdown output
            analyze_request = {
                "body": file_content,
                "content_type": "application/octet-stream"
            }
            
            # Add markdown output format if requested
            if output_format == "markdown":
                poller = self.client.begin_analyze_document(
                    model_id="prebuilt-layout",
                    output_content_format=DocumentContentFormat.MARKDOWN,
                    **analyze_request
                )
            else:
                poller = self.client.begin_analyze_document(
                    model_id="prebuilt-layout",
                    **analyze_request
                )
            
            result = poller.result()
            return result
        except Exception as e:
            print(f"Error in document extraction: {e}")
            print(f"File path: {file_path}")
            print(f"File exists: {os.path.exists(file_path)}")
            return None

    def extract_tables_and_text(self, result):
        """Extract tables and relevant text from document intelligence result"""
        extracted_data = {
            "tables": [],
            "paragraphs": [],
            "key_value_pairs": []
        }

        if result.tables:
            for table in result.tables:
                table_data = []
                for cell in table.cells:
                    table_data.append({
                        "content": cell.content,
                        "row": cell.row_index,
                        "column": cell.column_index
                    })
                extracted_data["tables"].append(table_data)

        if result.paragraphs:
            for paragraph in result.paragraphs:
                extracted_data["paragraphs"].append(paragraph.content)

        if hasattr(result, 'key_value_pairs') and result.key_value_pairs:
            for kv in result.key_value_pairs:
                extracted_data["key_value_pairs"].append({
                    "key": kv.key.content if kv.key else "",
                    "value": kv.value.content if kv.value else ""
                })

        return extracted_data

    def analyze_with_gpt4o_markdown(self, markdown_data, document_name):
        """Use GPT-4o to analyze filtered markdown content for better financial data extraction"""
        
        # Prepare focused content for LLM
        filtered_content = {
            "filtered_tables": markdown_data.get('filtered_tables', []),
            "table_contexts": markdown_data.get('table_contexts', []),
            "document_structure": markdown_data.get('markdown_content', '')[:2000]  # First 2000 chars for context
        }
        
        prompt = f"""
        Analyze the following financial document data extracted in structured markdown format and extract the required information in JSON format.

        Document: {document_name}

        FILTERED FINANCIAL TABLES (Pre-screened for relevance):
        {json.dumps([table['table_html'] for table in filtered_content['filtered_tables']], indent=2)}

        TABLE CONTEXTS (Headings and context before each table):
        {json.dumps(filtered_content['table_contexts'], indent=2)}

        DOCUMENT STRUCTURE (First 2000 characters for context):
        {filtered_content['document_structure']}

        Please extract and return ONLY a JSON object with this exact structure:
        {{
            "document_name": "string",
            "advisor_name": "string or null",
            "client_name": "string or null", 
            "portfolio_id": "string or null",
            "total_account_value": number or null,
            "date_of_analysis": "string or null",
            "tabular_data": [
                {{
                    "account_number": "string or null",
                    "ticker_symbol": "string (REQUIRED)",
                    "shares_quantity": number (REQUIRED),
                    "current_value": number or null,
                    "cost_basis": number or null
                }}
            ]
        }}

        IMPORTANT INSTRUCTIONS:
        - ticker_symbol and shares_quantity are REQUIRED fields
        - Focus ONLY on the pre-filtered tables above - these contain the most relevant financial data
        - Use table contexts to understand what each table represents
        - Look for variations like "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units"
        - Extract holdings/portfolio information from the HTML tables
        - The HTML table format preserves cell relationships - use this structure
        - Return valid JSON only, no explanations
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                messages=[
                    {"role": "system", "content": "You are a financial document analysis expert. You receive pre-filtered, structured data and extract portfolio information with high accuracy. Focus on the filtered tables provided."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )

            result_text = response.choices[0].message.content.strip()
            # Clean up the response to ensure it's valid JSON
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]
            elif result_text.startswith("```"):
                result_text = result_text[3:-3]

            return json.loads(result_text)
        except Exception as e:
            print(f"Error in GPT-4o markdown analysis: {e}")
            return None

    def analyze_with_gpt4o(self, extracted_data, document_name):
        """Use GPT-4o to analyze and structure the extracted data (original method)"""
        prompt = f"""
        Analyze the following financial document data and extract the required information in JSON format.

        Document: {document_name}

        Extracted Data:
        Tables: {json.dumps(extracted_data.get('tables', []), indent=2)}
        Text: {' '.join(extracted_data.get('paragraphs', [])[:10])}  # First 10 paragraphs
        Key-Value Pairs: {json.dumps(extracted_data.get('key_value_pairs', []), indent=2)}

        Please extract and return ONLY a JSON object with this exact structure:
        {{
            "document_name": "string",
            "advisor_name": "string or null",
            "client_name": "string or null",
            "portfolio_id": "string or null",
            "total_account_value": number or null,
            "date_of_analysis": "string or null",
            "tabular_data": [
                {{
                    "account_number": "string or null",
                    "ticker_symbol": "string (REQUIRED)",
                    "shares_quantity": number (REQUIRED),
                    "current_value": number or null,
                    "cost_basis": number or null
                }}
            ]
        }}

        Important:
        - ticker_symbol and shares_quantity are REQUIRED fields
        - Look for variations like "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units"
        - Focus on tabular data for holdings/portfolio information
        - Return valid JSON only, no explanations
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                messages=[
                    {"role": "system", "content": "You are a financial document analysis expert. Extract data accurately and return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )

            result_text = response.choices[0].message.content.strip()
            # Clean up the response to ensure it's valid JSON
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]
            elif result_text.startswith("```"):
                result_text = result_text[3:-3]

            return json.loads(result_text)
        except Exception as e:
            print(f"Error in GPT-4o analysis: {e}")
            return None

    def extract_markdown_content(self, result):
        """Extract structured markdown content with enhanced table filtering"""
        extracted_data = {
            "markdown_content": "",
            "filtered_tables": [],
            "structured_sections": [],
            "table_contexts": []
        }

        if hasattr(result, 'content') and result.content:
            extracted_data["markdown_content"] = result.content
            
            # Extract and filter tables from markdown
            filtered_tables = self._filter_financial_tables_from_markdown(result.content)
            extracted_data["filtered_tables"] = filtered_tables
            
            # Extract table contexts (headings before tables)
            table_contexts = self._extract_table_contexts(result.content)
            extracted_data["table_contexts"] = table_contexts

        return extracted_data

    def _filter_financial_tables_from_markdown(self, markdown_content):
        """Filter tables that likely contain financial data from markdown content"""
        import re
        
        # Find all HTML tables in markdown
        table_pattern = r'<table[^>]*>.*?</table>'
        tables = re.findall(table_pattern, markdown_content, re.DOTALL | re.IGNORECASE)
        
        financial_tables = []
        financial_keywords = [
            'ticker', 'symbol', 'shares', 'quantity', 'units', 'holdings',
            'portfolio', 'value', 'price', 'cost', 'basis', 'account',
            'balance', 'equity', 'bond', 'stock', 'fund', 'asset',
            'investment', 'mutual', 'etf', 'reit', 'dividend'
        ]
        
        for i, table in enumerate(tables):
            table_text = table.lower()
            
            # Count financial keywords in table
            keyword_count = sum(1 for keyword in financial_keywords if keyword in table_text)
            
            # Filter criteria
            is_financial = (
                keyword_count >= 2 or  # Has multiple financial keywords
                any(keyword in table_text for keyword in ['ticker', 'symbol', 'shares']) or  # Core financial indicators
                ('$' in table and keyword_count >= 1)  # Has dollar signs and at least one keyword
            )
            
            # Check table size (avoid tiny or huge tables)
            row_count = table_text.count('<tr>')
            col_count = table_text.count('<th>') + table_text.count('<td>')
            
            if is_financial and 2 <= row_count <= 100 and 2 <= col_count <= 50:
                financial_tables.append({
                    'table_index': i,
                    'table_html': table,
                    'keyword_count': keyword_count,
                    'estimated_rows': row_count,
                    'estimated_cols': col_count // row_count if row_count > 0 else 0
                })
        
        return financial_tables

    def _extract_table_contexts(self, markdown_content):
        """Extract headings and context that appear before tables"""
        import re
        
        # Split content into sections around tables
        table_pattern = r'(<table[^>]*>.*?</table>)'
        sections = re.split(table_pattern, markdown_content, flags=re.DOTALL | re.IGNORECASE)
        
        contexts = []
        for i in range(0, len(sections), 2):  # Even indices are content before tables
            if i + 1 < len(sections):  # There's a table after this section
                context_text = sections[i]
                
                # Extract headings from context
                heading_pattern = r'^(#{1,6})\s+(.+)$'
                headings = re.findall(heading_pattern, context_text, re.MULTILINE)
                
                # Get last few lines before table
                lines = context_text.strip().split('\n')
                recent_context = '\n'.join(lines[-3:]) if len(lines) >= 3 else context_text
                
                contexts.append({
                    'headings': [{'level': len(level), 'text': text.strip()} for level, text in headings],
                    'recent_context': recent_context.strip(),
                    'table_index': i // 2
                })
        
        return contexts

    def extract_cross_page_tables(self, result):
        """Extract and reconstruct tables that span across multiple pages"""
        cross_page_tables = []
        
        if not result.tables:
            return cross_page_tables
        
        # Group tables by their structural similarity and proximity
        potential_continuations = self._identify_table_continuations(result.tables)
        
        for group in potential_continuations:
            if len(group) > 1:  # Multi-page table detected
                merged_table = self._merge_cross_page_tables(group)
                cross_page_tables.append({
                    'type': 'cross_page',
                    'page_span': f"{group[0].bounding_regions[0].page_number}-{group[-1].bounding_regions[0].page_number}",
                    'total_rows': merged_table['total_rows'],
                    'total_columns': merged_table['total_columns'],
                    'merged_content': merged_table['content'],
                    'original_tables': len(group)
                })
            else:
                # Single page table
                table = group[0]
                table_content = self._extract_single_table_content(table)
                cross_page_tables.append({
                    'type': 'single_page',
                    'page_span': str(table.bounding_regions[0].page_number),
                    'total_rows': table.row_count,
                    'total_columns': table.column_count,
                    'merged_content': table_content,
                    'original_tables': 1
                })
        
        return cross_page_tables
    
    def _identify_table_continuations(self, tables):
        """Identify tables that are likely continuations of each other across pages"""
        import difflib
        
        # Sort tables by page number
        sorted_tables = sorted(tables, key=lambda t: t.bounding_regions[0].page_number)
        
        groups = []
        current_group = [sorted_tables[0]] if sorted_tables else []
        
        for i in range(1, len(sorted_tables)):
            current_table = sorted_tables[i]
            previous_table = sorted_tables[i-1]
            
            # Check if tables are on consecutive pages
            current_page = current_table.bounding_regions[0].page_number
            previous_page = previous_table.bounding_regions[0].page_number
            
            if current_page == previous_page + 1:
                # Check structural similarity
                similarity = self._calculate_table_similarity(previous_table, current_table)
                
                if similarity > 0.7:  # 70% structural similarity threshold
                    current_group.append(current_table)
                else:
                    groups.append(current_group)
                    current_group = [current_table]
            else:
                groups.append(current_group)
                current_group = [current_table]
        
        if current_group:
            groups.append(current_group)
        
        return groups
    
    def _calculate_table_similarity(self, table1, table2):
        """Calculate structural similarity between two tables"""
        # Compare column count
        col_similarity = 1.0 if table1.column_count == table2.column_count else 0.5
        
        # Compare header structure if available
        header_similarity = self._compare_table_headers(table1, table2)
        
        # Compare column widths/positions (approximate)
        position_similarity = self._compare_column_positions(table1, table2)
        
        # Weighted average
        return (col_similarity * 0.4 + header_similarity * 0.4 + position_similarity * 0.2)
    
    def _compare_table_headers(self, table1, table2):
        """Compare header cells between two tables"""
        table1_headers = [cell.content.lower().strip() for cell in table1.cells if getattr(cell, 'kind', None) == 'columnHeader']
        table2_headers = [cell.content.lower().strip() for cell in table2.cells if getattr(cell, 'kind', None) == 'columnHeader']
        
        if not table1_headers or not table2_headers:
            return 0.5  # Neutral score if no headers detected
        
        # Use sequence matcher to compare header similarity
        import difflib
        matcher = difflib.SequenceMatcher(None, table1_headers, table2_headers)
        return matcher.ratio()
    
    def _compare_column_positions(self, table1, table2):
        """Compare column positions between tables"""
        # Get column positions for first row of each table
        table1_positions = []
        table2_positions = []
        
        for cell in table1.cells:
            if cell.row_index == 0:
                table1_positions.append((cell.column_index, cell.bounding_regions[0].polygon[0]))
        
        for cell in table2.cells:
            if cell.row_index == 0:
                table2_positions.append((cell.column_index, cell.bounding_regions[0].polygon[0]))
        
        if len(table1_positions) != len(table2_positions):
            return 0.5
        
        # Compare relative positions
        position_diffs = []
        for i, ((_, pos1), (_, pos2)) in enumerate(zip(sorted(table1_positions), sorted(table2_positions))):
            if i > 0:  # Compare relative spacing
                diff = abs(pos1 - pos2)
                position_diffs.append(1.0 if diff < 50 else max(0, 1.0 - diff/100))
        
        return sum(position_diffs) / len(position_diffs) if position_diffs else 0.5
    
    def _merge_cross_page_tables(self, table_group):
        """Merge multiple tables that span across pages"""
        merged_content = []
        total_rows = 0
        total_columns = table_group[0].column_count
        
        # Extract headers from first table
        first_table = table_group[0]
        headers = []
        
        # Get header row
        for cell in first_table.cells:
            if cell.row_index == 0 or getattr(cell, 'kind', None) == 'columnHeader':
                headers.append({
                    'column_index': cell.column_index,
                    'content': cell.content,
                    'is_header': True
                })
        
        if headers:
            merged_content.append(headers)
            total_rows += 1
        
        # Process all tables in the group
        for table_index, table in enumerate(table_group):
            table_rows = {}
            
            # Group cells by row
            for cell in table.cells:
                row_idx = cell.row_index
                if row_idx not in table_rows:
                    table_rows[row_idx] = []
                
                # Skip headers for continuation tables
                if table_index > 0 and (row_idx == 0 or getattr(cell, 'kind', None) == 'columnHeader'):
                    continue
                
                table_rows[row_idx].append({
                    'column_index': cell.column_index,
                    'content': cell.content,
                    'is_header': False
                })
            
            # Add rows to merged content
            for row_idx in sorted(table_rows.keys()):
                if table_index == 0 and row_idx == 0:  # Skip header row for first table as it's already added
                    continue
                merged_content.append(table_rows[row_idx])
                total_rows += 1
        
        return {
            'content': merged_content,
            'total_rows': total_rows,
            'total_columns': total_columns
        }
    
    def _extract_single_table_content(self, table):
        """Extract content from a single table"""
        table_rows = {}
        
        # Group cells by row
        for cell in table.cells:
            row_idx = cell.row_index
            if row_idx not in table_rows:
                table_rows[row_idx] = []
            
            table_rows[row_idx].append({
                'column_index': cell.column_index,
                'content': cell.content,
                'is_header': getattr(cell, 'kind', None) == 'columnHeader'
            })
        
        # Convert to list format
        content = []
        for row_idx in sorted(table_rows.keys()):
            content.append(table_rows[row_idx])
        
        return content

    def extract_markdown_content_with_cross_page(self, result):
        """Enhanced markdown extraction with cross-page table detection"""
        extracted_data = {
            "markdown_content": "",
            "filtered_tables": [],
            "cross_page_tables": [],
            "structured_sections": [],
            "table_contexts": []
        }

        if hasattr(result, 'content') and result.content:
            extracted_data["markdown_content"] = result.content
            
            # Extract cross-page tables first
            cross_page_tables = self.extract_cross_page_tables(result)
            extracted_data["cross_page_tables"] = cross_page_tables
            
            # Convert cross-page tables to HTML for filtering
            cross_page_html_tables = self._convert_cross_page_to_html(cross_page_tables)
            
            # Extract regular markdown tables
            regular_tables = self._filter_financial_tables_from_markdown(result.content)
            
            # Combine and prioritize cross-page tables
            all_tables = cross_page_html_tables + regular_tables
            
            # Apply enhanced filtering with cross-page logic
            filtered_tables = self._apply_enhanced_filtering(all_tables)
            extracted_data["filtered_tables"] = filtered_tables
            
            # Extract table contexts
            table_contexts = self._extract_table_contexts(result.content)
            extracted_data["table_contexts"] = table_contexts

        return extracted_data

    def _convert_cross_page_to_html(self, cross_page_tables):
        """Convert cross-page table data to HTML format for consistent processing"""
        html_tables = []
        
        for i, table_info in enumerate(cross_page_tables):
            merged_content = table_info.get('merged_content', [])
            
            if not merged_content:
                continue
                
            # Build HTML table
            html_parts = ['<table>']
            
            for row_idx, row_cells in enumerate(merged_content):
                html_parts.append('<tr>')
                
                # Sort cells by column index
                sorted_cells = sorted(row_cells, key=lambda x: x.get('column_index', 0))
                
                for cell in sorted_cells:
                    tag = 'th' if cell.get('is_header', False) else 'td'
                    content = cell.get('content', '').strip()
                    html_parts.append(f'<{tag}>{content}</{tag}>')
                
                html_parts.append('</tr>')
            
            html_parts.append('</table>')
            html_table = ''.join(html_parts)
            
            # Calculate keywords and metrics
            table_text = html_table.lower()
            financial_keywords = [
                'ticker', 'symbol', 'shares', 'quantity', 'units', 'holdings',
                'portfolio', 'value', 'price', 'cost', 'basis', 'account',
                'balance', 'equity', 'bond', 'stock', 'fund', 'asset',
                'investment', 'mutual', 'etf', 'reit', 'dividend'
            ]
            
            keyword_count = sum(1 for keyword in financial_keywords if keyword in table_text)
            
            # Create enhanced table data with cross-page info
            from models import CrossPageTableInfo
            
            cross_page_info = CrossPageTableInfo(
                table_type=table_info.get('type', 'unknown'),
                page_span=table_info.get('page_span', '1'),
                total_rows=table_info.get('total_rows', 0),
                total_columns=table_info.get('total_columns', 0),
                original_tables=table_info.get('original_tables', 1)
            )
            
            html_tables.append({
                'table_index': f"cross_page_{i}",
                'table_html': html_table,
                'keyword_count': keyword_count,
                'estimated_rows': table_info.get('total_rows', 0),
                'estimated_cols': table_info.get('total_columns', 0),
                'is_cross_page': table_info.get('type') == 'cross_page',
                'cross_page_info': cross_page_info.dict(),
                'source': 'cross_page_detection'
            })
        
        return html_tables

    def _apply_enhanced_filtering(self, all_tables):
        """Apply enhanced Pydantic-based filtering with cross-page prioritization"""
        from models import EnhancedTableFilterCriteria, EnhancedFilteredTableData
        
        # Create filtering criteria
        criteria = EnhancedTableFilterCriteria()
        
        filtered_tables = []
        
        for table_data in all_tables:
            try:
                # Create enhanced filtered table data
                cross_page_info = table_data.get('cross_page_info')
                
                enhanced_table = EnhancedFilteredTableData(
                    table_index=table_data.get('table_index', 0),
                    table_html=table_data.get('table_html', ''),
                    keyword_count=table_data.get('keyword_count', 0),
                    estimated_rows=table_data.get('estimated_rows', 0),
                    estimated_cols=table_data.get('estimated_cols', 0),
                    is_cross_page=table_data.get('is_cross_page', False),
                    cross_page_info=cross_page_info
                )
                
                # Apply Pydantic filtering logic
                if enhanced_table.should_pass_to_llm(criteria):
                    filtered_tables.append({
                        **table_data,
                        'relevance_score': enhanced_table.relevance_score,
                        'pydantic_validated': True,
                        'filtering_reason': 'enhanced_pydantic_logic'
                    })
                    
            except Exception as e:
                print(f"Error in enhanced filtering for table {table_data.get('table_index', 'unknown')}: {e}")
                # Fallback to basic inclusion for problematic tables
                if table_data.get('keyword_count', 0) >= 2:
                    filtered_tables.append({
                        **table_data,
                        'relevance_score': 0.5,
                        'pydantic_validated': False,
                        'filtering_reason': 'fallback_basic'
                    })
        
        # Sort by relevance score (cross-page tables with high confidence will rank higher)
        filtered_tables.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        
        return filtered_tables

    def analyze_with_gpt4o_enhanced_markdown(self, markdown_data, document_name):
        """Enhanced GPT-4o analysis with cross-page table awareness"""
        
        # Prepare enhanced content for LLM
        filtered_content = {
            "filtered_tables": markdown_data.get('filtered_tables', []),
            "cross_page_tables": markdown_data.get('cross_page_tables', []),
            "table_contexts": markdown_data.get('table_contexts', []),
            "document_structure": markdown_data.get('markdown_content', '')[:2000]
        }
        
        # Separate cross-page and regular tables for focused processing
        cross_page_tables = [t for t in filtered_content['filtered_tables'] if t.get('is_cross_page', False)]
        regular_tables = [t for t in filtered_content['filtered_tables'] if not t.get('is_cross_page', False)]
        
        prompt = f"""
        Analyze the following financial document data with ENHANCED cross-page table detection and extract the required information in JSON format.

        Document: {document_name}

        🔄 CROSS-PAGE TABLES (High Priority - Likely contain complete portfolio data):
        {json.dumps([{'html': table['table_html'], 'info': table.get('cross_page_info', {}), 'score': table.get('relevance_score', 0)} for table in cross_page_tables], indent=2)}

        📊 REGULAR FILTERED TABLES (Pre-screened for relevance):
        {json.dumps([{'html': table['table_html'], 'keywords': table.get('keyword_count', 0), 'score': table.get('relevance_score', 0)} for table in regular_tables], indent=2)}

        📋 TABLE CONTEXTS (Headings and context):
        {json.dumps(filtered_content['table_contexts'], indent=2)}

        📄 DOCUMENT STRUCTURE (First 2000 characters for context):
        {filtered_content['document_structure']}

        Please extract and return ONLY a JSON object with this exact structure:
        {{
            "document_name": "string",
            "advisor_name": "string or null",
            "client_name": "string or null", 
            "portfolio_id": "string or null",
            "total_account_value": number or null,
            "date_of_analysis": "string or null",
            "tabular_data": [
                {{
                    "account_number": "string or null",
                    "ticker_symbol": "string (REQUIRED)",
                    "shares_quantity": number (REQUIRED),
                    "current_value": number or null,
                    "cost_basis": number or null
                }}
            ]
        }}

        ENHANCED PROCESSING INSTRUCTIONS:
        - PRIORITIZE CROSS-PAGE TABLES: These often contain the most complete portfolio data
        - Cross-page tables spanning multiple pages typically have comprehensive holdings information
        - Use the cross-page table info (page span, confidence) to understand data completeness
        - ticker_symbol and shares_quantity are REQUIRED fields
        - Look for variations like "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units"
        - Cross-page tables preserve complete relationships across page breaks
        - Regular tables supplement the cross-page data
        - Return valid JSON only, no explanations
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                messages=[
                    {"role": "system", "content": "You are a financial document analysis expert with advanced cross-page table reconstruction capabilities. Prioritize cross-page tables as they contain the most complete portfolio information."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=3000  # Increased for complex cross-page data
            )

            result_text = response.choices[0].message.content.strip()
            # Clean up the response to ensure it's valid JSON
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]
            elif result_text.startswith("```"):
                result_text = result_text[3:-3]

            return json.loads(result_text)
        except Exception as e:
            print(f"Error in enhanced GPT-4o markdown analysis: {e}")
            return None
