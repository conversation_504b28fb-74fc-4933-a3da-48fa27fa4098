import os
import json
import asyncio
import concurrent.futures
from typing import List, Dict, Any, Tuple
from dotenv import load_dotenv
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from openai import AzureOpenAI
from models import (
    EnhancedFilteredTableData, 
    EnhancedTableFilterCriteria, 
    CrossPageTableInfo,
    DocumentChunk
)
import re
from pathlib import Path

load_dotenv()

class EnhancedAzureDocumentService:
    def __init__(self):
        self.endpoint = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")
        self.key = os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY")
        self.client = DocumentIntelligenceClient(
            endpoint=self.endpoint,
            credential=AzureKeyCredential(self.key)
        )
        
        # OpenAI setup
        self.openai_client = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
        )
        
        # Document size threshold for chunking (2MB)
        self.chunk_threshold = 2 * 1024 * 1024
    
    def should_chunk_document(self, file_path: str) -> bool:
        """Determine if document should be chunked based on size"""
        return os.path.getsize(file_path) > self.chunk_threshold
    
    def create_document_chunks(self, file_path: str, chunk_size: int = 1024*1024) -> List[DocumentChunk]:
        """Create document chunks for large files"""
        chunks = []
        file_size = os.path.getsize(file_path)
        
        with open(file_path, 'rb') as f:
            chunk_id = 0
            while True:
                chunk_data = f.read(chunk_size)
                if not chunk_data:
                    break
                
                # Estimate page range (rough calculation)
                start_page = chunk_id * 10 + 1  # Assume ~10 pages per chunk
                end_page = (chunk_id + 1) * 10
                
                chunks.append(DocumentChunk(
                    chunk_id=chunk_id,
                    page_range=(start_page, end_page),
                    content=chunk_data,
                    file_path=f"{file_path}_chunk_{chunk_id}"
                ))
                chunk_id += 1
        
        return chunks
    
    def extract_with_markdown(self, file_path: str) -> Dict[str, Any]:
        """Extract document using markdown output format"""
        try:
            with open(file_path, "rb") as f:
                file_content = f.read()
            
            # Use markdown output format for better structure
            poller = self.client.begin_analyze_document(
                model_id="prebuilt-layout",
                body=file_content,
                content_type="application/octet-stream",
                output_content_format="markdown"  # Key enhancement!
            )
            result = poller.result()
            return {
                "success": True,
                "result": result,
                "markdown_content": result.content if hasattr(result, 'content') else None
            }
        except Exception as e:
            print(f"Error in markdown extraction: {e}")
            return {"success": False, "error": str(e)}
    
    def detect_cross_page_tables(self, result) -> List[CrossPageTableInfo]:
        """Detect tables that span across multiple pages"""
        cross_page_tables = []
        
        if not result.tables:
            return cross_page_tables
        
        # Group tables by potential cross-page relationships
        table_groups = []
        current_group = []
        
        for i, table in enumerate(result.tables):
            table_pages = self.get_table_page_numbers(table)
            
            if not current_group:
                current_group = [(i, table, table_pages)]
            else:
                # Check if this table could be continuation of previous
                prev_table_pages = current_group[-1][2]
                
                # If table starts on next page and has similar column structure
                if (min(table_pages) == max(prev_table_pages) + 1 and
                    self.tables_have_similar_structure(current_group[-1][1], table)):
                    current_group.append((i, table, table_pages))
                else:
                    # Process current group if it has multiple tables
                    if len(current_group) > 1:
                        cross_page_info = self.create_cross_page_info(current_group)
                        cross_page_tables.append(cross_page_info)
                    current_group = [(i, table, table_pages)]
        
        # Process final group
        if len(current_group) > 1:
            cross_page_info = self.create_cross_page_info(current_group)
            cross_page_tables.append(cross_page_info)
        
        return cross_page_tables
    
    def get_table_page_numbers(self, table) -> List[int]:
        """Get page numbers where table appears"""
        if hasattr(table, 'bounding_regions'):
            return [region.page_number for region in table.bounding_regions]
        return [1]  # Default to page 1 if no bounding regions
    
    def tables_have_similar_structure(self, table1, table2) -> bool:
        """Check if two tables have similar column structure"""
        return abs(table1.column_count - table2.column_count) <= 1
    
    def create_cross_page_info(self, table_group) -> CrossPageTableInfo:
        """Create cross-page table info from grouped tables"""
        indices = [item[0] for item in table_group]
        pages = []
        total_rows = 0
        total_cols = table_group[0][1].column_count
        
        for _, table, table_pages in table_group:
            pages.extend(table_pages)
            total_rows += table.row_count
        
        page_span = f"{min(pages)}-{max(pages)}" if len(set(pages)) > 1 else str(pages[0])
        
        return CrossPageTableInfo(
            table_type="cross_page",
            page_span=page_span,
            total_rows=total_rows,
            total_columns=total_cols,
            original_tables=len(table_group)
        )
    
    def filter_relevant_tables(self, result, criteria: EnhancedTableFilterCriteria) -> List[EnhancedFilteredTableData]:
        """Filter tables using Pydantic validation logic"""
        if not result.tables:
            return []
        
        # Detect cross-page tables first
        cross_page_tables = self.detect_cross_page_tables(result) if criteria.enable_cross_page_detection else []
        cross_page_indices = set()
        for cp_table in cross_page_tables:
            # Mark tables that are part of cross-page groups
            pass  # Implementation would mark specific indices
        
        filtered_tables = []
        financial_keywords = [
            'ticker', 'symbol', 'stock', 'shares', 'quantity', 'units',
            'value', 'market value', 'current value', 'cost', 'cost basis',
            'account', 'portfolio', 'holdings', 'position', 'investment',
            'balance', 'amount', 'price', 'total', 'asset', 'security',
            'description', 'name', 'fund', 'etf', 'mutual fund'
        ]
        
        for i, table in enumerate(result.tables):
            # Convert table to HTML-like string for analysis
            table_content = self.table_to_string(table)
            
            # Count financial keywords
            keyword_count = sum(1 for keyword in financial_keywords 
                              if keyword.lower() in table_content.lower())
            
            # Check if this is part of a cross-page table
            is_cross_page = i in cross_page_indices
            cross_page_info = None
            if is_cross_page:
                # Find the corresponding cross-page info
                for cp_info in cross_page_tables:
                    cross_page_info = cp_info
                    break
            
            # Extract financial context
            financial_context = [kw for kw in financial_keywords 
                               if kw.lower() in table_content.lower()]
            
            filtered_table = EnhancedFilteredTableData(
                table_index=i,
                table_html=table_content,
                keyword_count=keyword_count,
                estimated_rows=table.row_count,
                estimated_cols=table.column_count,
                is_cross_page=is_cross_page,
                cross_page_info=cross_page_info,
                financial_context=financial_context
            )
            
            # Apply filtering logic
            if filtered_table.should_pass_to_llm(criteria):
                filtered_tables.append(filtered_table)
        
        return filtered_tables
    
    def table_to_string(self, table) -> str:
        """Convert table to string representation"""
        content = []
        if hasattr(table, 'cells'):
            for cell in table.cells:
                content.append(cell.content)
        return ' '.join(content)
    
    async def process_chunks_parallel(self, chunks: List[DocumentChunk]) -> List[Dict[str, Any]]:
        """Process document chunks in parallel"""
        async def process_single_chunk(chunk: DocumentChunk):
            # Save chunk to temporary file
            temp_path = f"temp_chunk_{chunk.chunk_id}.tmp"
            with open(temp_path, 'wb') as f:
                f.write(chunk.content)
            
            try:
                result = self.extract_with_markdown(temp_path)
                return {
                    "chunk_id": chunk.chunk_id,
                    "page_range": chunk.page_range,
                    "result": result
                }
            finally:
                # Clean up temp file
                if os.path.exists(temp_path):
                    os.remove(temp_path)
        
        # Process chunks concurrently
        tasks = [process_single_chunk(chunk) for chunk in chunks]
        results = await asyncio.gather(*tasks)
        return results

    def analyze_filtered_tables_with_gpt4o(self, filtered_tables: List[EnhancedFilteredTableData],
                                         document_name: str, markdown_content: str = None) -> Dict[str, Any]:
        """Analyze only relevant tables and key-value pairs with GPT-4o"""

        # Prepare focused data for LLM - only relevant tables
        relevant_tables_data = []
        for table in filtered_tables:
            relevant_tables_data.append({
                "table_index": table.table_index,
                "content": table.table_html,
                "relevance_score": table.relevance_score,
                "financial_keywords": table.financial_context,
                "is_cross_page": table.is_cross_page,
                "cross_page_info": table.cross_page_info.model_dump() if table.cross_page_info else None
            })

        # Include markdown content if available (better structure)
        content_summary = ""
        if markdown_content:
            # Extract key sections from markdown
            content_summary = self.extract_key_sections_from_markdown(markdown_content)

        prompt = f"""
        Analyze the following FILTERED financial document data. Only relevant tables and key information are provided.

        Document: {document_name}
        Total Relevant Tables Found: {len(filtered_tables)}

        RELEVANT TABLES DATA:
        {json.dumps(relevant_tables_data, indent=2)}

        DOCUMENT STRUCTURE (Markdown):
        {content_summary[:2000] if content_summary else "Not available"}

        Extract and return ONLY a JSON object with this exact structure:
        {{
            "document_name": "string",
            "advisor_name": "string or null",
            "client_name": "string or null",
            "portfolio_id": "string or null",
            "total_account_value": number or null,
            "date_of_analysis": "string or null",
            "tabular_data": [
                {{
                    "account_number": "string or null",
                    "ticker_symbol": "string (REQUIRED)",
                    "shares_quantity": number (REQUIRED)",
                    "current_value": number or null,
                    "cost_basis": number or null
                }}
            ]
        }}

        IMPORTANT INSTRUCTIONS:
        - Focus ONLY on the provided relevant tables - ignore irrelevant data
        - ticker_symbol and shares_quantity are REQUIRED fields
        - Look for variations: "Symbol", "Ticker", "Stock Symbol", "Shares", "Quantity", "Units", "Holdings"
        - Cross-page tables (is_cross_page: true) often contain the most important portfolio data
        - Extract ALL portfolio holdings from relevant tables
        - Use markdown structure to identify document sections and context
        - Return valid JSON only, no explanations
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
                messages=[
                    {"role": "system", "content": "You are a financial document analysis expert. Focus on relevant tables only and extract data accurately. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=3000
            )

            result_text = response.choices[0].message.content.strip()
            # Clean up the response to ensure it's valid JSON
            if result_text.startswith("```json"):
                result_text = result_text[7:-3]
            elif result_text.startswith("```"):
                result_text = result_text[3:-3]

            return json.loads(result_text)
        except Exception as e:
            print(f"Error in GPT-4o analysis: {e}")
            return None

    def extract_key_sections_from_markdown(self, markdown_content: str) -> str:
        """Extract key sections from markdown content"""
        if not markdown_content:
            return ""

        # Look for key financial sections
        key_patterns = [
            r'#+.*(?:portfolio|holdings|account|investment|position).*',
            r'#+.*(?:summary|total|balance).*',
            r'\|.*(?:ticker|symbol|shares|quantity|value).*\|',  # Table headers
        ]

        key_sections = []
        lines = markdown_content.split('\n')

        for line in lines:
            for pattern in key_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    key_sections.append(line)
                    break

        return '\n'.join(key_sections[:50])  # Limit to first 50 relevant lines
