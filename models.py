from pydantic import BaseModel, validator, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class TabularData(BaseModel):
    account_number: Optional[str] = None
    ticker_symbol: str  # Required field
    shares_quantity: float  # Required field
    current_value: Optional[float] = None
    cost_basis: Optional[float] = None

class ExtractionResult(BaseModel):
    document_name: Optional[str] = None
    advisor_name: Optional[str] = None
    client_name: Optional[str] = None
    portfolio_id: Optional[str] = None
    total_account_value: Optional[float] = None
    date_of_analysis: Optional[str] = None
    tabular_data: List[TabularData] = []
    
    def validate_required_fields(self) -> bool:
        """Check if required fields are present in tabular data"""
        if not self.tabular_data:
            return False
        
        for row in self.tabular_data:
            if not row.ticker_symbol or row.shares_quantity is None:
                return False
        return True

class TableFilterCriteria(BaseModel):
    """Pydantic model for intelligent table filtering logic"""
    min_financial_keywords: int = Field(default=2, description="Minimum financial keywords required")
    required_keywords: List[str] = Field(default=['ticker', 'symbol', 'shares'], description="Keywords that guarantee relevance")
    min_rows: int = Field(default=2, description="Minimum table rows")
    max_rows: int = Field(default=100, description="Maximum table rows")
    min_cols: int = Field(default=2, description="Minimum table columns")
    max_cols: int = Field(default=20, description="Maximum table columns")
    
    @validator('min_financial_keywords')
    def validate_min_keywords(cls, v):
        if v < 1:
            raise ValueError('Minimum financial keywords must be at least 1')
        return v

class FilteredTableData(BaseModel):
    """Pydantic model for filtered table with validation logic"""
    table_index: int
    table_html: str
    keyword_count: int
    estimated_rows: int
    estimated_cols: int
    relevance_score: float = 0.0
    contains_required_keywords: bool = False
    
    @validator('relevance_score', always=True)
    def calculate_relevance_score(cls, v, values):
        """Custom Pydantic logic to calculate table relevance"""
        keyword_count = values.get('keyword_count', 0)
        estimated_rows = values.get('estimated_rows', 0)
        estimated_cols = values.get('estimated_cols', 0)
        
        # Base score from keyword density
        score = keyword_count * 0.3
        
        # Bonus for optimal table size (financial tables usually 5-50 rows)
        if 5 <= estimated_rows <= 50:
            score += 0.4
        elif 2 <= estimated_rows <= 100:
            score += 0.2
            
        # Bonus for reasonable column count (2-10 typical for financial data)
        if 3 <= estimated_cols <= 10:
            score += 0.3
        elif 2 <= estimated_cols <= 15:
            score += 0.1
            
        return min(score, 1.0)  # Cap at 1.0
    
    @validator('contains_required_keywords', always=True)
    def check_required_keywords(cls, v, values):
        """Check if table contains critical financial keywords"""
        table_html = values.get('table_html', '').lower()
        required_keywords = ['ticker', 'symbol', 'shares', 'quantity', 'holdings']
        return any(keyword in table_html for keyword in required_keywords)
    
    def should_pass_to_llm(self, criteria: TableFilterCriteria) -> bool:
        """Determine if this table should be passed to LLM based on criteria"""
        return (
            self.relevance_score >= 0.5 or
            self.contains_required_keywords or
            self.keyword_count >= criteria.min_financial_keywords
        )

class CrossPageTableInfo(BaseModel):
    """Pydantic model for cross-page table reconstruction"""
    table_type: str = Field(description="'cross_page' or 'single_page'")
    page_span: str = Field(description="Page range like '1-3' or '2'")
    total_rows: int = Field(description="Total rows in merged table")
    total_columns: int = Field(description="Total columns in table")
    original_tables: int = Field(description="Number of source tables merged")
    confidence_score: float = Field(default=0.0, description="Confidence in cross-page detection")
    
    @validator('confidence_score', always=True)
    def calculate_confidence(cls, v, values):
        """Calculate confidence based on table characteristics"""
        total_rows = values.get('total_rows', 0)
        total_columns = values.get('total_columns', 0)
        original_tables = values.get('original_tables', 1)
        
        # Higher confidence for larger, multi-page tables
        score = 0.5  # Base score
        
        if original_tables > 1:  # Multi-page table
            score += 0.3
        
        if 10 <= total_rows <= 200:  # Reasonable size for financial tables
            score += 0.2
        
        if 3 <= total_columns <= 15:  # Typical financial table columns
            score += 0.2
        
        return min(score, 1.0)
    
    def is_high_confidence(self) -> bool:
        """Check if this is a high-confidence cross-page table"""
        return self.confidence_score >= 0.8

class EnhancedTableFilterCriteria(TableFilterCriteria):
    """Enhanced filtering criteria with cross-page table support"""
    enable_cross_page_detection: bool = Field(default=True, description="Enable cross-page table detection")
    cross_page_similarity_threshold: float = Field(default=0.7, description="Similarity threshold for cross-page detection")
    prefer_cross_page_tables: bool = Field(default=True, description="Prioritize cross-page tables in filtering")
    max_cross_page_span: int = Field(default=5, description="Maximum pages a table can span")
    
    @validator('cross_page_similarity_threshold')
    def validate_similarity_threshold(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Similarity threshold must be between 0.0 and 1.0')
        return v

class EnhancedFilteredTableData(FilteredTableData):
    """Enhanced filtered table data with cross-page support"""
    cross_page_info: Optional[CrossPageTableInfo] = None
    is_cross_page: bool = Field(default=False, description="Whether this is a cross-page table")
    financial_context: List[str] = Field(default=[], description="Financial context keywords found")
    
    @validator('relevance_score', always=True)
    def enhanced_relevance_calculation(cls, v, values):
        """Enhanced relevance calculation including cross-page factors"""
        # Start with base calculation
        keyword_count = values.get('keyword_count', 0)
        estimated_rows = values.get('estimated_rows', 0)
        estimated_cols = values.get('estimated_cols', 0)
        is_cross_page = values.get('is_cross_page', False)
        cross_page_info = values.get('cross_page_info')
        
        # Base score from keyword density
        score = keyword_count * 0.3
        
        # Bonus for optimal table size
        if 5 <= estimated_rows <= 50:
            score += 0.4
        elif 2 <= estimated_rows <= 100:
            score += 0.2
            
        # Bonus for reasonable column count
        if 3 <= estimated_cols <= 10:
            score += 0.3
        elif 2 <= estimated_cols <= 15:
            score += 0.1
        
        # Cross-page table bonus (these are often more important)
        if is_cross_page and cross_page_info:
            if cross_page_info.is_high_confidence():
                score += 0.4  # High bonus for confident cross-page tables
            else:
                score += 0.2  # Moderate bonus for likely cross-page tables
                
            # Additional bonus for large cross-page tables (often portfolio summaries)
            if cross_page_info.total_rows > 20:
                score += 0.2
                
        return min(score, 1.0)  # Cap at 1.0
    
    def should_pass_to_llm(self, criteria: EnhancedTableFilterCriteria) -> bool:
        """Enhanced decision logic including cross-page considerations"""
        base_decision = (
            self.relevance_score >= 0.5 or
            self.contains_required_keywords or
            self.keyword_count >= criteria.min_financial_keywords
        )
        
        # Always include high-confidence cross-page tables if enabled
        if criteria.prefer_cross_page_tables and self.is_cross_page and self.cross_page_info:
            if self.cross_page_info.is_high_confidence():
                return True
                
        return base_decision
