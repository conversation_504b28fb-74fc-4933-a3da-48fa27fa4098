import streamlit as st
import os
import json
import pandas as pd
from PIL import Image
from dotenv import load_dotenv
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from openai import AzureOpenAI

load_dotenv()

st.set_page_config(page_title="Financial Document Extraction", layout="wide")
st.title("📊 Financial Document Extraction")

# Initialize clients
doc_client = DocumentIntelligenceClient(
    endpoint=os.getenv("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT"),
    credential=AzureKeyCredential(os.getenv("AZURE_DOCUMENT_INTELLIGENCE_KEY"))
)

openai_client = AzureOpenAI(
    api_key=os.getenv("AZURE_OPENAI_API_KEY"),
    api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
    azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
)

# File selection
st.sidebar.header("📁 Select Document")
sample_dir = "Financial Statements - Sample Files (2)"
sample_files = [f for f in os.listdir(sample_dir) if os.path.isfile(os.path.join(sample_dir, f))] if os.path.exists(sample_dir) else []

selected_file = st.sidebar.selectbox("Choose a sample file:", sample_files) if sample_files else None
file_path = os.path.join(sample_dir, selected_file) if selected_file else None

# Main processing
if file_path and st.button("🚀 Extract Data"):
    with st.spinner("Processing..."):
        # Extract document
        with open(file_path, "rb") as f:
            poller = doc_client.begin_analyze_document("prebuilt-layout", body=f.read())
        azure_result = poller.result()

        # Process tables
        tables = []
        for table in azure_result.tables or []:
            table_data = []
            for cell in table.cells:
                table_data.append({"content": cell.content, "row": cell.row_index, "column": cell.column_index})
            tables.append(table_data)

        # Get text
        text = " ".join([p.content for p in azure_result.paragraphs or []][:10])

        # AI analysis
        prompt = f"""Extract financial data from: {selected_file}
        Tables: {json.dumps(tables[:2])}
        Text: {text}

        Return JSON:
        {{
            "document_name": "{selected_file}",
            "advisor_name": null,
            "client_name": null,
            "portfolio_id": null,
            "total_account_value": null,
            "date_of_analysis": null,
            "tabular_data": [
                {{
                    "account_number": null,
                    "ticker_symbol": "REQUIRED",
                    "shares_quantity": "REQUIRED",
                    "current_value": null,
                    "cost_basis": null
                }}
            ]
        }}"""

        response = openai_client.chat.completions.create(
            model=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )

        result_text = response.choices[0].message.content.strip()
        if "```json" in result_text:
            result_text = result_text.split("```json")[1].split("```")[0]

        try:
            final_result = json.loads(result_text)
            st.session_state.result = final_result
            st.session_state.tables = tables
            st.session_state.text = text
        except:
            st.error("Failed to parse AI response")

# Display results
if hasattr(st.session_state, 'result'):
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 Original Document")
        if file_path and file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
            st.image(Image.open(file_path), use_container_width=True)
        else:
            st.info("Document selected")

    with col2:
        st.subheader("🔍 Azure DI Extraction")
        if hasattr(st.session_state, 'tables') and st.session_state.tables:
            st.write("**Tables:**")
            for i, table in enumerate(st.session_state.tables[:1]):
                df_data = {}
                for cell in table:
                    col = f"Col_{cell['column']}"
                    if col not in df_data:
                        df_data[col] = {}
                    df_data[col][cell['row']] = cell['content']

                if df_data:
                    max_rows = max([max(col.keys()) for col in df_data.values()]) + 1
                    df = pd.DataFrame(index=range(max_rows))
                    for col, rows in df_data.items():
                        df[col] = [rows.get(i, '') for i in range(max_rows)]
                    st.dataframe(df, use_container_width=True)

        if hasattr(st.session_state, 'text'):
            st.write("**Text:**")
            st.text_area("", st.session_state.text[:500], height=150)

    with col3:
        st.subheader("💎 Filtered Data")
        result = st.session_state.result

        st.write("**Document Info:**")
        for key, value in result.items():
            if key != 'tabular_data':
                st.write(f"**{key}:** {value}")

        if result.get('tabular_data'):
            st.write("**Holdings:**")
            st.dataframe(pd.DataFrame(result['tabular_data']), use_container_width=True)

    st.header("📊 Results")
    st.json(st.session_state.result)

    # Download
    json_str = json.dumps(st.session_state.result, indent=2)
    st.download_button("💾 Download JSON", json_str, f"{selected_file}_result.json", "application/json")

else:
    st.info("Select a file and click Extract Data to begin")


