import streamlit as st
import os
import json
from pathlib import Path
from agents import FinancialExtractionCrew
from PIL import Image
import pandas as pd

# Page config
st.set_page_config(
    page_title="Financial Document Extraction",
    layout="wide"
)

st.title("Financial Document Extraction System")
st.markdown("Multi-agent system for extracting financial data from documents")

# Initialize the extraction crew
@st.cache_resource
def get_extraction_crew():
    return FinancialExtractionCrew()

crew = get_extraction_crew()

# Sidebar for file selection
st.sidebar.header("Document Selection")

# Get sample files
sample_files_dir = "Financial Statements - Sample Files (2)"
sample_files = []

if os.path.exists(sample_files_dir):
    for file in os.listdir(sample_files_dir):
        file_path = os.path.join(sample_files_dir, file)
        if os.path.isfile(file_path):
            sample_files.append(file)

# File selection options
file_source = st.sidebar.radio(
    "Choose file source:",
    ["Sample Files", "Upload New File"]
)

selected_file_path = None
selected_file_name = None

if file_source == "Sample Files":
    if sample_files:
        selected_file = st.sidebar.selectbox(
            "Select a sample file:",
            sample_files
        )
        if selected_file:
            selected_file_path = os.path.join(sample_files_dir, selected_file)
            selected_file_name = selected_file
    else:
        st.sidebar.error("No sample files found in the directory")

else:
    uploaded_file = st.sidebar.file_uploader(
        "Upload a financial document",
        type=['pdf', 'png', 'jpg', 'jpeg', 'docx', 'odt']
    )
    if uploaded_file:
        # Save uploaded file temporarily
        temp_dir = "temp"
        os.makedirs(temp_dir, exist_ok=True)
        selected_file_path = os.path.join(temp_dir, uploaded_file.name)
        with open(selected_file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        selected_file_name = uploaded_file.name

# Main content area
if selected_file_path and os.path.exists(selected_file_path):
    st.header(f"Processing: {selected_file_name}")

    # Process button with enhanced processing options
    st.subheader("Processing Options")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        use_markdown = st.checkbox(
            "Use Enhanced Markdown Processing", 
            value=True, 
            help="Uses Azure Document Intelligence markdown output with intelligent table filtering for better accuracy and lower token usage"
        )
    
    with col2:
        enable_cross_page = st.checkbox(
            "Enable Cross-Page Table Detection",
            value=True,
            help="Detect and reconstruct tables that span across multiple pages (ideal for large portfolio documents)"
        )
    
    with col3:
        use_chunking = st.selectbox(
            " Document Chunking",
            ["Auto", "Force Enable", "Disable"],
            index=0,
            help="Auto: Use chunking for large/complex documents. Force Enable: Always use chunking. Disable: Never use chunking."
        )
    
    # Advanced options in expander
    with st.expander("Advanced Processing Options"):
        col1, col2 = st.columns(2)
        
        with col1:
            max_parallel_workers = st.slider(
                "Max Parallel Workers",
                min_value=1,
                max_value=8,
                value=4,
                help="Maximum number of parallel processing workers for chunked documents"
            )
            
            chunk_overlap = st.slider(
                "Chunk Overlap (pages)",
                min_value=0,
                max_value=3,
                value=1,
                help="Number of overlapping pages between chunks to ensure continuity"
            )
        
        with col2:
            max_pages_per_chunk = st.slider(
                "Max Pages per Chunk",
                min_value=5,
                max_value=20,
                value=10,
                help="Maximum number of pages in each chunk for parallel processing"
            )
            
            prioritize_early_pages = st.checkbox(
                "Prioritize Early Pages",
                value=True,
                help="Process earlier pages first as they often contain summary information"
            )
    
    st.info("**Recommended**: Enable all main options for maximum accuracy. Chunking will automatically activate for large documents (>5MB or >15 pages).")
    
    if st.button("Extract Financial Data", type="primary"):
        # Prepare processing options
        chunking_option = None
        if use_chunking == "Force Enable":
            chunking_option = True
        elif use_chunking == "Disable":
            chunking_option = False
        # If "Auto", chunking_option remains None and system will auto-decide
        
        with st.spinner("Processing document through multi-agent system..."):
            # Show file analysis first
            from chunking_service import DocumentChunkingService
            chunking_service = DocumentChunkingService()
            analysis = chunking_service.analyze_document_for_chunking(selected_file_path)
            
            st.info(f" **File Analysis**: {analysis.get('file_size_mb', 0):.2f} MB, ~{analysis.get('estimated_pages', 0)} pages, complexity: {analysis.get('complexity_score', 0):.2f}")
            
            if analysis.get('needs_chunking', False) or chunking_option:
                st.info("**Large document detected** - Using parallel processing with chunking for optimal performance")
            
            result = crew.process_document(
                selected_file_path, 
                selected_file_name, 
                use_markdown=use_markdown,
                enable_cross_page=enable_cross_page,
                use_chunking=chunking_option
            )

        # Store result in session state
        st.session_state.extraction_result = result
        st.session_state.file_path = selected_file_path
        st.session_state.file_name = selected_file_name

    # Display results if available
    if hasattr(st.session_state, 'extraction_result'):
        result = st.session_state.extraction_result

        # Create three columns for previews
        col1, col2, col3 = st.columns(3)

        with col1:
            st.subheader("Original Document")
            try:
                if selected_file_path.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image = Image.open(selected_file_path)
                    st.image(image, use_container_width=True)
                elif selected_file_path.lower().endswith('.pdf'):
                    st.info("PDF preview - showing first page")
                    # You could add PDF preview here using pdf2image if needed
                    st.text("PDF file selected")
                else:
                    st.info(f"Document type: {Path(selected_file_path).suffix}")
            except Exception as e:
                st.error(f"Could not preview file: {e}")

        with col2:
            st.subheader("Azure DI Extraction")
            
            # Show processing method used with enhanced info
            processing_method = result.get('processing_method', 'unknown')
            cross_page_enabled = result.get('cross_page_enabled', False)
            cross_page_tables_found = result.get('cross_page_tables_found', 0)
            chunking_enabled = result.get('chunking_enabled', False)
            chunking_stats = result.get('chunking_stats', {})
            
            # Processing method badge
            if processing_method.startswith('chunked_'):
                if 'enhanced_markdown' in processing_method:
                    st.success("Chunked + Enhanced Markdown + Cross-Page Processing")
                elif 'markdown' in processing_method:
                    st.success("Chunked + Enhanced Markdown Processing")
                else:
                    st.info("Chunked + Standard Processing")
                
                # Show chunking statistics
                if chunking_stats:
                    col1_stats, col2_stats = st.columns(2)
                    with col1_stats:
                        st.metric("Total Chunks", chunking_stats.get('total_chunks', 0))
                        st.metric("Success Rate", f"{(chunking_stats.get('successful_chunks', 0) / max(chunking_stats.get('total_chunks', 1), 1) * 100):.1f}%")
                    with col2_stats:
                        st.metric("Processing Time", f"{chunking_stats.get('total_processing_time', 0):.1f}s")
                        if chunking_stats.get('parallel_efficiency', 0) > 0:
                            st.metric("Parallel Efficiency", f"{chunking_stats.get('parallel_efficiency', 0):.1f}x")
                
                # Show chunk summaries
                if chunking_stats.get('chunk_summaries'):
                    st.write("**Chunk Processing Summary:**")
                    chunk_data = []
                    for chunk in chunking_stats['chunk_summaries']:
                        chunk_data.append({
                            'Chunk': chunk['chunk_id'],
                            'Pages': chunk['page_range'],
                            'Priority': chunk['priority'],
                            'Tables': chunk['tables_found'],
                            'Time (s)': f"{chunk['processing_time']:.1f}"
                        })
                    
                    chunk_df = pd.DataFrame(chunk_data)
                    st.dataframe(chunk_df, use_container_width=True)
                    
            else:
                if processing_method == 'enhanced_markdown':
                    st.success(" Enhanced Markdown + Cross-Page Processing Used")
                elif processing_method == 'markdown':
                    st.success("Enhanced Markdown Processing Used")
                else:
                    st.info("Standard Processing Used")
            
            # Cross-page table information
            if cross_page_enabled and cross_page_tables_found > 0:
                st.info(f"Detected {cross_page_tables_found} cross-page table(s)")
            
            if 'raw_extracted_data' in result:
                raw_data = result['raw_extracted_data']

                # Show cross-page tables if detected
                if cross_page_enabled and raw_data.get('cross_page_tables'):
                    st.write("**🔄 Cross-Page Table Analysis:**")
                    for i, table_info in enumerate(raw_data['cross_page_tables'][:2]):
                        table_type = table_info.get('type', 'unknown')
                        page_span = table_info.get('page_span', 'unknown')
                        total_rows = table_info.get('total_rows', 0)
                        
                        if table_type == 'cross_page':
                            st.success(f"**Multi-Page Table {i+1}** - Pages: {page_span}, Rows: {total_rows}")
                        else:
                            st.info(f"**Single-Page Table {i+1}** - Page: {page_span}, Rows: {total_rows}")

                # Show filtered tables with enhanced info
                if processing_method.endswith('markdown') and raw_data.get('filtered_tables'):
                    st.write("**Filtered Financial Tables:**")
                    
                    # Separate cross-page and regular tables for display
                    cross_page_filtered = [t for t in raw_data['filtered_tables'] if t.get('is_cross_page', False)]
                    regular_filtered = [t for t in raw_data['filtered_tables'] if not t.get('is_cross_page', False)]
                    
                    # Show cross-page tables first (higher priority)
                    if cross_page_filtered:
                        st.write("** Cross-Page Tables (High Priority):**")
                        for i, table_info in enumerate(cross_page_filtered[:2]):
                            relevance_score = table_info.get('relevance_score', 0)
                            cross_page_info = table_info.get('cross_page_info', {})
                            source_chunk = table_info.get('source_chunk', 'N/A')
                            st.write(f"**Cross-Page Table {i+1}** (Score: {relevance_score:.2f}, Pages: {cross_page_info.get('page_span', 'N/A')}, Chunk: {source_chunk})")
                            st.markdown(table_info.get('table_html', ''), unsafe_allow_html=True)
                    
                    # Show regular tables
                    if regular_filtered:
                        st.write("**Regular Filtered Tables:**")
                        for i, table_info in enumerate(regular_filtered[:2]):
                            relevance_score = table_info.get('relevance_score', 0)
                            keyword_count = table_info.get('keyword_count', 0)
                            source_chunk = table_info.get('source_chunk', 'N/A')
                            st.write(f"**Table {i+1}** (Score: {relevance_score:.2f}, Keywords: {keyword_count}, Chunk: {source_chunk})")
                            st.markdown(table_info.get('table_html', ''), unsafe_allow_html=True)
                        
                    # Show table contexts
                    if raw_data.get('table_contexts'):
                        st.write("**Table Contexts:**")
                        for i, context in enumerate(raw_data['table_contexts'][:3]):
                            if context.get('headings'):
                                st.write(f"Context {i+1}: {', '.join([h['text'] for h in context['headings']])}")
                            elif context.get('chunk_id'):
                                st.write(f"Chunk Context: {context.get('chunk_id', 'Unknown')}")
                
                # Show combined chunk data if available
                elif chunking_enabled and result.get('combined_chunk_data'):
                    combined_data = result['combined_chunk_data']
                    st.write("**🔗 Combined Chunk Data:**")
                    
                    if combined_data.get('combined_tables'):
                        st.write(f"Found {len(combined_data['combined_tables'])} tables across all chunks")
                        for i, table_info in enumerate(combined_data['combined_tables'][:2]):
                            source_chunk = table_info.get('source_chunk', 'Unknown')
                            source_pages = table_info.get('source_pages', 'Unknown')
                            st.write(f"**Table {i+1}** from {source_chunk} (Pages: {source_pages})")
                            
                            if 'table_html' in table_info:
                                st.markdown(table_info['table_html'], unsafe_allow_html=True)
                    
                    # Show processing metadata
                    if combined_data.get('processing_metadata'):
                        metadata = combined_data['processing_metadata']
                        st.write("**Processing Coverage:**")
                        coverage_data = []
                        for page_range, info in metadata.get('page_coverage', {}).items():
                            coverage_data.append({
                                'Pages': page_range,
                                'Chunk': info.get('chunk_id', 'Unknown'),
                                'Priority': info.get('priority', 'Unknown'),
                                'Time (s)': f"{info.get('processing_time', 0):.1f}"
                            })
                        
                        if coverage_data:
                            coverage_df = pd.DataFrame(coverage_data)
                            st.dataframe(coverage_df, use_container_width=True)
                
                # Show standard tables if standard processing was used
                elif raw_data.get('tables'):
                    st.write("**Tables found:**")
                    for i, table in enumerate(raw_data['tables'][:2]):  # Show first 2 tables
                        st.write(f"Table {i+1}:")
                        # Convert table data to DataFrame for better display
                        if table:
                            df_data = {}
                            for cell in table:
                                col_key = f"Col_{cell['column']}"
                                if col_key not in df_data:
                                    df_data[col_key] = {}
                                df_data[col_key][cell['row']] = cell['content']

                            # Create DataFrame
                            max_rows = max([max(col.keys()) for col in df_data.values()]) + 1 if df_data else 0
                            table_df = pd.DataFrame(index=range(max_rows))
                            for col, rows in df_data.items():
                                table_df[col] = [rows.get(i, '') for i in range(max_rows)]

                            st.dataframe(table_df, use_container_width=True)

                # Show key text
                if raw_data.get('paragraphs'):
                    st.write("**Key Text:**")
                    st.text_area("Extracted text", '\n'.join(raw_data['paragraphs'][:5]), height=200)
                elif raw_data.get('markdown_content'):
                    st.write("**Document Structure (Markdown):**")
                    content = raw_data['markdown_content']
                    if chunking_enabled:
                        content = content[:1500] + "...\n\n[Content from multiple chunks combined]" if len(content) > 1500 else content
                    else:
                        content = content[:1000] + "..." if len(content) > 1000 else content
                    st.text_area("Markdown content", content, height=200)

        with col3:
            st.subheader("Filtered Useful Data")
            if result.get('success'):
                extraction_data = result['result']

                # Show key information
                st.write("**Document Information:**")
                info_data = {
                    "Document": extraction_data.get('document_name', 'N/A'),
                    "Client": extraction_data.get('client_name', 'N/A'),
                    "Advisor": extraction_data.get('advisor_name', 'N/A'),
                    "Portfolio ID": extraction_data.get('portfolio_id', 'N/A'),
                    "Total Value": extraction_data.get('total_account_value', 'N/A'),
                    "Date": extraction_data.get('date_of_analysis', 'N/A')
                }

                for key, value in info_data.items():
                    st.write(f"**{key}:** {value}")

                # Show tabular data
                if extraction_data.get('tabular_data'):
                    st.write("**Portfolio Holdings:**")
                    holdings_df = pd.DataFrame(extraction_data['tabular_data'])
                    st.dataframe(holdings_df, use_container_width=True)
            else:
                st.error(" Extraction failed or required fields missing")
                if 'error' in result:
                    st.error(result['error'])

        # Results section
        st.header("Extraction Results")

        if result.get('success'):
            st.success("Extraction completed successfully!")

            # Display final JSON
            st.subheader("Final JSON Output")
            st.json(result['result'])

            # Download button
            json_str = json.dumps(result['result'], indent=2)
            st.download_button(
                label="Download JSON Result",
                data=json_str,
                file_name=f"{selected_file_name}_extraction.json",
                mime="application/json"
            )

        else:
            st.error(" Extraction failed")
            st.error(result.get('error', 'Unknown error'))

            # Show partial results if available
            if 'partial_result' in result:
                st.subheader("Partial Results")
                st.json(result['partial_result'])

            if 'raw_result' in result:
                st.subheader(" Raw Analysis Result")
                st.json(result['raw_result'])

else:
    st.info("Please select a file from the sidebar to begin extraction")

    # Show available sample files
    if sample_files:
        st.subheader("Available Sample Files")
        for file in sample_files:
            st.write(f"• {file}")


